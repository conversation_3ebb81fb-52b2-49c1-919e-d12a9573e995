{"version": 3, "sources": ["../../../src/config/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport chalk from 'chalk';\n\nimport { Command } from '../../bin/cli';\nimport { assertArgs, getProjectRoot, printHelp } from '../utils/args';\n\nexport const expoConfig: Command = async (argv) => {\n  const args = assertArgs(\n    {\n      // Types\n      '--help': Boolean,\n      '--full': <PERSON>olean,\n      '--json': Boolean,\n      '--type': String,\n      // Aliases\n      '-h': '--help',\n      '-t': '--type',\n    },\n    argv\n  );\n\n  if (args['--help']) {\n    printHelp(\n      `Show the project config`,\n      chalk`npx expo config {dim <dir>}`,\n      [\n        chalk`<dir>                                    Directory of the Expo project. {dim Default: Current working directory}`,\n        `--full                                   Include all project config data`,\n        `--json                                   Output in JSON format`,\n        `-t, --type <public|prebuild|introspect>  Type of config to show`,\n        `-h, --help                               Usage info`,\n      ].join('\\n')\n    );\n  }\n\n  // Load modules after the help prompt so `npx expo config -h` shows as fast as possible.\n  const [\n    // ./configAsync\n    { configAsync },\n    // ../utils/errors\n    { logCmdError },\n  ] = await Promise.all([import('./configAsync.js'), import('../utils/errors.js')]);\n\n  return configAsync(getProjectRoot(args), {\n    // Parsed options\n    full: args['--full'],\n    json: args['--json'],\n    type: args['--type'],\n  }).catch(logCmdError);\n};\n"], "names": ["expoConfig", "argv", "args", "assertArgs", "Boolean", "String", "printHelp", "chalk", "join", "config<PERSON><PERSON>", "logCmdError", "Promise", "all", "getProjectRoot", "full", "json", "type", "catch"], "mappings": ";;;;;+BAMaA;;;eAAAA;;;;gEALK;;;;;;sBAGoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE/C,MAAMA,aAAsB,OAAOC;IACxC,MAAMC,OAAOC,IAAAA,gBAAU,EACrB;QACE,QAAQ;QACR,UAAUC;QACV,UAAUA;QACV,UAAUA;QACV,UAAUC;QACV,UAAU;QACV,MAAM;QACN,MAAM;IACR,GACAJ;IAGF,IAAIC,IAAI,CAAC,SAAS,EAAE;QAClBI,IAAAA,eAAS,EACP,CAAC,uBAAuB,CAAC,EACzBC,IAAAA,gBAAK,CAAA,CAAC,2BAA2B,CAAC,EAClC;YACEA,IAAAA,gBAAK,CAAA,CAAC,gHAAgH,CAAC;YACvH,CAAC,wEAAwE,CAAC;YAC1E,CAAC,8DAA8D,CAAC;YAChE,CAAC,+DAA+D,CAAC;YACjE,CAAC,mDAAmD,CAAC;SACtD,CAACC,IAAI,CAAC;IAEX;IAEA,wFAAwF;IACxF,MAAM,CACJ,gBAAgB;IAChB,EAAEC,WAAW,EAAE,EACf,kBAAkB;IAClB,EAAEC,WAAW,EAAE,CAChB,GAAG,MAAMC,QAAQC,GAAG,CAAC;QAAC,mEAAA,QAAO;QAAqB,mEAAA,QAAO;KAAsB;IAEhF,OAAOH,YAAYI,IAAAA,oBAAc,EAACX,OAAO;QACvC,iBAAiB;QACjBY,MAAMZ,IAAI,CAAC,SAAS;QACpBa,MAAMb,IAAI,CAAC,SAAS;QACpBc,MAAMd,IAAI,CAAC,SAAS;IACtB,GAAGe,KAAK,CAACP;AACX"}
{"version": 3, "sources": ["../../../src/export/exportAssets.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport fs from 'fs';\nimport { minimatch } from 'minimatch';\nimport path from 'path';\n\nimport { getAssetIdForLogGrouping, persistMetroAssetsAsync } from './persistMetroAssets';\nimport type { Asset, BundleAssetWithFileHashes, BundleOutput, ExportAssetMap } from './saveAssets';\nimport * as Log from '../log';\nimport { resolveGoogleServicesFile } from '../start/server/middleware/resolveAssets';\nimport { uniqBy } from '../utils/array';\n\nconst debug = require('debug')('expo:export:exportAssets') as typeof console.log;\n\nfunction mapAssetHashToAssetString(asset: Asset, hash: string) {\n  return 'asset_' + hash + ('type' in asset && asset.type ? '.' + asset.type : '');\n}\n\nexport function assetPatternsToBeBundled(\n  exp: ExpoConfig & { extra?: { updates?: { assetPatternsToBeBundled?: string[] } } }\n): string[] | undefined {\n  // new location for this key\n  if (exp.updates?.assetPatternsToBeBundled?.length) {\n    return exp.updates.assetPatternsToBeBundled;\n  }\n\n  // old, untyped location for this key. we may want to change this to throw in a few SDK versions (deprecated as of SDK 52).\n  if (exp.extra?.updates?.assetPatternsToBeBundled?.length) {\n    return exp.extra.updates.assetPatternsToBeBundled;\n  }\n\n  return undefined;\n}\n\n/**\n * Given an asset and a set of strings representing the assets to be bundled, returns true if\n * the asset is part of the set to be bundled.\n * @param asset Asset object\n * @param bundledAssetsSet Set of strings\n * @returns true if the asset should be bundled\n */\nfunction assetShouldBeIncludedInExport(asset: Asset, bundledAssetsSet: Set<string> | undefined) {\n  if (!bundledAssetsSet) {\n    return true;\n  }\n  return (\n    asset.fileHashes.filter((hash) => bundledAssetsSet.has(mapAssetHashToAssetString(asset, hash)))\n      .length > 0\n  );\n}\n\n/**\n * Computes a set of strings representing the assets to be bundled with an export, given an array of assets,\n * and a set of patterns to match\n * @param assets The asset array\n * @param assetPatternsToBeBundled An array of strings with glob patterns to match\n * @param projectRoot The project root\n * @returns A set of asset strings\n */\nfunction setOfAssetsToBeBundled(\n  assets: Asset[],\n  assetPatternsToBeBundled: string[],\n  projectRoot: string\n): Set<string> | undefined {\n  // Convert asset patterns to a list of asset strings that match them.\n  // Assets strings are formatted as `asset_<hash>.<type>` and represent\n  // the name that the file will have in the app bundle. The `asset_` prefix is\n  // needed because android doesn't support assets that start with numbers.\n\n  const fullPatterns: string[] = assetPatternsToBeBundled.map((p: string) =>\n    path.join(projectRoot, p)\n  );\n\n  logPatterns(fullPatterns);\n\n  const allBundledAssets = assets\n    .map((asset) => {\n      const shouldBundle = shouldBundleAsset(asset, fullPatterns);\n      if (shouldBundle) {\n        debug(`${shouldBundle ? 'Include' : 'Exclude'} asset ${asset.files?.[0]}`);\n        return asset.fileHashes.map((hash) => mapAssetHashToAssetString(asset, hash));\n      }\n      return [];\n    })\n    .flat();\n\n  // The assets returned by the RN packager has duplicates so make sure we\n  // only bundle each once.\n  return new Set(allBundledAssets);\n}\n\n/**\n * Resolves the assetBundlePatterns from the manifest and returns the set of assets to bundle.\n *\n * @modifies {exp}\n */\nexport function resolveAssetPatternsToBeBundled<T extends ExpoConfig>(\n  projectRoot: string,\n  exp: T,\n  assets: Asset[]\n): Set<string> | undefined {\n  const assetPatternsToBeBundledForConfig = assetPatternsToBeBundled(exp);\n  if (!assetPatternsToBeBundledForConfig) {\n    return undefined;\n  }\n  const bundledAssets = setOfAssetsToBeBundled(\n    assets,\n    assetPatternsToBeBundledForConfig,\n    projectRoot\n  );\n  return bundledAssets;\n}\n\nfunction logPatterns(patterns: string[]) {\n  // Only log the patterns in debug mode, if they aren't already defined in the app.json, then all files will be targeted.\n  Log.log('\\nProcessing asset bundle patterns:');\n  patterns.forEach((p) => Log.log('- ' + p));\n}\n\nfunction shouldBundleAsset(asset: Asset, patterns: string[]) {\n  const file = asset.files?.[0];\n  return !!(\n    '__packager_asset' in asset &&\n    asset.__packager_asset &&\n    file &&\n    patterns.some((pattern) => minimatch(file, pattern))\n  );\n}\n\nexport async function exportAssetsAsync(\n  projectRoot: string,\n  {\n    exp,\n    outputDir,\n    bundles: { web, ...bundles },\n    baseUrl,\n    files = new Map(),\n  }: {\n    exp: ExpoConfig;\n    bundles: Partial<Record<string, BundleOutput>>;\n    outputDir: string;\n    baseUrl: string;\n    files?: ExportAssetMap;\n  }\n) {\n  // NOTE: We use a different system for static web\n  if (web) {\n    // Save assets like a typical bundler, preserving the file paths on web.\n    // TODO: Update React Native Web to support loading files from asset hashes.\n    await persistMetroAssetsAsync(projectRoot, web.assets, {\n      files,\n      platform: 'web',\n      outputDirectory: outputDir,\n      baseUrl,\n    });\n  }\n\n  const assets: BundleAssetWithFileHashes[] = uniqBy(\n    Object.values(bundles).flatMap((bundle) => bundle!.assets),\n    (asset) => asset.hash\n  );\n\n  let bundledAssetsSet: Set<string> | undefined = undefined;\n  let filteredAssets = assets;\n  const embeddedHashSet: Set<string> = new Set();\n\n  if (assets[0]?.fileHashes) {\n    debug(`Assets = ${JSON.stringify(assets, null, 2)}`);\n    // Updates the manifest to reflect additional asset bundling + configs\n    // Get only asset strings for assets we will save\n    bundledAssetsSet = resolveAssetPatternsToBeBundled(projectRoot, exp, assets);\n    if (bundledAssetsSet) {\n      debug(`Bundled assets = ${JSON.stringify([...bundledAssetsSet], null, 2)}`);\n      // Filter asset objects to only ones that include assetPatternsToBeBundled matches\n      filteredAssets = assets.filter((asset) => {\n        const shouldInclude = assetShouldBeIncludedInExport(asset, bundledAssetsSet);\n        if (!shouldInclude) {\n          embeddedHashSet.add(asset.hash);\n        }\n        return shouldInclude;\n      });\n      debug(`Filtered assets count = ${filteredAssets.length}`);\n    }\n\n    const hashes = new Set<string>();\n\n    // Add assets to copy.\n    filteredAssets.forEach((asset) => {\n      const assetId = getAssetIdForLogGrouping(projectRoot, asset);\n\n      asset.files.forEach((fp: string, index: number) => {\n        const hash = asset.fileHashes[index];\n        if (hashes.has(hash)) return;\n        hashes.add(hash);\n        files.set(path.join('assets', hash), {\n          originFilename: path.relative(projectRoot, fp),\n          contents: fs.readFileSync(fp),\n          assetId,\n        });\n      });\n    });\n  }\n\n  // Add google services file if it exists\n  await resolveGoogleServicesFile(projectRoot, exp);\n\n  return { exp, assets, embeddedHashSet, files };\n}\n"], "names": ["assetPatternsToBeBundled", "exportAssetsAsync", "resolveAssetPatternsToBeBundled", "debug", "require", "mapAssetHashToAssetString", "asset", "hash", "type", "exp", "updates", "length", "extra", "undefined", "assetShouldBeIncludedInExport", "bundledAssetsSet", "fileHashes", "filter", "has", "setOfAssetsToBeBundled", "assets", "projectRoot", "fullPatterns", "map", "p", "path", "join", "logPatterns", "allBundledAssets", "shouldBundle", "shouldBundleAsset", "files", "flat", "Set", "assetPatternsToBeBundledForConfig", "bundledAssets", "patterns", "Log", "log", "for<PERSON>ach", "file", "__packager_asset", "some", "pattern", "minimatch", "outputDir", "bundles", "web", "baseUrl", "Map", "persistMetroAssetsAsync", "platform", "outputDirectory", "uniqBy", "Object", "values", "flatMap", "bundle", "filteredAssets", "embeddedHashSet", "JSON", "stringify", "shouldInclude", "add", "hashes", "assetId", "getAssetIdForLogGrouping", "fp", "index", "set", "originFilename", "relative", "contents", "fs", "readFileSync", "resolveGoogleServicesFile"], "mappings": ";;;;;;;;;;;IAiBgBA,wBAAwB;eAAxBA;;IA+GMC,iBAAiB;eAAjBA;;IAjCNC,+BAA+B;eAA/BA;;;;gEA9FD;;;;;;;yBACW;;;;;;;gEACT;;;;;;oCAEiD;6DAE7C;+BACqB;uBACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEvB,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,SAASC,0BAA0BC,KAAY,EAAEC,IAAY;IAC3D,OAAO,WAAWA,OAAQ,CAAA,UAAUD,SAASA,MAAME,IAAI,GAAG,MAAMF,MAAME,IAAI,GAAG,EAAC;AAChF;AAEO,SAASR,yBACdS,GAAmF;QAG/EA,uCAAAA,cAKAA,6CAAAA,oBAAAA;IANJ,4BAA4B;IAC5B,KAAIA,eAAAA,IAAIC,OAAO,sBAAXD,wCAAAA,aAAaT,wBAAwB,qBAArCS,sCAAuCE,MAAM,EAAE;QACjD,OAAOF,IAAIC,OAAO,CAACV,wBAAwB;IAC7C;IAEA,2HAA2H;IAC3H,KAAIS,aAAAA,IAAIG,KAAK,sBAATH,qBAAAA,WAAWC,OAAO,sBAAlBD,8CAAAA,mBAAoBT,wBAAwB,qBAA5CS,4CAA8CE,MAAM,EAAE;QACxD,OAAOF,IAAIG,KAAK,CAACF,OAAO,CAACV,wBAAwB;IACnD;IAEA,OAAOa;AACT;AAEA;;;;;;CAMC,GACD,SAASC,8BAA8BR,KAAY,EAAES,gBAAyC;IAC5F,IAAI,CAACA,kBAAkB;QACrB,OAAO;IACT;IACA,OACET,MAAMU,UAAU,CAACC,MAAM,CAAC,CAACV,OAASQ,iBAAiBG,GAAG,CAACb,0BAA0BC,OAAOC,QACrFI,MAAM,GAAG;AAEhB;AAEA;;;;;;;CAOC,GACD,SAASQ,uBACPC,MAAe,EACfpB,wBAAkC,EAClCqB,WAAmB;IAEnB,qEAAqE;IACrE,sEAAsE;IACtE,6EAA6E;IAC7E,yEAAyE;IAEzE,MAAMC,eAAyBtB,yBAAyBuB,GAAG,CAAC,CAACC,IAC3DC,eAAI,CAACC,IAAI,CAACL,aAAaG;IAGzBG,YAAYL;IAEZ,MAAMM,mBAAmBR,OACtBG,GAAG,CAAC,CAACjB;QACJ,MAAMuB,eAAeC,kBAAkBxB,OAAOgB;QAC9C,IAAIO,cAAc;gBACuCvB;YAAvDH,MAAM,GAAG0B,eAAe,YAAY,UAAU,OAAO,GAAEvB,eAAAA,MAAMyB,KAAK,qBAAXzB,YAAa,CAAC,EAAE,EAAE;YACzE,OAAOA,MAAMU,UAAU,CAACO,GAAG,CAAC,CAAChB,OAASF,0BAA0BC,OAAOC;QACzE;QACA,OAAO,EAAE;IACX,GACCyB,IAAI;IAEP,wEAAwE;IACxE,yBAAyB;IACzB,OAAO,IAAIC,IAAIL;AACjB;AAOO,SAAS1B,gCACdmB,WAAmB,EACnBZ,GAAM,EACNW,MAAe;IAEf,MAAMc,oCAAoClC,yBAAyBS;IACnE,IAAI,CAACyB,mCAAmC;QACtC,OAAOrB;IACT;IACA,MAAMsB,gBAAgBhB,uBACpBC,QACAc,mCACAb;IAEF,OAAOc;AACT;AAEA,SAASR,YAAYS,QAAkB;IACrC,wHAAwH;IACxHC,KAAIC,GAAG,CAAC;IACRF,SAASG,OAAO,CAAC,CAACf,IAAMa,KAAIC,GAAG,CAAC,OAAOd;AACzC;AAEA,SAASM,kBAAkBxB,KAAY,EAAE8B,QAAkB;QAC5C9B;IAAb,MAAMkC,QAAOlC,eAAAA,MAAMyB,KAAK,qBAAXzB,YAAa,CAAC,EAAE;IAC7B,OAAO,CAAC,CACN,CAAA,sBAAsBA,SACtBA,MAAMmC,gBAAgB,IACtBD,QACAJ,SAASM,IAAI,CAAC,CAACC,UAAYC,IAAAA,sBAAS,EAACJ,MAAMG,SAAQ;AAEvD;AAEO,eAAe1C,kBACpBoB,WAAmB,EACnB,EACEZ,GAAG,EACHoC,SAAS,EACTC,SAAS,EAAEC,GAAG,EAAE,GAAGD,SAAS,EAC5BE,OAAO,EACPjB,QAAQ,IAAIkB,KAAK,EAOlB;QAuBG7B;IArBJ,iDAAiD;IACjD,IAAI2B,KAAK;QACP,wEAAwE;QACxE,4EAA4E;QAC5E,MAAMG,IAAAA,2CAAuB,EAAC7B,aAAa0B,IAAI3B,MAAM,EAAE;YACrDW;YACAoB,UAAU;YACVC,iBAAiBP;YACjBG;QACF;IACF;IAEA,MAAM5B,SAAsCiC,IAAAA,aAAM,EAChDC,OAAOC,MAAM,CAACT,SAASU,OAAO,CAAC,CAACC,SAAWA,OAAQrC,MAAM,GACzD,CAACd,QAAUA,MAAMC,IAAI;IAGvB,IAAIQ,mBAA4CF;IAChD,IAAI6C,iBAAiBtC;IACrB,MAAMuC,kBAA+B,IAAI1B;IAEzC,KAAIb,WAAAA,MAAM,CAAC,EAAE,qBAATA,SAAWJ,UAAU,EAAE;QACzBb,MAAM,CAAC,SAAS,EAAEyD,KAAKC,SAAS,CAACzC,QAAQ,MAAM,IAAI;QACnD,sEAAsE;QACtE,iDAAiD;QACjDL,mBAAmBb,gCAAgCmB,aAAaZ,KAAKW;QACrE,IAAIL,kBAAkB;YACpBZ,MAAM,CAAC,iBAAiB,EAAEyD,KAAKC,SAAS,CAAC;mBAAI9C;aAAiB,EAAE,MAAM,IAAI;YAC1E,kFAAkF;YAClF2C,iBAAiBtC,OAAOH,MAAM,CAAC,CAACX;gBAC9B,MAAMwD,gBAAgBhD,8BAA8BR,OAAOS;gBAC3D,IAAI,CAAC+C,eAAe;oBAClBH,gBAAgBI,GAAG,CAACzD,MAAMC,IAAI;gBAChC;gBACA,OAAOuD;YACT;YACA3D,MAAM,CAAC,wBAAwB,EAAEuD,eAAe/C,MAAM,EAAE;QAC1D;QAEA,MAAMqD,SAAS,IAAI/B;QAEnB,sBAAsB;QACtByB,eAAenB,OAAO,CAAC,CAACjC;YACtB,MAAM2D,UAAUC,IAAAA,4CAAwB,EAAC7C,aAAaf;YAEtDA,MAAMyB,KAAK,CAACQ,OAAO,CAAC,CAAC4B,IAAYC;gBAC/B,MAAM7D,OAAOD,MAAMU,UAAU,CAACoD,MAAM;gBACpC,IAAIJ,OAAO9C,GAAG,CAACX,OAAO;gBACtByD,OAAOD,GAAG,CAACxD;gBACXwB,MAAMsC,GAAG,CAAC5C,eAAI,CAACC,IAAI,CAAC,UAAUnB,OAAO;oBACnC+D,gBAAgB7C,eAAI,CAAC8C,QAAQ,CAAClD,aAAa8C;oBAC3CK,UAAUC,aAAE,CAACC,YAAY,CAACP;oBAC1BF;gBACF;YACF;QACF;IACF;IAEA,wCAAwC;IACxC,MAAMU,IAAAA,wCAAyB,EAACtD,aAAaZ;IAE7C,OAAO;QAAEA;QAAKW;QAAQuC;QAAiB5B;IAAM;AAC/C"}
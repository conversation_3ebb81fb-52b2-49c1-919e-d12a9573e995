{"version": 3, "sources": ["../../../src/install/resolveOptions.ts"], "sourcesContent": ["import { NodePackageManagerForProject } from '@expo/package-manager';\n\nimport { CommandError } from '../utils/errors';\nimport { assertUnexpectedVariadicFlags, parseVariadicArguments } from '../utils/variadic';\n\nexport type Options = Pick<NodePackageManagerForProject, 'npm' | 'pnpm' | 'yarn' | 'bun'> & {\n  /** Check which packages need to be updated, does not install any provided packages. */\n  check?: boolean;\n  /** Should the dependencies be fixed automatically. */\n  fix?: boolean;\n  /** Should disable install output, used for commands like `prebuild` that run install internally. */\n  silent?: boolean;\n  /** Should be installed as dev dependencies */\n  dev?: boolean;\n};\n\nfunction resolveOptions(options: Options): Options {\n  if (options.fix && options.check) {\n    throw new CommandError('BAD_ARGS', 'Specify at most one of: --check, --fix');\n  }\n  if ([options.npm, options.pnpm, options.yarn, options.bun].filter(Boolean).length > 1) {\n    throw new CommandError('BAD_ARGS', 'Specify at most one of: --npm, --pnpm, --yarn, --bun');\n  }\n  return {\n    ...options,\n  };\n}\n\nexport async function resolveArgsAsync(\n  argv: string[]\n): Promise<{ variadic: string[]; options: Options; extras: string[] }> {\n  const { variadic, extras, flags } = parseVariadicArguments(argv);\n\n  assertUnexpectedVariadicFlags(\n    ['--check', '--dev', '--fix', '--npm', '--pnpm', '--yarn', '--bun'],\n    { variadic, extras, flags },\n    'npx expo install'\n  );\n\n  return {\n    // Variadic arguments like `npx expo install react react-dom` -> ['react', 'react-dom']\n    variadic,\n    options: resolveOptions({\n      fix: !!flags['--fix'],\n      dev: !!flags['--dev'],\n      check: !!flags['--check'],\n      yarn: !!flags['--yarn'],\n      npm: !!flags['--npm'],\n      pnpm: !!flags['--pnpm'],\n      bun: !!flags['--bun'],\n    }),\n    extras,\n  };\n}\n"], "names": ["resolveArgsAsync", "resolveOptions", "options", "fix", "check", "CommandError", "npm", "pnpm", "yarn", "bun", "filter", "Boolean", "length", "argv", "variadic", "extras", "flags", "parseVariadicArguments", "assertUnexpectedVariadicFlags", "dev"], "mappings": ";;;;+BA4BsBA;;;eAAAA;;;wBA1BO;0BACyC;AAatE,SAASC,eAAeC,OAAgB;IACtC,IAAIA,QAAQC,GAAG,IAAID,QAAQE,KAAK,EAAE;QAChC,MAAM,IAAIC,oBAAY,CAAC,YAAY;IACrC;IACA,IAAI;QAACH,QAAQI,GAAG;QAAEJ,QAAQK,IAAI;QAAEL,QAAQM,IAAI;QAAEN,QAAQO,GAAG;KAAC,CAACC,MAAM,CAACC,SAASC,MAAM,GAAG,GAAG;QACrF,MAAM,IAAIP,oBAAY,CAAC,YAAY;IACrC;IACA,OAAO;QACL,GAAGH,OAAO;IACZ;AACF;AAEO,eAAeF,iBACpBa,IAAc;IAEd,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAE,GAAGC,IAAAA,gCAAsB,EAACJ;IAE3DK,IAAAA,uCAA6B,EAC3B;QAAC;QAAW;QAAS;QAAS;QAAS;QAAU;QAAU;KAAQ,EACnE;QAAEJ;QAAUC;QAAQC;IAAM,GAC1B;IAGF,OAAO;QACL,uFAAuF;QACvFF;QACAZ,SAASD,eAAe;YACtBE,KAAK,CAAC,CAACa,KAAK,CAAC,QAAQ;YACrBG,KAAK,CAAC,CAACH,KAAK,CAAC,QAAQ;YACrBZ,OAAO,CAAC,CAACY,KAAK,CAAC,UAAU;YACzBR,MAAM,CAAC,CAACQ,KAAK,CAAC,SAAS;YACvBV,KAAK,CAAC,CAACU,KAAK,CAAC,QAAQ;YACrBT,MAAM,CAAC,CAACS,KAAK,CAAC,SAAS;YACvBP,KAAK,CAAC,CAACO,KAAK,CAAC,QAAQ;QACvB;QACAD;IACF;AACF"}
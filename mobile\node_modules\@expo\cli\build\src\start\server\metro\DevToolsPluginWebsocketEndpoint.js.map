{"version": 3, "sources": ["../../../../../src/start/server/metro/DevToolsPluginWebsocketEndpoint.ts"], "sourcesContent": ["import { WebSocket, WebSocketServer } from 'ws';\n\nexport function createDevToolsPluginWebsocketEndpoint(): Record<string, WebSocketServer> {\n  const wss = new WebSocketServer({ noServer: true });\n\n  wss.on('connection', (ws: WebSocket) => {\n    ws.on('message', (message, isBinary) => {\n      // Broadcast the received message to all other connected clients\n      wss.clients.forEach((client) => {\n        if (client !== ws && client.readyState === WebSocket.OPEN) {\n          client.send(message, { binary: isBinary });\n        }\n      });\n    });\n  });\n\n  return { '/expo-dev-plugins/broadcast': wss };\n}\n"], "names": ["createDevToolsPluginWebsocketEndpoint", "wss", "WebSocketServer", "noServer", "on", "ws", "message", "isBinary", "clients", "for<PERSON>ach", "client", "readyState", "WebSocket", "OPEN", "send", "binary"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;;yBAF2B;;;;;;AAEpC,SAASA;IACd,MAAMC,MAAM,IAAIC,CAAAA,KAAc,iBAAC,CAAC;QAAEC,UAAU;IAAK;IAEjDF,IAAIG,EAAE,CAAC,cAAc,CAACC;QACpBA,GAAGD,EAAE,CAAC,WAAW,CAACE,SAASC;YACzB,gEAAgE;YAChEN,IAAIO,OAAO,CAACC,OAAO,CAAC,CAACC;gBACnB,IAAIA,WAAWL,MAAMK,OAAOC,UAAU,KAAKC,eAAS,CAACC,IAAI,EAAE;oBACzDH,OAAOI,IAAI,CAACR,SAAS;wBAAES,QAAQR;oBAAS;gBAC1C;YACF;QACF;IACF;IAEA,OAAO;QAAE,+BAA+BN;IAAI;AAC9C"}
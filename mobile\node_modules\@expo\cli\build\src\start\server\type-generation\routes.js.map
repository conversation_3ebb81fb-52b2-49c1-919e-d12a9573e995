{"version": 3, "sources": ["../../../../../src/start/server/type-generation/routes.ts"], "sourcesContent": ["import fs from 'fs/promises';\nimport { Server } from 'metro';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { directoryExistsAsync } from '../../../utils/dir';\nimport { unsafeTemplate } from '../../../utils/template';\nimport { ServerLike } from '../BundlerDevServer';\nimport { metroWatchTypeScriptFiles } from '../metro/metroWatchTypeScriptFiles';\n\n// /test/[...param1]/[param2]/[param3] - captures [\"param1\", \"param2\", \"param3\"]\nexport const CAPTURE_DYNAMIC_PARAMS = /\\[(?:\\.{3})?(\\w*?)[\\]$]/g;\n// /[...param1]/ - Match [...param1]\nexport const CATCH_ALL = /\\[\\.\\.\\..+?\\]/g;\n// /[param1] - Match [param1]\nexport const SLUG = /\\[.+?\\]/g;\n// /(group1,group2,group3)/test - match (group1,group2,group3)\nexport const ARRAY_GROUP_REGEX = /\\(\\s*\\w[\\w\\s]*?,.*?\\)/g;\n// /(group1,group2,group3)/test - captures [\"group1\", \"group2\", \"group3\"]\nexport const CAPTURE_GROUP_REGEX = /[\\\\(,]\\s*(\\w[\\w\\s]*?)\\s*(?=[,\\\\)])/g;\n/**\n * Match:\n *  - _layout files, +html, +not-found, string+api, etc\n *  - Routes can still use `+`, but it cannot be in the last segment.\n */\nexport const TYPED_ROUTES_EXCLUSION_REGEX = /(_layout|[^/]*?\\+[^/]*?)\\.[tj]sx?$/;\n\nexport interface SetupTypedRoutesOptions {\n  server?: ServerLike;\n  metro?: Server | null;\n  typesDirectory: string;\n  projectRoot: string;\n  /** Absolute expo router routes directory. */\n  routerDirectory: string;\n  plugin?: Record<string, any>;\n}\n\nexport async function setupTypedRoutes(options: SetupTypedRoutesOptions) {\n  /*\n   * In SDK 51, TypedRoutes was moved out of cli and into expo-router. For now we need to support both\n   * the legacy and new versions of TypedRoutes.\n   *\n   * TODO (@marklawlor): Remove this check in SDK 53, only support Expo Router v4 and above.\n   */\n  const typedRoutesModule = resolveFrom.silent(\n    options.projectRoot,\n    'expo-router/build/typed-routes'\n  );\n  return typedRoutesModule ? typedRoutes(typedRoutesModule, options) : legacyTypedRoutes(options);\n}\n\nasync function typedRoutes(\n  typedRoutesModulePath: any,\n  { server, metro, typesDirectory, projectRoot, routerDirectory, plugin }: SetupTypedRoutesOptions\n) {\n  /*\n   * Expo Router uses EXPO_ROUTER_APP_ROOT in multiple places to determine the root of the project.\n   * In apps compiled by Metro, this code is compiled away. But Typed Routes run in NodeJS with no compilation\n   * so we need to explicitly set it.\n   */\n  process.env.EXPO_ROUTER_APP_ROOT = routerDirectory;\n\n  const typedRoutesModule = require(typedRoutesModulePath);\n\n  /*\n   * Typed Routes can be run with out Metro or a Server, e.g. `expo customize tsconfig.json`\n   */\n  if (metro && server) {\n    // Setup out watcher first\n    metroWatchTypeScriptFiles({\n      projectRoot,\n      server,\n      metro,\n      eventTypes: ['add', 'delete', 'change'],\n      callback: typedRoutesModule.getWatchHandler(typesDirectory),\n    });\n  }\n\n  /*\n   * In SDK 52, the `regenerateDeclarations` was changed to accept plugin options.\n   * This function has an optional parameter that we cannot override, so we need to ensure the user\n   * is using a compatible version of `expo-router`. Otherwise, we will fallback to the old method.\n   *\n   * TODO(@marklawlor): In SDK53+ we should remove this check and always use the new method.\n   */\n  if ('version' in typedRoutesModule && typedRoutesModule.version >= 52) {\n    typedRoutesModule.regenerateDeclarations(typesDirectory, plugin);\n  } else {\n    typedRoutesModule.regenerateDeclarations(typesDirectory);\n  }\n}\n\nasync function legacyTypedRoutes({\n  server,\n  metro,\n  typesDirectory,\n  projectRoot,\n  routerDirectory,\n}: SetupTypedRoutesOptions) {\n  const { filePathToRoute, staticRoutes, dynamicRoutes, addFilePath, isRouteFile } =\n    getTypedRoutesUtils(routerDirectory);\n\n  // Typed Routes can be run with out Metro or a Server, e.g. `expo customize tsconfig.json`\n  if (metro && server) {\n    metroWatchTypeScriptFiles({\n      projectRoot,\n      server,\n      metro,\n      eventTypes: ['add', 'delete', 'change'],\n      async callback({ filePath, type }) {\n        if (!isRouteFile(filePath)) {\n          return;\n        }\n\n        let shouldRegenerate = false;\n\n        if (type === 'delete') {\n          const route = filePathToRoute(filePath);\n          staticRoutes.delete(route);\n          dynamicRoutes.delete(route);\n          shouldRegenerate = true;\n        } else {\n          shouldRegenerate = addFilePath(filePath);\n        }\n\n        if (shouldRegenerate) {\n          regenerateRouterDotTS(\n            typesDirectory,\n            new Set([...staticRoutes.values()].flatMap((v) => Array.from(v))),\n            new Set([...dynamicRoutes.values()].flatMap((v) => Array.from(v))),\n            new Set(dynamicRoutes.keys())\n          );\n        }\n      },\n    });\n  }\n\n  if (await directoryExistsAsync(routerDirectory)) {\n    // Do we need to walk the entire tree on startup?\n    // Idea: Store the list of files in the last write, then simply check Git for what files have changed\n    await walk(routerDirectory, addFilePath);\n  }\n\n  regenerateRouterDotTS(\n    typesDirectory,\n    new Set([...staticRoutes.values()].flatMap((v) => Array.from(v))),\n    new Set([...dynamicRoutes.values()].flatMap((v) => Array.from(v))),\n    new Set(dynamicRoutes.keys())\n  );\n}\n\nfunction debounce<U, T extends (this: U, ...args: any[]) => void>(fn: T, delay: number): T {\n  let timeoutId: NodeJS.Timeout | undefined;\n  return function (this: U, ...args: any[]) {\n    clearTimeout(timeoutId);\n    timeoutId = setTimeout(() => fn.apply(this, args), delay);\n  } as T;\n}\n\n/**\n * Generate a router.d.ts file that contains all of the routes in the project.\n * Should be debounced as its very common for developers to make changes to multiple files at once (eg Save All)\n */\nconst regenerateRouterDotTS = debounce(\n  async (\n    typesDir: string,\n    staticRoutes: Set<string>,\n    dynamicRoutes: Set<string>,\n    dynamicRouteTemplates: Set<string>\n  ) => {\n    await fs.mkdir(typesDir, { recursive: true });\n    await fs.writeFile(\n      path.resolve(typesDir, './router.d.ts'),\n      getTemplateString(staticRoutes, dynamicRoutes, dynamicRouteTemplates)\n    );\n  },\n  100\n);\n\n/*\n * This is exported for testing purposes\n */\nexport function getTemplateString(\n  staticRoutes: Set<string>,\n  dynamicRoutes: Set<string>,\n  dynamicRouteTemplates: Set<string>\n) {\n  return routerDotTSTemplate({\n    staticRoutes: setToUnionType(staticRoutes),\n    dynamicRoutes: setToUnionType(dynamicRoutes),\n    dynamicRouteParams: setToUnionType(dynamicRouteTemplates),\n  });\n}\n\n/**\n * Utility functions for typed routes\n *\n * These are extracted for easier testing\n */\nexport function getTypedRoutesUtils(appRoot: string, filePathSeperator = path.sep) {\n  /*\n   * staticRoutes are a map where the key if the route without groups and the value\n   *   is another set of all group versions of the route. e.g,\n   *    Map([\n   *      [\"/\", [\"/(app)/(notes)\", \"/(app)/(profile)\"]\n   *    ])\n   */\n  const staticRoutes = new Map<string, Set<string>>([['/', new Set('/')]]);\n  /*\n   * dynamicRoutes are the same as staticRoutes (key if the resolved route,\n   *   and the value is a set of possible routes). e.g:\n   *\n   * /[...fruits] -> /${CatchAllRoutePart<T>}\n   * /color/[color] -> /color/${SingleRoutePart<T>}\n   *\n   * The keys of this map are also important, as they can be used as \"static\" types\n   * <Link href={{ pathname: \"/[...fruits]\",params: { fruits: [\"apple\"] } }} />\n   */\n  const dynamicRoutes = new Map<string, Set<string>>();\n\n  function normalizedFilePath(filePath: string) {\n    return filePath.replaceAll(filePathSeperator, '/');\n  }\n\n  const normalizedAppRoot = normalizedFilePath(appRoot);\n\n  const filePathToRoute = (filePath: string) => {\n    return normalizedFilePath(filePath)\n      .replace(normalizedAppRoot, '')\n      .replace(/index\\.[jt]sx?/, '')\n      .replace(/\\.[jt]sx?$/, '');\n  };\n\n  const isRouteFile = (filePath: string) => {\n    if (filePath.match(TYPED_ROUTES_EXCLUSION_REGEX)) {\n      return false;\n    }\n\n    // Route files must be nested with in the appRoot\n    const relative = path.relative(appRoot, filePath);\n    return relative && !relative.startsWith('..') && !path.isAbsolute(relative);\n  };\n\n  const addFilePath = (filePath: string): boolean => {\n    if (!isRouteFile(filePath)) {\n      return false;\n    }\n\n    const route = filePathToRoute(filePath);\n\n    // We have already processed this file\n    if (staticRoutes.has(route) || dynamicRoutes.has(route)) {\n      return false;\n    }\n\n    const dynamicParams = new Set(\n      [...route.matchAll(CAPTURE_DYNAMIC_PARAMS)].map((match) => match[1])\n    );\n    const isDynamic = dynamicParams.size > 0;\n\n    const addRoute = (originalRoute: string, route: string) => {\n      if (isDynamic) {\n        let set = dynamicRoutes.get(originalRoute);\n\n        if (!set) {\n          set = new Set();\n          dynamicRoutes.set(originalRoute, set);\n        }\n\n        set.add(\n          route\n            .replaceAll(CATCH_ALL, '${CatchAllRoutePart<T>}')\n            .replaceAll(SLUG, '${SingleRoutePart<T>}')\n        );\n      } else {\n        let set = staticRoutes.get(originalRoute);\n\n        if (!set) {\n          set = new Set();\n          staticRoutes.set(originalRoute, set);\n        }\n\n        set.add(route);\n      }\n    };\n\n    if (!route.match(ARRAY_GROUP_REGEX)) {\n      addRoute(route, route);\n    }\n\n    // Does this route have a group? eg /(group)\n    if (route.includes('/(')) {\n      const routeWithoutGroups = route.replace(/\\/\\(.+?\\)/g, '');\n      addRoute(route, routeWithoutGroups);\n\n      // If there are multiple groups, we need to expand them\n      // eg /(test1,test2)/page => /test1/page & /test2/page\n      for (const routeWithSingleGroup of extrapolateGroupRoutes(route)) {\n        addRoute(route, routeWithSingleGroup);\n      }\n    }\n\n    return true;\n  };\n\n  return {\n    staticRoutes,\n    dynamicRoutes,\n    filePathToRoute,\n    addFilePath,\n    isRouteFile,\n  };\n}\n\nexport const setToUnionType = <T>(set: Set<T>) => {\n  return set.size > 0 ? [...set].map((s) => `\\`${s}\\``).join(' | ') : 'never';\n};\n\n/**\n * Recursively walk a directory and call the callback with the file path.\n */\nasync function walk(directory: string, callback: (filePath: string) => void) {\n  const files = await fs.readdir(directory);\n  for (const file of files) {\n    const p = path.join(directory, file);\n    if ((await fs.stat(p)).isDirectory()) {\n      await walk(p, callback);\n    } else {\n      // Normalise the paths so they are easier to convert to URLs\n      const normalizedPath = p.replaceAll(path.sep, '/');\n      callback(normalizedPath);\n    }\n  }\n}\n\n/**\n * Given a route, return all possible routes that could be generated from it.\n */\nexport function extrapolateGroupRoutes(\n  route: string,\n  routes: Set<string> = new Set()\n): Set<string> {\n  // Create a version with no groups. We will then need to cleanup double and/or trailing slashes\n  routes.add(route.replaceAll(ARRAY_GROUP_REGEX, '').replaceAll(/\\/+/g, '/').replace(/\\/$/, ''));\n\n  const match = route.match(ARRAY_GROUP_REGEX);\n\n  if (!match) {\n    routes.add(route);\n    return routes;\n  }\n\n  const groupsMatch = match[0];\n\n  for (const group of groupsMatch.matchAll(CAPTURE_GROUP_REGEX)) {\n    extrapolateGroupRoutes(route.replace(groupsMatch, `(${group[1].trim()})`), routes);\n  }\n\n  return routes;\n}\n\n/**\n * NOTE: This code refers to a specific version of `expo-router` and is therefore unsafe to\n * mix with arbitrary versions.\n * TODO: Version this code with `expo-router` or version expo-router with `@expo/cli`.\n */\nconst routerDotTSTemplate = unsafeTemplate`/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable import/export */\n/* eslint-disable @typescript-eslint/ban-types */\ndeclare module \"expo-router\" {\n  import type { LinkProps as OriginalLinkProps } from 'expo-router/build/link/Link';\n  import type { Router as OriginalRouter } from 'expo-router/build/types';\n  export * from 'expo-router/build';\n\n  // prettier-ignore\n  type StaticRoutes = ${'staticRoutes'};\n  // prettier-ignore\n  type DynamicRoutes<T extends string> = ${'dynamicRoutes'};\n  // prettier-ignore\n  type DynamicRouteTemplate = ${'dynamicRouteParams'};\n\n  type RelativePathString = \\`./\\${string}\\` | \\`../\\${string}\\` | '..';\n  type AbsoluteRoute = DynamicRouteTemplate | StaticRoutes;\n  type ExternalPathString = \\`\\${string}:\\${string}\\`;\n\n  type ExpoRouterRoutes = DynamicRouteTemplate | StaticRoutes | RelativePathString;\n  export type AllRoutes = ExpoRouterRoutes | ExternalPathString;\n\n  /****************\n   * Route Utils  *\n   ****************/\n\n  type SearchOrHash = \\`?\\${string}\\` | \\`#\\${string}\\`;\n  type UnknownInputParams = Record<string, string | number | (string | number)[]>;\n  type UnknownOutputParams = Record<string, string | string[]>;\n\n  /**\n   * Return only the RoutePart of a string. If the string has multiple parts return never\n   *\n   * string   | type\n   * ---------|------\n   * 123      | 123\n   * /123/abc | never\n   * 123?abc  | never\n   * ./123    | never\n   * /123     | never\n   * 123/../  | never\n   */\n  type SingleRoutePart<S extends string> = S extends \\`\\${string}/\\${string}\\`\n    ? never\n    : S extends \\`\\${string}\\${SearchOrHash}\\`\n      ? never\n      : S extends ''\n        ? never\n        : S extends \\`(\\${string})\\`\n          ? never\n          : S extends \\`[\\${string}]\\`\n            ? never\n            : S;\n\n  /**\n   * Return only the CatchAll router part. If the string has search parameters or a hash return never\n   */\n  type CatchAllRoutePart<S extends string> = S extends \\`\\${string}\\${SearchOrHash}\\`\n    ? never\n    : S extends ''\n      ? never\n      : S extends \\`\\${string}(\\${string})\\${string}\\`\n        ? never\n        : S extends \\`\\${string}[\\${string}]\\${string}\\`\n          ? never\n          : S;\n\n  // type OptionalCatchAllRoutePart<S extends string> = S extends \\`\\${string}\\${SearchOrHash}\\` ? never : S\n\n  /**\n   * Return the name of a route parameter\n   * '[test]'    -> 'test'\n   * 'test'      -> never\n   * '[...test]' -> '...test'\n   */\n  type IsParameter<Part> = Part extends \\`[\\${infer ParamName}]\\` ? ParamName : never;\n\n  /**\n   * Return a union of all parameter names. If there are no names return never\n   *\n   * /[test]         -> 'test'\n   * /[abc]/[...def] -> 'abc'|'...def'\n   */\n  type ParameterNames<Path> = Path extends \\`\\${infer PartA}/\\${infer PartB}\\`\n    ? IsParameter<PartA> | ParameterNames<PartB>\n    : IsParameter<Path>;\n\n  /**\n   * Returns all segements of a route.\n   *\n   * /(group)/123/abc/[id]/[...rest] -> ['(group)', '123', 'abc', '[id]', '[...rest]'\n   */\n  type RouteSegments<Path> = Path extends \\`\\${infer PartA}/\\${infer PartB}\\`\n    ? PartA extends '' | '.'\n      ? [...RouteSegments<PartB>]\n      : [PartA, ...RouteSegments<PartB>]\n    : Path extends ''\n      ? []\n      : [Path];\n\n  /**\n   * Returns a Record of the routes parameters as strings and CatchAll parameters\n   *\n   * There are two versions, input and output, as you can input 'string | number' but\n   *  the output will always be 'string'\n   *\n   * /[id]/[...rest] -> { id: string, rest: string[] }\n   * /no-params      -> {}\n   */\n  type InputRouteParams<Path> = {\n    [Key in ParameterNames<Path> as Key extends \\`...\\${infer Name}\\`\n      ? Name\n      : Key]: Key extends \\`...\\${string}\\` ? (string | number)[] : string | number;\n  } & UnknownInputParams;\n\n  type OutputRouteParams<Path> = {\n    [Key in ParameterNames<Path> as Key extends \\`...\\${infer Name}\\`\n      ? Name\n      : Key]: Key extends \\`...\\${string}\\` ? string[] : string;\n  } & UnknownOutputParams;\n\n  /**\n   * Returns the search parameters for a route.\n   */\n  export type SearchParams<T extends AllRoutes> = T extends DynamicRouteTemplate\n    ? OutputRouteParams<T>\n    : T extends StaticRoutes\n      ? never\n      : UnknownOutputParams;\n\n  /**\n   * Route is mostly used as part of Href to ensure that a valid route is provided\n   *\n   * Given a dynamic route, this will return never. This is helpful for conditional logic\n   *\n   * /test         -> /test, /test2, etc\n   * /test/[abc]   -> never\n   * /test/resolve -> /test, /test2, etc\n   *\n   * Note that if we provide a value for [abc] then the route is allowed\n   *\n   * This is named Route to prevent confusion, as users they will often see it in tooltips\n   */\n  export type Route<T> = T extends string\n    ? T extends DynamicRouteTemplate\n      ? never\n      :\n          | StaticRoutes\n          | RelativePathString\n          | ExternalPathString\n          | (T extends \\`\\${infer P}\\${SearchOrHash}\\`\n              ? P extends DynamicRoutes<infer _>\n                ? T\n                : never\n              : T extends DynamicRoutes<infer _>\n                ? T\n                : never)\n    : never;\n\n  /*********\n   * Href  *\n   *********/\n\n  export type Href<T> = T extends Record<'pathname', string> ? HrefObject<T> : Route<T>;\n\n  export type HrefObject<\n    R extends Record<'pathname', string>,\n    P = R['pathname'],\n  > = P extends DynamicRouteTemplate\n    ? { pathname: P; params: InputRouteParams<P> }\n    : P extends Route<P>\n      ? { pathname: Route<P> | DynamicRouteTemplate; params?: never | InputRouteParams<never> }\n      : never;\n\n  /***********************\n   * Expo Router Exports *\n   ***********************/\n\n  export type Router = Omit<OriginalRouter, 'push' | 'replace' | 'setParams'> & {\n    /** Navigate to the provided href. */\n    push: <T>(href: Href<T>) => void;\n    /** Navigate to route without appending to the history. */\n    replace: <T>(href: Href<T>) => void;\n    /** Update the current route query params. */\n    setParams: <T = ''>(params?: T extends '' ? Record<string, string> : InputRouteParams<T>) => void;\n  };\n\n  /** The imperative router. */\n  export const router: Router;\n\n  /************\n   * <Link /> *\n   ************/\n  export interface LinkProps<T> extends OriginalLinkProps {\n    href: Href<T>;\n  }\n\n  export interface LinkComponent {\n    <T>(props: React.PropsWithChildren<LinkProps<T>>): JSX.Element;\n    /** Helper method to resolve an Href object into a string. */\n    resolveHref: <T>(href: Href<T>) => string;\n  }\n\n  /**\n   * Component to render link to another route using a path.\n   * Uses an anchor tag on the web.\n   *\n   * @param props.href Absolute path to route (e.g. \\`/feeds/hot\\`).\n   * @param props.replace Should replace the current route without adding to the history.\n   * @param props.asChild Forward props to child component. Useful for custom buttons.\n   * @param props.children Child elements to render the content.\n   * @param props.className On web, this sets the HTML \\`class\\` directly. On native, this can be used with CSS interop tools like Nativewind.\n   */\n  export const Link: LinkComponent;\n\n  /** Redirects to the href as soon as the component is mounted. */\n  export const Redirect: <T>(\n    props: React.PropsWithChildren<{ href: Href<T> }>\n  ) => JSX.Element;\n\n  /************\n   * Hooks *\n   ************/\n  export function useRouter(): Router;\n\n  export function useLocalSearchParams<\n    T extends AllRoutes | UnknownOutputParams = UnknownOutputParams,\n  >(): T extends AllRoutes ? SearchParams<T> : T;\n\n  /** @deprecated renamed to \\`useGlobalSearchParams\\` */\n  export function useSearchParams<\n    T extends AllRoutes | UnknownOutputParams = UnknownOutputParams,\n  >(): T extends AllRoutes ? SearchParams<T> : T;\n\n  export function useGlobalSearchParams<\n    T extends AllRoutes | UnknownOutputParams = UnknownOutputParams,\n  >(): T extends AllRoutes ? SearchParams<T> : T;\n\n  export function useSegments<\n    T extends AbsoluteRoute | RouteSegments<AbsoluteRoute> | RelativePathString,\n  >(): T extends AbsoluteRoute ? RouteSegments<T> : T extends string ? string[] : T;\n}\n`;\n"], "names": ["ARRAY_GROUP_REGEX", "CAPTURE_DYNAMIC_PARAMS", "CAPTURE_GROUP_REGEX", "CATCH_ALL", "SLUG", "TYPED_ROUTES_EXCLUSION_REGEX", "extrapolateGroupRoutes", "getTemplateString", "getTypedRoutesUtils", "setToUnionType", "setupTypedRoutes", "options", "typedRoutesModule", "resolveFrom", "silent", "projectRoot", "typedRoutes", "legacyTypedRoutes", "typedRoutesModulePath", "server", "metro", "typesDirectory", "routerDirectory", "plugin", "process", "env", "EXPO_ROUTER_APP_ROOT", "require", "metroWatchTypeScriptFiles", "eventTypes", "callback", "getWatchHandler", "version", "regenerateDeclarations", "filePathToRoute", "staticRoutes", "dynamicRoutes", "addFilePath", "isRouteFile", "filePath", "type", "shouldRegenerate", "route", "delete", "regenerateRouterDotTS", "Set", "values", "flatMap", "v", "Array", "from", "keys", "directoryExistsAsync", "walk", "debounce", "fn", "delay", "timeoutId", "args", "clearTimeout", "setTimeout", "apply", "typesDir", "dynamicRouteTemplates", "fs", "mkdir", "recursive", "writeFile", "path", "resolve", "routerDotTSTemplate", "dynamicRouteParams", "appRoot", "filePathSeperator", "sep", "Map", "normalizedFilePath", "replaceAll", "normalizedAppRoot", "replace", "match", "relative", "startsWith", "isAbsolute", "has", "dynamicParams", "matchAll", "map", "isDynamic", "size", "addRoute", "originalRoute", "set", "get", "add", "includes", "routeWithoutGroups", "routeWithSingleGroup", "s", "join", "directory", "files", "readdir", "file", "p", "stat", "isDirectory", "normalizedPath", "routes", "groupsMatch", "group", "trim", "unsafeTemplate"], "mappings": ";;;;;;;;;;;IAiBaA,iBAAiB;eAAjBA;;IANAC,sBAAsB;eAAtBA;;IAQAC,mBAAmB;eAAnBA;;IANAC,SAAS;eAATA;;IAEAC,IAAI;eAAJA;;IAUAC,4BAA4B;eAA5BA;;IAyTGC,sBAAsB;eAAtBA;;IA5JAC,iBAAiB;eAAjBA;;IAiBAC,mBAAmB;eAAnBA;;IAmHHC,cAAc;eAAdA;;IArRSC,gBAAgB;eAAhBA;;;;gEArCP;;;;;;;gEAEE;;;;;;;gEACO;;;;;;qBAEa;0BACN;2CAEW;;;;;;AAGnC,MAAMT,yBAAyB;AAE/B,MAAME,YAAY;AAElB,MAAMC,OAAO;AAEb,MAAMJ,oBAAoB;AAE1B,MAAME,sBAAsB;AAM5B,MAAMG,+BAA+B;AAYrC,eAAeK,iBAAiBC,OAAgC;IACrE;;;;;GAKC,GACD,MAAMC,oBAAoBC,sBAAW,CAACC,MAAM,CAC1CH,QAAQI,WAAW,EACnB;IAEF,OAAOH,oBAAoBI,YAAYJ,mBAAmBD,WAAWM,kBAAkBN;AACzF;AAEA,eAAeK,YACbE,qBAA0B,EAC1B,EAAEC,MAAM,EAAEC,KAAK,EAAEC,cAAc,EAAEN,WAAW,EAAEO,eAAe,EAAEC,MAAM,EAA2B;IAEhG;;;;GAIC,GACDC,QAAQC,GAAG,CAACC,oBAAoB,GAAGJ;IAEnC,MAAMV,oBAAoBe,QAAQT;IAElC;;GAEC,GACD,IAAIE,SAASD,QAAQ;QACnB,0BAA0B;QAC1BS,IAAAA,oDAAyB,EAAC;YACxBb;YACAI;YACAC;YACAS,YAAY;gBAAC;gBAAO;gBAAU;aAAS;YACvCC,UAAUlB,kBAAkBmB,eAAe,CAACV;QAC9C;IACF;IAEA;;;;;;GAMC,GACD,IAAI,aAAaT,qBAAqBA,kBAAkBoB,OAAO,IAAI,IAAI;QACrEpB,kBAAkBqB,sBAAsB,CAACZ,gBAAgBE;IAC3D,OAAO;QACLX,kBAAkBqB,sBAAsB,CAACZ;IAC3C;AACF;AAEA,eAAeJ,kBAAkB,EAC/BE,MAAM,EACNC,KAAK,EACLC,cAAc,EACdN,WAAW,EACXO,eAAe,EACS;IACxB,MAAM,EAAEY,eAAe,EAAEC,YAAY,EAAEC,aAAa,EAAEC,WAAW,EAAEC,WAAW,EAAE,GAC9E9B,oBAAoBc;IAEtB,0FAA0F;IAC1F,IAAIF,SAASD,QAAQ;QACnBS,IAAAA,oDAAyB,EAAC;YACxBb;YACAI;YACAC;YACAS,YAAY;gBAAC;gBAAO;gBAAU;aAAS;YACvC,MAAMC,UAAS,EAAES,QAAQ,EAAEC,IAAI,EAAE;gBAC/B,IAAI,CAACF,YAAYC,WAAW;oBAC1B;gBACF;gBAEA,IAAIE,mBAAmB;gBAEvB,IAAID,SAAS,UAAU;oBACrB,MAAME,QAAQR,gBAAgBK;oBAC9BJ,aAAaQ,MAAM,CAACD;oBACpBN,cAAcO,MAAM,CAACD;oBACrBD,mBAAmB;gBACrB,OAAO;oBACLA,mBAAmBJ,YAAYE;gBACjC;gBAEA,IAAIE,kBAAkB;oBACpBG,sBACEvB,gBACA,IAAIwB,IAAI;2BAAIV,aAAaW,MAAM;qBAAG,CAACC,OAAO,CAAC,CAACC,IAAMC,MAAMC,IAAI,CAACF,MAC7D,IAAIH,IAAI;2BAAIT,cAAcU,MAAM;qBAAG,CAACC,OAAO,CAAC,CAACC,IAAMC,MAAMC,IAAI,CAACF,MAC9D,IAAIH,IAAIT,cAAce,IAAI;gBAE9B;YACF;QACF;IACF;IAEA,IAAI,MAAMC,IAAAA,yBAAoB,EAAC9B,kBAAkB;QAC/C,iDAAiD;QACjD,qGAAqG;QACrG,MAAM+B,KAAK/B,iBAAiBe;IAC9B;IAEAO,sBACEvB,gBACA,IAAIwB,IAAI;WAAIV,aAAaW,MAAM;KAAG,CAACC,OAAO,CAAC,CAACC,IAAMC,MAAMC,IAAI,CAACF,MAC7D,IAAIH,IAAI;WAAIT,cAAcU,MAAM;KAAG,CAACC,OAAO,CAAC,CAACC,IAAMC,MAAMC,IAAI,CAACF,MAC9D,IAAIH,IAAIT,cAAce,IAAI;AAE9B;AAEA,SAASG,SAAyDC,EAAK,EAAEC,KAAa;IACpF,IAAIC;IACJ,OAAO,SAAmB,GAAGC,IAAW;QACtCC,aAAaF;QACbA,YAAYG,WAAW,IAAML,GAAGM,KAAK,CAAC,IAAI,EAAEH,OAAOF;IACrD;AACF;AAEA;;;CAGC,GACD,MAAMZ,wBAAwBU,SAC5B,OACEQ,UACA3B,cACAC,eACA2B;IAEA,MAAMC,mBAAE,CAACC,KAAK,CAACH,UAAU;QAAEI,WAAW;IAAK;IAC3C,MAAMF,mBAAE,CAACG,SAAS,CAChBC,eAAI,CAACC,OAAO,CAACP,UAAU,kBACvBvD,kBAAkB4B,cAAcC,eAAe2B;AAEnD,GACA;AAMK,SAASxD,kBACd4B,YAAyB,EACzBC,aAA0B,EAC1B2B,qBAAkC;IAElC,OAAOO,oBAAoB;QACzBnC,cAAc1B,eAAe0B;QAC7BC,eAAe3B,eAAe2B;QAC9BmC,oBAAoB9D,eAAesD;IACrC;AACF;AAOO,SAASvD,oBAAoBgE,OAAe,EAAEC,oBAAoBL,eAAI,CAACM,GAAG;IAC/E;;;;;;GAMC,GACD,MAAMvC,eAAe,IAAIwC,IAAyB;QAAC;YAAC;YAAK,IAAI9B,IAAI;SAAK;KAAC;IACvE;;;;;;;;;GASC,GACD,MAAMT,gBAAgB,IAAIuC;IAE1B,SAASC,mBAAmBrC,QAAgB;QAC1C,OAAOA,SAASsC,UAAU,CAACJ,mBAAmB;IAChD;IAEA,MAAMK,oBAAoBF,mBAAmBJ;IAE7C,MAAMtC,kBAAkB,CAACK;QACvB,OAAOqC,mBAAmBrC,UACvBwC,OAAO,CAACD,mBAAmB,IAC3BC,OAAO,CAAC,kBAAkB,IAC1BA,OAAO,CAAC,cAAc;IAC3B;IAEA,MAAMzC,cAAc,CAACC;QACnB,IAAIA,SAASyC,KAAK,CAAC3E,+BAA+B;YAChD,OAAO;QACT;QAEA,iDAAiD;QACjD,MAAM4E,WAAWb,eAAI,CAACa,QAAQ,CAACT,SAASjC;QACxC,OAAO0C,YAAY,CAACA,SAASC,UAAU,CAAC,SAAS,CAACd,eAAI,CAACe,UAAU,CAACF;IACpE;IAEA,MAAM5C,cAAc,CAACE;QACnB,IAAI,CAACD,YAAYC,WAAW;YAC1B,OAAO;QACT;QAEA,MAAMG,QAAQR,gBAAgBK;QAE9B,sCAAsC;QACtC,IAAIJ,aAAaiD,GAAG,CAAC1C,UAAUN,cAAcgD,GAAG,CAAC1C,QAAQ;YACvD,OAAO;QACT;QAEA,MAAM2C,gBAAgB,IAAIxC,IACxB;eAAIH,MAAM4C,QAAQ,CAACrF;SAAwB,CAACsF,GAAG,CAAC,CAACP,QAAUA,KAAK,CAAC,EAAE;QAErE,MAAMQ,YAAYH,cAAcI,IAAI,GAAG;QAEvC,MAAMC,WAAW,CAACC,eAAuBjD;YACvC,IAAI8C,WAAW;gBACb,IAAII,MAAMxD,cAAcyD,GAAG,CAACF;gBAE5B,IAAI,CAACC,KAAK;oBACRA,MAAM,IAAI/C;oBACVT,cAAcwD,GAAG,CAACD,eAAeC;gBACnC;gBAEAA,IAAIE,GAAG,CACLpD,MACGmC,UAAU,CAAC1E,WAAW,2BACtB0E,UAAU,CAACzE,MAAM;YAExB,OAAO;gBACL,IAAIwF,MAAMzD,aAAa0D,GAAG,CAACF;gBAE3B,IAAI,CAACC,KAAK;oBACRA,MAAM,IAAI/C;oBACVV,aAAayD,GAAG,CAACD,eAAeC;gBAClC;gBAEAA,IAAIE,GAAG,CAACpD;YACV;QACF;QAEA,IAAI,CAACA,MAAMsC,KAAK,CAAChF,oBAAoB;YACnC0F,SAAShD,OAAOA;QAClB;QAEA,4CAA4C;QAC5C,IAAIA,MAAMqD,QAAQ,CAAC,OAAO;YACxB,MAAMC,qBAAqBtD,MAAMqC,OAAO,CAAC,cAAc;YACvDW,SAAShD,OAAOsD;YAEhB,uDAAuD;YACvD,sDAAsD;YACtD,KAAK,MAAMC,wBAAwB3F,uBAAuBoC,OAAQ;gBAChEgD,SAAShD,OAAOuD;YAClB;QACF;QAEA,OAAO;IACT;IAEA,OAAO;QACL9D;QACAC;QACAF;QACAG;QACAC;IACF;AACF;AAEO,MAAM7B,iBAAiB,CAAImF;IAChC,OAAOA,IAAIH,IAAI,GAAG,IAAI;WAAIG;KAAI,CAACL,GAAG,CAAC,CAACW,IAAM,CAAC,EAAE,EAAEA,EAAE,EAAE,CAAC,EAAEC,IAAI,CAAC,SAAS;AACtE;AAEA;;CAEC,GACD,eAAe9C,KAAK+C,SAAiB,EAAEtE,QAAoC;IACzE,MAAMuE,QAAQ,MAAMrC,mBAAE,CAACsC,OAAO,CAACF;IAC/B,KAAK,MAAMG,QAAQF,MAAO;QACxB,MAAMG,IAAIpC,eAAI,CAAC+B,IAAI,CAACC,WAAWG;QAC/B,IAAI,AAAC,CAAA,MAAMvC,mBAAE,CAACyC,IAAI,CAACD,EAAC,EAAGE,WAAW,IAAI;YACpC,MAAMrD,KAAKmD,GAAG1E;QAChB,OAAO;YACL,4DAA4D;YAC5D,MAAM6E,iBAAiBH,EAAE3B,UAAU,CAACT,eAAI,CAACM,GAAG,EAAE;YAC9C5C,SAAS6E;QACX;IACF;AACF;AAKO,SAASrG,uBACdoC,KAAa,EACbkE,SAAsB,IAAI/D,KAAK;IAE/B,+FAA+F;IAC/F+D,OAAOd,GAAG,CAACpD,MAAMmC,UAAU,CAAC7E,mBAAmB,IAAI6E,UAAU,CAAC,QAAQ,KAAKE,OAAO,CAAC,OAAO;IAE1F,MAAMC,QAAQtC,MAAMsC,KAAK,CAAChF;IAE1B,IAAI,CAACgF,OAAO;QACV4B,OAAOd,GAAG,CAACpD;QACX,OAAOkE;IACT;IAEA,MAAMC,cAAc7B,KAAK,CAAC,EAAE;IAE5B,KAAK,MAAM8B,SAASD,YAAYvB,QAAQ,CAACpF,qBAAsB;QAC7DI,uBAAuBoC,MAAMqC,OAAO,CAAC8B,aAAa,CAAC,CAAC,EAAEC,KAAK,CAAC,EAAE,CAACC,IAAI,GAAG,CAAC,CAAC,GAAGH;IAC7E;IAEA,OAAOA;AACT;AAEA;;;;CAIC,GACD,MAAMtC,sBAAsB0C,IAAAA,wBAAc,CAAA,CAAC;;;;;;;;;sBASrB,EAAE,eAAe;;yCAEE,EAAE,gBAAgB;;8BAE7B,EAAE,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqOrD,CAAC"}
{"version": 3, "sources": ["../../../src/utils/git.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport chalk from 'chalk';\n\nimport { env } from './env';\nimport { isInteractive } from './interactive';\nimport { confirmAsync } from './prompts';\nimport * as Log from '../log';\n\nexport async function maybeBailOnGitStatusAsync(): Promise<boolean> {\n  if (env.EXPO_NO_GIT_STATUS) {\n    Log.warn(\n      'Git status is dirty but the command will continue because EXPO_NO_GIT_STATUS is enabled...'\n    );\n    return false;\n  }\n  const isGitStatusClean = await validateGitStatusAsync();\n\n  // Give people a chance to bail out if git working tree is dirty\n  if (!isGitStatusClean) {\n    if (!isInteractive()) {\n      Log.warn(\n        `Git status is dirty but the command will continue because the terminal is not interactive.`\n      );\n      return false;\n    }\n\n    Log.log();\n    const answer = await confirmAsync({\n      message: `Continue with uncommited changes?`,\n    });\n\n    if (!answer) {\n      return true;\n    }\n\n    Log.log();\n  }\n  return false;\n}\n\nexport async function validateGitStatusAsync(): Promise<boolean> {\n  let workingTreeStatus = 'unknown';\n  try {\n    const result = await spawnAsync('git', ['status', '--porcelain']);\n    workingTreeStatus = result.stdout === '' ? 'clean' : 'dirty';\n  } catch {\n    // Maybe git is not installed?\n    // Maybe this project is not using git?\n  }\n\n  if (workingTreeStatus === 'clean') {\n    return true;\n  } else if (workingTreeStatus === 'dirty') {\n    logWarning(\n      'Git branch has uncommited file changes',\n      `It's recommended to commit all changes before proceeding in case you want to revert generated changes.`\n    );\n  } else {\n    logWarning(\n      'No git repo found in current directory',\n      `Use git to track file changes before running commands that modify project files.`\n    );\n  }\n\n  return false;\n}\n\nfunction logWarning(warning: string, hint: string) {\n  Log.warn(chalk.bold`! ` + warning);\n  Log.log(chalk.gray`\\u203A ` + chalk.gray(hint));\n}\n"], "names": ["maybeBailOnGitStatusAsync", "validateGitStatusAsync", "env", "EXPO_NO_GIT_STATUS", "Log", "warn", "isGitStatusClean", "isInteractive", "log", "answer", "<PERSON><PERSON><PERSON>", "message", "workingTreeStatus", "result", "spawnAsync", "stdout", "logWarning", "warning", "hint", "chalk", "bold", "gray"], "mappings": ";;;;;;;;;;;IAQsBA,yBAAyB;eAAzBA;;IAgCAC,sBAAsB;eAAtBA;;;;gEAxCC;;;;;;;gEACL;;;;;;qBAEE;6BACU;yBACD;6DACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEd,eAAeD;IACpB,IAAIE,QAAG,CAACC,kBAAkB,EAAE;QAC1BC,KAAIC,IAAI,CACN;QAEF,OAAO;IACT;IACA,MAAMC,mBAAmB,MAAML;IAE/B,gEAAgE;IAChE,IAAI,CAACK,kBAAkB;QACrB,IAAI,CAACC,IAAAA,0BAAa,KAAI;YACpBH,KAAIC,IAAI,CACN,CAAC,0FAA0F,CAAC;YAE9F,OAAO;QACT;QAEAD,KAAII,GAAG;QACP,MAAMC,SAAS,MAAMC,IAAAA,qBAAY,EAAC;YAChCC,SAAS,CAAC,iCAAiC,CAAC;QAC9C;QAEA,IAAI,CAACF,QAAQ;YACX,OAAO;QACT;QAEAL,KAAII,GAAG;IACT;IACA,OAAO;AACT;AAEO,eAAeP;IACpB,IAAIW,oBAAoB;IACxB,IAAI;QACF,MAAMC,SAAS,MAAMC,IAAAA,qBAAU,EAAC,OAAO;YAAC;YAAU;SAAc;QAChEF,oBAAoBC,OAAOE,MAAM,KAAK,KAAK,UAAU;IACvD,EAAE,OAAM;IACN,8BAA8B;IAC9B,uCAAuC;IACzC;IAEA,IAAIH,sBAAsB,SAAS;QACjC,OAAO;IACT,OAAO,IAAIA,sBAAsB,SAAS;QACxCI,WACE,0CACA,CAAC,sGAAsG,CAAC;IAE5G,OAAO;QACLA,WACE,0CACA,CAAC,gFAAgF,CAAC;IAEtF;IAEA,OAAO;AACT;AAEA,SAASA,WAAWC,OAAe,EAAEC,IAAY;IAC/Cd,KAAIC,IAAI,CAACc,gBAAK,CAACC,IAAI,CAAC,EAAE,CAAC,GAAGH;IAC1Bb,KAAII,GAAG,CAACW,gBAAK,CAACE,IAAI,CAAC,OAAO,CAAC,GAAGF,gBAAK,CAACE,IAAI,CAACH;AAC3C"}
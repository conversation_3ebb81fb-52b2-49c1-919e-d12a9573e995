{"version": 3, "sources": ["../../../../../src/start/server/metro/MetroBundlerDevServer.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { ExpoConfig, getConfig } from '@expo/config';\nimport { getMetroServerRoot } from '@expo/config/paths';\nimport * as runtimeEnv from '@expo/env';\nimport { SerialAsset } from '@expo/metro-config/build/serializer/serializerAssets';\nimport assert from 'assert';\nimport chalk from 'chalk';\nimport { DeltaResult, TransformInputOptions } from 'metro';\nimport baseJSBundle from 'metro/src/DeltaBundler/Serializers/baseJSBundle';\nimport {\n  sourceMapGeneratorNonBlocking,\n  type SourceMapGeneratorOptions,\n} from 'metro/src/DeltaBundler/Serializers/sourceMapGenerator';\nimport type MetroHmrServer from 'metro/src/HmrServer';\nimport type { Client as MetroHmrClient } from 'metro/src/HmrServer';\nimport { GraphRevision } from 'metro/src/IncrementalBundler';\nimport bundleToString from 'metro/src/lib/bundleToString';\nimport getGraphId from 'metro/src/lib/getGraphId';\nimport { TransformProfile } from 'metro-babel-transformer';\nimport type { CustomResolverOptions } from 'metro-resolver/src/types';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport {\n  createServerComponentsMiddleware,\n  fileURLToFilePath,\n} from './createServerComponentsMiddleware';\nimport { createRouteHandlerMiddleware } from './createServerRouteMiddleware';\nimport { ExpoRouterServerManifestV1, fetchManifest } from './fetchRouterManifest';\nimport { instantiateMetroAsync } from './instantiateMetro';\nimport { getErrorOverlayHtmlAsync, IS_METRO_BUNDLE_ERROR_SYMBOL } from './metroErrorInterface';\nimport { assertMetroPrivateServer, MetroPrivateServer } from './metroPrivateServer';\nimport { metroWatchTypeScriptFiles } from './metroWatchTypeScriptFiles';\nimport {\n  getRouterDirectoryModuleIdWithManifest,\n  hasWarnedAboutApiRoutes,\n  isApiRouteConvention,\n  warnInvalidWebOutput,\n} from './router';\nimport { serializeHtmlWithAssets } from './serializeHtml';\nimport { observeAnyFileChanges, observeFileChanges } from './waitForMetroToObserveTypeScriptFile';\nimport { BundleAssetWithFileHashes, ExportAssetMap } from '../../../export/saveAssets';\nimport { Log } from '../../../log';\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\nimport { toPosixPath } from '../../../utils/filePath';\nimport { getFreePortAsync } from '../../../utils/port';\nimport { BundlerDevServer, BundlerStartOptions, DevServerInstance } from '../BundlerDevServer';\nimport {\n  cachedSourceMaps,\n  evalMetroAndWrapFunctions,\n  evalMetroNoHandling,\n} from '../getStaticRenderFunctions';\nimport { ContextModuleSourceMapsMiddleware } from '../middleware/ContextModuleSourceMapsMiddleware';\nimport { CreateFileMiddleware } from '../middleware/CreateFileMiddleware';\nimport { DevToolsPluginMiddleware } from '../middleware/DevToolsPluginMiddleware';\nimport { createDomComponentsMiddleware } from '../middleware/DomComponentsMiddleware';\nimport { FaviconMiddleware } from '../middleware/FaviconMiddleware';\nimport { HistoryFallbackMiddleware } from '../middleware/HistoryFallbackMiddleware';\nimport { InterstitialPageMiddleware } from '../middleware/InterstitialPageMiddleware';\nimport { resolveMainModuleName } from '../middleware/ManifestMiddleware';\nimport { RuntimeRedirectMiddleware } from '../middleware/RuntimeRedirectMiddleware';\nimport { ServeStaticMiddleware } from '../middleware/ServeStaticMiddleware';\nimport {\n  convertPathToModuleSpecifier,\n  createBundleUrlPath,\n  ExpoMetroOptions,\n  createBundleOsPath,\n  getAsyncRoutesFromExpoConfig,\n  getBaseUrlFromExpoConfig,\n  getMetroDirectBundleOptions,\n  shouldEnableAsyncImports,\n} from '../middleware/metroOptions';\nimport { prependMiddleware } from '../middleware/mutations';\nimport { startTypescriptTypeGenerationAsync } from '../type-generation/startTypescriptTypeGeneration';\n\nexport type ExpoRouterRuntimeManifest = Awaited<\n  ReturnType<typeof import('expo-router/build/static/renderStaticContent').getManifest>\n>;\ntype MetroOnProgress = NonNullable<\n  import('metro/src/DeltaBundler/types').Options<void>['onProgress']\n>;\ntype SSRLoadModuleFunc = <T extends Record<string, any>>(\n  filePath: string,\n  specificOptions?: Partial<ExpoMetroOptions>,\n  extras?: { hot?: boolean }\n) => Promise<T>;\n\ninterface BundleDirectResult {\n  numModifiedFiles: number;\n  lastModifiedDate: Date;\n  nextRevId: string;\n  bundle: string;\n  map: string;\n  /** Defined if the output is multi-bundle. */\n  artifacts?: SerialAsset[];\n  assets?: readonly BundleAssetWithFileHashes[];\n}\n\ninterface MetroModuleContentsResult extends BundleDirectResult {\n  filename: string;\n}\n\ninterface SSRModuleContentsResult extends Omit<BundleDirectResult, 'bundle'> {\n  filename: string;\n  src: string;\n  map: string;\n}\n\nconst debug = require('debug')('expo:start:server:metro') as typeof console.log;\n\n/** Default port to use for apps running in Expo Go. */\nconst EXPO_GO_METRO_PORT = 8081;\n\n/** Default port to use for apps that run in standard React Native projects or Expo Dev Clients. */\nconst DEV_CLIENT_METRO_PORT = 8081;\n\nexport class MetroBundlerDevServer extends BundlerDevServer {\n  private metro: MetroPrivateServer | null = null;\n  private hmrServer: MetroHmrServer | null = null;\n  private ssrHmrClients: Map<string, MetroHmrClient> = new Map();\n  isReactServerComponentsEnabled?: boolean;\n  isReactServerRoutesEnabled?: boolean;\n\n  get name(): string {\n    return 'metro';\n  }\n\n  async resolvePortAsync(options: Partial<BundlerStartOptions> = {}): Promise<number> {\n    const port =\n      // If the manually defined port is busy then an error should be thrown...\n      options.port ??\n      // Otherwise use the default port based on the runtime target.\n      (options.devClient\n        ? // Don't check if the port is busy if we're using the dev client since most clients are hardcoded to 8081.\n          Number(process.env.RCT_METRO_PORT) || DEV_CLIENT_METRO_PORT\n        : // Otherwise (running in Expo Go) use a free port that falls back on the classic 8081 port.\n          await getFreePortAsync(EXPO_GO_METRO_PORT));\n\n    return port;\n  }\n\n  async exportExpoRouterApiRoutesAsync({\n    includeSourceMaps,\n    outputDir,\n    prerenderManifest,\n    platform,\n  }: {\n    includeSourceMaps?: boolean;\n    outputDir: string;\n    // This does not contain the API routes info.\n    prerenderManifest: ExpoRouterServerManifestV1;\n    platform: string;\n  }): Promise<{ files: ExportAssetMap; manifest: ExpoRouterServerManifestV1<string> }> {\n    const { routerRoot } = this.instanceMetroOptions;\n    assert(\n      routerRoot != null,\n      'The server must be started before calling exportExpoRouterApiRoutesAsync.'\n    );\n\n    const appDir = path.join(this.projectRoot, routerRoot);\n    const manifest = await this.getExpoRouterRoutesManifestAsync({ appDir });\n\n    const files: ExportAssetMap = new Map();\n\n    // Inject RSC middleware.\n    const rscPath = '/_flight/[...rsc]';\n\n    if (\n      this.isReactServerComponentsEnabled &&\n      // If the RSC route is not already in the manifest, add it.\n      !manifest.apiRoutes.find((route) => route.page.startsWith('/_flight/'))\n    ) {\n      debug('Adding RSC route to the manifest:', rscPath);\n      // NOTE: This might need to be sorted to the correct spot in the future.\n      manifest.apiRoutes.push({\n        file: resolveFrom(this.projectRoot, '@expo/cli/static/template/[...rsc]+api.ts'),\n        page: rscPath,\n        namedRegex: '^/_flight(?:/(?<rsc>.+?))?(?:/)?$',\n        routeKeys: { rsc: 'rsc' },\n      });\n    }\n\n    for (const route of manifest.apiRoutes) {\n      const filepath = path.isAbsolute(route.file) ? route.file : path.join(appDir, route.file);\n      const contents = await this.bundleApiRoute(filepath, { platform });\n\n      const artifactFilename =\n        route.page === rscPath\n          ? // HACK: Add RSC renderer to the output...\n            convertPathToModuleSpecifier(path.join(outputDir, '.' + rscPath + '.js'))\n          : convertPathToModuleSpecifier(\n              path.join(outputDir, path.relative(appDir, filepath.replace(/\\.[tj]sx?$/, '.js')))\n            );\n\n      if (contents) {\n        let src = contents.src;\n\n        if (includeSourceMaps && contents.map) {\n          // TODO(kitten): Merge the source map transformer in the future\n          // https://github.com/expo/expo/blob/0dffdb15/packages/%40expo/metro-config/src/serializer/serializeChunks.ts#L422-L439\n          // Alternatively, check whether `sourcesRoot` helps here\n          const artifactBasename = encodeURIComponent(path.basename(artifactFilename) + '.map');\n          src = src.replace(\n            /\\/\\/# sourceMappingURL=.*/g,\n            `//# sourceMappingURL=${artifactBasename}`\n          );\n\n          const parsedMap =\n            typeof contents.map === 'string' ? JSON.parse(contents.map) : contents.map;\n          files.set(artifactFilename + '.map', {\n            contents: JSON.stringify({\n              version: parsedMap.version,\n              sources: parsedMap.sources.map((source: string) => {\n                source =\n                  typeof source === 'string' && source.startsWith(this.projectRoot)\n                    ? path.relative(this.projectRoot, source)\n                    : source;\n                return convertPathToModuleSpecifier(source);\n              }),\n              sourcesContent: new Array(parsedMap.sources.length).fill(null),\n              names: parsedMap.names,\n              mappings: parsedMap.mappings,\n            }),\n            apiRouteId: route.page,\n            targetDomain: 'server',\n          });\n        }\n        files.set(artifactFilename, {\n          contents: src,\n          apiRouteId: route.page,\n          targetDomain: 'server',\n        });\n      }\n      // Remap the manifest files to represent the output files.\n      route.file = artifactFilename;\n    }\n\n    return {\n      manifest: {\n        ...manifest,\n        htmlRoutes: prerenderManifest.htmlRoutes,\n      },\n      files,\n    };\n  }\n\n  async getExpoRouterRoutesManifestAsync({ appDir }: { appDir: string }) {\n    // getBuiltTimeServerManifest\n    const { exp } = getConfig(this.projectRoot);\n    const manifest = await fetchManifest(this.projectRoot, {\n      ...exp.extra?.router,\n      preserveRedirectAndRewrites: true,\n      asJson: true,\n      appDir,\n    });\n\n    if (!manifest) {\n      throw new CommandError(\n        'EXPO_ROUTER_SERVER_MANIFEST',\n        'Unexpected error: server manifest could not be fetched.'\n      );\n    }\n\n    return manifest;\n  }\n\n  async getServerManifestAsync(): Promise<{\n    serverManifest: ExpoRouterServerManifestV1;\n    htmlManifest: ExpoRouterRuntimeManifest;\n  }> {\n    const { exp } = getConfig(this.projectRoot);\n    // NOTE: This could probably be folded back into `renderStaticContent` when expo-asset and font support RSC.\n    const { getBuildTimeServerManifestAsync, getManifest } = await this.ssrLoadModule<\n      typeof import('expo-router/build/static/getServerManifest')\n    >('expo-router/build/static/getServerManifest.js', {\n      // Only use react-server environment when the routes are using react-server rendering by default.\n      environment: this.isReactServerRoutesEnabled ? 'react-server' : 'node',\n    });\n\n    return {\n      serverManifest: await getBuildTimeServerManifestAsync({ ...exp.extra?.router }),\n      htmlManifest: await getManifest({ ...exp.extra?.router }),\n    };\n  }\n\n  async getStaticRenderFunctionAsync(): Promise<{\n    serverManifest: ExpoRouterServerManifestV1;\n    manifest: ExpoRouterRuntimeManifest;\n    renderAsync: (path: string) => Promise<string>;\n  }> {\n    const url = this.getDevServerUrlOrAssert();\n\n    const { getStaticContent, getManifest, getBuildTimeServerManifestAsync } =\n      await this.ssrLoadModule<typeof import('expo-router/build/static/renderStaticContent')>(\n        'expo-router/node/render.js',\n        {\n          // This must always use the legacy rendering resolution (no `react-server`) because it leverages\n          // the previous React SSG utilities which aren't available in React 19.\n          environment: 'node',\n        }\n      );\n\n    const { exp } = getConfig(this.projectRoot);\n\n    return {\n      serverManifest: await getBuildTimeServerManifestAsync({\n        ...exp.extra?.router,\n      }),\n      // Get routes from Expo Router.\n      manifest: await getManifest({ preserveApiRoutes: false, ...exp.extra?.router }),\n      // Get route generating function\n      async renderAsync(path: string) {\n        return await getStaticContent(new URL(path, url));\n      },\n    };\n  }\n\n  async getStaticResourcesAsync({\n    includeSourceMaps,\n    mainModuleName,\n    clientBoundaries = this.instanceMetroOptions.clientBoundaries ?? [],\n    platform = 'web',\n  }: {\n    includeSourceMaps?: boolean;\n    mainModuleName?: string;\n    clientBoundaries?: string[];\n    platform?: string;\n  } = {}) {\n    const { mode, minify, isExporting, baseUrl, reactCompiler, routerRoot, asyncRoutes } =\n      this.instanceMetroOptions;\n    assert(\n      mode != null &&\n        isExporting != null &&\n        baseUrl != null &&\n        routerRoot != null &&\n        reactCompiler != null &&\n        asyncRoutes != null,\n      'The server must be started before calling getStaticResourcesAsync.'\n    );\n\n    const resolvedMainModuleName =\n      mainModuleName ?? './' + resolveMainModuleName(this.projectRoot, { platform });\n    return await this.metroImportAsArtifactsAsync(resolvedMainModuleName, {\n      splitChunks: isExporting && !env.EXPO_NO_BUNDLE_SPLITTING,\n      platform,\n      mode,\n      minify,\n      environment: 'client',\n      serializerIncludeMaps: includeSourceMaps,\n      mainModuleName: resolvedMainModuleName,\n      lazy: shouldEnableAsyncImports(this.projectRoot),\n      asyncRoutes,\n      baseUrl,\n      isExporting,\n      routerRoot,\n      clientBoundaries,\n      reactCompiler,\n      bytecode: false,\n    });\n  }\n\n  private async getStaticPageAsync(pathname: string) {\n    const { mode, isExporting, clientBoundaries, baseUrl, reactCompiler, routerRoot, asyncRoutes } =\n      this.instanceMetroOptions;\n    assert(\n      mode != null &&\n        isExporting != null &&\n        baseUrl != null &&\n        reactCompiler != null &&\n        routerRoot != null &&\n        asyncRoutes != null,\n      'The server must be started before calling getStaticPageAsync.'\n    );\n    const platform = 'web';\n\n    const devBundleUrlPathname = createBundleUrlPath({\n      splitChunks: isExporting && !env.EXPO_NO_BUNDLE_SPLITTING,\n      platform,\n      mode,\n      environment: 'client',\n      reactCompiler,\n      mainModuleName: resolveMainModuleName(this.projectRoot, { platform }),\n      lazy: shouldEnableAsyncImports(this.projectRoot),\n      baseUrl,\n      isExporting,\n      asyncRoutes,\n      routerRoot,\n      clientBoundaries,\n      bytecode: false,\n    });\n\n    const bundleStaticHtml = async (): Promise<string> => {\n      const { getStaticContent } = await this.ssrLoadModule<\n        typeof import('expo-router/build/static/renderStaticContent')\n      >('expo-router/node/render.js', {\n        // This must always use the legacy rendering resolution (no `react-server`) because it leverages\n        // the previous React SSG utilities which aren't available in React 19.\n        environment: 'node',\n        minify: false,\n        isExporting,\n        platform,\n      });\n\n      const location = new URL(pathname, this.getDevServerUrlOrAssert());\n      return await getStaticContent(location);\n    };\n\n    const [{ artifacts: resources }, staticHtml] = await Promise.all([\n      this.getStaticResourcesAsync({\n        clientBoundaries: [],\n      }),\n      bundleStaticHtml(),\n    ]);\n    const content = serializeHtmlWithAssets({\n      isExporting,\n      resources,\n      template: staticHtml,\n      devBundleUrl: devBundleUrlPathname,\n      baseUrl,\n      hydrate: env.EXPO_WEB_DEV_HYDRATE,\n    });\n    return {\n      content,\n      resources,\n    };\n  }\n\n  // Set when the server is started.\n  private instanceMetroOptions: Partial<ExpoMetroOptions> = {};\n\n  private ssrLoadModule: SSRLoadModuleFunc = async (\n    filePath,\n    specificOptions = {},\n    extras = {}\n  ) => {\n    const res = await this.ssrLoadModuleContents(filePath, specificOptions);\n\n    if (\n      // TODO: hot should be a callback function for invalidating the related SSR module.\n      extras.hot &&\n      this.instanceMetroOptions.isExporting !== true\n    ) {\n      // Register SSR HMR\n      const serverRoot = getMetroServerRoot(this.projectRoot);\n      const relativePath = path.relative(serverRoot, res.filename);\n      const url = new URL(relativePath, this.getDevServerUrlOrAssert());\n      this.setupHmr(url);\n    }\n\n    return evalMetroAndWrapFunctions(\n      this.projectRoot,\n      res.src,\n      res.filename,\n      specificOptions.isExporting ?? this.instanceMetroOptions.isExporting!\n    );\n  };\n\n  private async metroImportAsArtifactsAsync(\n    filePath: string,\n    specificOptions: Partial<Omit<ExpoMetroOptions, 'serializerOutput'>> = {}\n  ) {\n    const results = await this.ssrLoadModuleContents(filePath, {\n      serializerOutput: 'static',\n      ...specificOptions,\n    });\n\n    // NOTE: This could potentially need more validation in the future.\n    if (results.artifacts && results.assets) {\n      return {\n        artifacts: results.artifacts,\n        assets: results.assets,\n        src: results.src,\n        filename: results.filename,\n        map: results.map,\n      };\n    }\n    throw new CommandError('Invalid bundler results: ' + results);\n  }\n\n  private async metroLoadModuleContents(\n    filePath: string,\n    specificOptions: ExpoMetroOptions,\n    extraOptions: {\n      sourceMapUrl?: string;\n      unstable_transformProfile?: TransformProfile;\n    } = {}\n  ): Promise<MetroModuleContentsResult> {\n    const { baseUrl } = this.instanceMetroOptions;\n    assert(baseUrl != null, 'The server must be started before calling metroLoadModuleContents.');\n\n    const opts: ExpoMetroOptions = {\n      // TODO: Possibly issues with using an absolute path here...\n      // mainModuleName: filePath,\n      lazy: false,\n      asyncRoutes: false,\n      inlineSourceMap: false,\n      engine: 'hermes',\n      minify: false,\n      // bytecode: false,\n      // Bundle in Node.js mode for SSR.\n      environment: 'node',\n      // platform: 'web',\n      // mode: 'development',\n      //\n      ...this.instanceMetroOptions,\n      baseUrl,\n      // routerRoot,\n      // isExporting,\n      ...specificOptions,\n    };\n\n    const expoBundleOptions = getMetroDirectBundleOptions(opts);\n\n    const resolverOptions = {\n      customResolverOptions: expoBundleOptions.customResolverOptions ?? {},\n      dev: expoBundleOptions.dev ?? true,\n    };\n\n    const transformOptions: TransformInputOptions = {\n      dev: expoBundleOptions.dev ?? true,\n      hot: true,\n      minify: expoBundleOptions.minify ?? false,\n      type: 'module',\n      unstable_transformProfile:\n        extraOptions.unstable_transformProfile ??\n        expoBundleOptions.unstable_transformProfile ??\n        'default',\n      customTransformOptions: expoBundleOptions.customTransformOptions ?? Object.create(null),\n      platform: expoBundleOptions.platform ?? 'web',\n      // @ts-expect-error: `runtimeBytecodeVersion` does not exist in `expoBundleOptions` or `TransformInputOptions`\n      runtimeBytecodeVersion: expoBundleOptions.runtimeBytecodeVersion,\n    };\n\n    const resolvedEntryFilePath = await this.resolveRelativePathAsync(filePath, {\n      resolverOptions,\n      transformOptions,\n    });\n\n    const filename = createBundleOsPath({\n      ...opts,\n      mainModuleName: resolvedEntryFilePath,\n    });\n\n    // https://github.com/facebook/metro/blob/2405f2f6c37a1b641cc379b9c733b1eff0c1c2a1/packages/metro/src/lib/parseOptionsFromUrl.js#L55-L87\n    const results = await this._bundleDirectAsync(resolvedEntryFilePath, {\n      graphOptions: {\n        lazy: expoBundleOptions.lazy ?? false,\n        shallow: expoBundleOptions.shallow ?? false,\n      },\n      resolverOptions,\n      serializerOptions: {\n        ...expoBundleOptions.serializerOptions,\n\n        inlineSourceMap: expoBundleOptions.inlineSourceMap ?? false,\n        modulesOnly: expoBundleOptions.modulesOnly ?? false,\n        runModule: expoBundleOptions.runModule ?? true,\n        // @ts-expect-error\n        sourceUrl: expoBundleOptions.sourceUrl,\n        // @ts-expect-error\n        sourceMapUrl: extraOptions.sourceMapUrl ?? expoBundleOptions.sourceMapUrl,\n      },\n      transformOptions,\n    });\n\n    return {\n      ...results,\n      filename,\n    };\n  }\n\n  private async ssrLoadModuleContents(\n    filePath: string,\n    specificOptions: Partial<ExpoMetroOptions> = {}\n  ): Promise<SSRModuleContentsResult> {\n    const { baseUrl, routerRoot, isExporting } = this.instanceMetroOptions;\n    assert(\n      baseUrl != null && routerRoot != null && isExporting != null,\n      'The server must be started before calling ssrLoadModuleContents.'\n    );\n\n    const opts: ExpoMetroOptions = {\n      // TODO: Possibly issues with using an absolute path here...\n      mainModuleName: convertPathToModuleSpecifier(filePath),\n      lazy: false,\n      asyncRoutes: false,\n      inlineSourceMap: false,\n      engine: 'hermes',\n      minify: false,\n      bytecode: false,\n      // Bundle in Node.js mode for SSR unless RSC is enabled.\n      environment: this.isReactServerComponentsEnabled ? 'react-server' : 'node',\n      platform: 'web',\n      mode: 'development',\n      //\n      ...this.instanceMetroOptions,\n\n      // Mostly disable compiler in SSR bundles.\n      reactCompiler: false,\n      baseUrl,\n      routerRoot,\n      isExporting,\n\n      ...specificOptions,\n    };\n\n    // https://github.com/facebook/metro/blob/2405f2f6c37a1b641cc379b9c733b1eff0c1c2a1/packages/metro/src/lib/parseOptionsFromUrl.js#L55-L87\n    const { filename, bundle, map, ...rest } = await this.metroLoadModuleContents(filePath, opts);\n    const scriptContents = wrapBundle(bundle);\n\n    if (map) {\n      debug('Registering SSR source map for:', filename);\n      cachedSourceMaps.set(filename, { url: this.projectRoot, map });\n    } else {\n      debug('No SSR source map found for:', filename);\n    }\n\n    return {\n      ...rest,\n      src: scriptContents,\n      filename,\n      map,\n    };\n  }\n\n  async nativeExportBundleAsync(\n    exp: ExpoConfig,\n    options: Omit<\n      ExpoMetroOptions,\n      'routerRoot' | 'asyncRoutes' | 'isExporting' | 'serializerOutput' | 'environment'\n    >,\n    files: ExportAssetMap,\n    extraOptions: {\n      sourceMapUrl?: string;\n      unstable_transformProfile?: TransformProfile;\n    } = {}\n  ): Promise<{\n    artifacts: SerialAsset[];\n    assets: readonly BundleAssetWithFileHashes[];\n    files?: ExportAssetMap;\n  }> {\n    if (this.isReactServerComponentsEnabled) {\n      return this.singlePageReactServerComponentExportAsync(exp, options, files, extraOptions);\n    }\n\n    return this.legacySinglePageExportBundleAsync(options, extraOptions);\n  }\n\n  private async singlePageReactServerComponentExportAsync(\n    exp: ExpoConfig,\n    options: Omit<\n      ExpoMetroOptions,\n      'baseUrl' | 'routerRoot' | 'asyncRoutes' | 'isExporting' | 'serializerOutput' | 'environment'\n    >,\n    files: ExportAssetMap,\n    extraOptions: {\n      sourceMapUrl?: string;\n      unstable_transformProfile?: TransformProfile;\n    } = {}\n  ): Promise<{\n    artifacts: SerialAsset[];\n    assets: readonly BundleAssetWithFileHashes[];\n    files: ExportAssetMap;\n  }> {\n    const getReactServerReferences = (artifacts: SerialAsset[]): string[] => {\n      // Get the React server action boundaries from the client bundle.\n      return unique(\n        artifacts\n          .filter((a) => a.type === 'js')\n          .map((artifact) =>\n            artifact.metadata.reactServerReferences?.map((ref) => fileURLToFilePath(ref))\n          )\n          // TODO: Segment by module for splitting.\n          .flat()\n          .filter(Boolean) as string[]\n      );\n    };\n\n    // NOTE(EvanBacon): This will not support any code elimination since it's a static pass.\n    let {\n      reactClientReferences: clientBoundaries,\n      reactServerReferences: serverActionReferencesInServer,\n      cssModules,\n    } = await this.rscRenderer!.getExpoRouterClientReferencesAsync(\n      {\n        platform: options.platform,\n        domRoot: options.domRoot,\n      },\n      files\n    );\n\n    // TODO: The output keys should be in production format or use a lookup manifest.\n\n    const processClientBoundaries = async (\n      reactServerReferences: string[]\n    ): Promise<{\n      artifacts: SerialAsset[];\n      assets: readonly BundleAssetWithFileHashes[];\n    }> => {\n      debug('Evaluated client boundaries:', clientBoundaries);\n\n      // Run metro bundler and create the JS bundles/source maps.\n      const bundle = await this.legacySinglePageExportBundleAsync(\n        {\n          ...options,\n          clientBoundaries,\n        },\n        extraOptions\n      );\n\n      // Get the React server action boundaries from the client bundle.\n      const newReactServerReferences = getReactServerReferences(bundle.artifacts);\n\n      if (!newReactServerReferences) {\n        // Possible issue with babel plugin / metro-config.\n        throw new Error(\n          'Static server action references were not returned from the Metro client bundle'\n        );\n      }\n      debug('React server action boundaries from client:', newReactServerReferences);\n\n      const allKnownReactServerReferences = unique([\n        ...reactServerReferences,\n        ...newReactServerReferences,\n      ]);\n\n      // When we export the server actions that were imported from the client, we may need to re-bundle the client with the new client boundaries.\n      const { clientBoundaries: nestedClientBoundaries } =\n        await this.rscRenderer!.exportServerActionsAsync(\n          {\n            platform: options.platform,\n            domRoot: options.domRoot,\n            entryPoints: allKnownReactServerReferences,\n          },\n          files\n        );\n\n      // TODO: Check against all modules in the initial client bundles.\n      const hasUniqueClientBoundaries = nestedClientBoundaries.some(\n        (boundary) => !clientBoundaries.includes(boundary)\n      );\n\n      if (!hasUniqueClientBoundaries) {\n        return bundle;\n      }\n\n      debug('Re-bundling client with nested client boundaries:', nestedClientBoundaries);\n\n      clientBoundaries = unique(clientBoundaries.concat(nestedClientBoundaries));\n\n      // Re-bundle the client with the new client boundaries that only exist in server actions that were imported from the client.\n      // Run metro bundler and create the JS bundles/source maps.\n      return processClientBoundaries(allKnownReactServerReferences);\n    };\n\n    const bundle = await processClientBoundaries(serverActionReferencesInServer);\n\n    // Inject the global CSS that was imported during the server render.\n    bundle.artifacts.push(...cssModules);\n\n    const serverRoot = getMetroServerRoot(this.projectRoot);\n\n    // HACK: Maybe this should be done in the serializer.\n    const clientBoundariesAsOpaqueIds = clientBoundaries.map((boundary) =>\n      // NOTE(cedric): relative module specifiers / IDs should always be POSIX formatted\n      toPosixPath(path.relative(serverRoot, boundary))\n    );\n    const moduleIdToSplitBundle = (\n      bundle.artifacts\n        .map((artifact) => artifact?.metadata?.paths && Object.values(artifact.metadata.paths))\n        .filter(Boolean)\n        .flat() as Record<string, string>[]\n    ).reduce((acc, paths) => ({ ...acc, ...paths }), {});\n\n    debug('SSR Manifest:', moduleIdToSplitBundle, clientBoundariesAsOpaqueIds);\n\n    const ssrManifest = new Map<string, string>();\n\n    if (Object.keys(moduleIdToSplitBundle).length) {\n      clientBoundariesAsOpaqueIds.forEach((boundary) => {\n        if (boundary in moduleIdToSplitBundle) {\n          ssrManifest.set(boundary, moduleIdToSplitBundle[boundary]);\n        } else {\n          throw new Error(\n            `Could not find boundary \"${boundary}\" in the SSR manifest. Available: ${Object.keys(moduleIdToSplitBundle).join(', ')}`\n          );\n        }\n      });\n    } else {\n      // Native apps with bundle splitting disabled.\n      debug('No split bundles');\n      clientBoundariesAsOpaqueIds.forEach((boundary) => {\n        // @ts-expect-error\n        ssrManifest.set(boundary, null);\n      });\n    }\n\n    const routerOptions = exp.extra?.router;\n\n    // Export the static RSC files\n    await this.rscRenderer!.exportRoutesAsync(\n      {\n        platform: options.platform,\n        ssrManifest,\n        routerOptions,\n      },\n      files\n    );\n\n    // Save the SSR manifest so we can perform more replacements in the server renderer and with server actions.\n    files.set(`_expo/rsc/${options.platform}/ssr-manifest.js`, {\n      targetDomain: 'server',\n      contents:\n        'module.exports = ' +\n        JSON.stringify(\n          // TODO: Add a less leaky version of this across the framework with just [key, value] (module ID, chunk).\n          Object.fromEntries(\n            Array.from(ssrManifest.entries()).map(([key, value]) => [\n              // Must match babel plugin.\n              './' + toPosixPath(path.relative(this.projectRoot, path.join(serverRoot, key))),\n              [key, value],\n            ])\n          )\n        ),\n    });\n\n    return { ...bundle, files };\n  }\n\n  async legacySinglePageExportBundleAsync(\n    options: Omit<\n      ExpoMetroOptions,\n      'routerRoot' | 'asyncRoutes' | 'isExporting' | 'serializerOutput' | 'environment'\n    >,\n    extraOptions: {\n      sourceMapUrl?: string;\n      unstable_transformProfile?: TransformProfile;\n    } = {}\n  ): Promise<{ artifacts: SerialAsset[]; assets: readonly BundleAssetWithFileHashes[] }> {\n    const { baseUrl, routerRoot, isExporting } = this.instanceMetroOptions;\n    assert(options.mainModuleName != null, 'mainModuleName must be provided in options.');\n    assert(\n      baseUrl != null && routerRoot != null && isExporting != null,\n      'The server must be started before calling legacySinglePageExportBundleAsync.'\n    );\n\n    const opts: ExpoMetroOptions = {\n      ...this.instanceMetroOptions,\n      baseUrl,\n      routerRoot,\n      isExporting,\n      ...options,\n      environment: 'client',\n      serializerOutput: 'static',\n    };\n\n    // https://github.com/facebook/metro/blob/2405f2f6c37a1b641cc379b9c733b1eff0c1c2a1/packages/metro/src/lib/parseOptionsFromUrl.js#L55-L87\n    if (!opts.mainModuleName.startsWith('/') && !path.isAbsolute(opts.mainModuleName)) {\n      opts.mainModuleName = './' + opts.mainModuleName;\n    }\n\n    const output = await this.metroLoadModuleContents(opts.mainModuleName, opts, extraOptions);\n\n    return {\n      artifacts: output.artifacts!,\n      assets: output.assets!,\n    };\n  }\n\n  async watchEnvironmentVariables() {\n    if (!this.instance) {\n      throw new Error(\n        'Cannot observe environment variable changes without a running Metro instance.'\n      );\n    }\n    if (!this.metro) {\n      // This can happen when the run command is used and the server is already running in another\n      // process.\n      debug('Skipping Environment Variable observation because Metro is not running (headless).');\n      return;\n    }\n\n    const envFiles = runtimeEnv\n      .getFiles(process.env.NODE_ENV)\n      .map((fileName) => path.join(this.projectRoot, fileName));\n\n    observeFileChanges(\n      {\n        metro: this.metro,\n        server: this.instance.server,\n      },\n      envFiles,\n      () => {\n        debug('Reloading environment variables...');\n        // Force reload the environment variables.\n        runtimeEnv.load(this.projectRoot, { force: true });\n      }\n    );\n  }\n\n  rscRenderer: Awaited<ReturnType<typeof createServerComponentsMiddleware>> | null = null;\n\n  protected async startImplementationAsync(\n    options: BundlerStartOptions\n  ): Promise<DevServerInstance> {\n    options.port = await this.resolvePortAsync(options);\n    this.urlCreator = this.getUrlCreator(options);\n\n    const config = getConfig(this.projectRoot, { skipSDKVersionRequirement: true });\n    const { exp } = config;\n    // NOTE: This will change in the future when it's less experimental, we enable React 19, and turn on more RSC flags by default.\n    const isReactServerComponentsEnabled =\n      !!exp.experiments?.reactServerComponentRoutes || !!exp.experiments?.reactServerFunctions;\n    const isReactServerActionsOnlyEnabled =\n      !exp.experiments?.reactServerComponentRoutes && !!exp.experiments?.reactServerFunctions;\n    this.isReactServerComponentsEnabled = isReactServerComponentsEnabled;\n    this.isReactServerRoutesEnabled = !!exp.experiments?.reactServerComponentRoutes;\n\n    const useServerRendering = ['static', 'server'].includes(exp.web?.output ?? '');\n    const hasApiRoutes = isReactServerComponentsEnabled || exp.web?.output === 'server';\n    const baseUrl = getBaseUrlFromExpoConfig(exp);\n    const asyncRoutes = getAsyncRoutesFromExpoConfig(exp, options.mode ?? 'development', 'web');\n    const routerRoot = getRouterDirectoryModuleIdWithManifest(this.projectRoot, exp);\n    const reactCompiler = !!exp.experiments?.reactCompiler;\n    const appDir = path.join(this.projectRoot, routerRoot);\n    const mode = options.mode ?? 'development';\n\n    const routerOptions = exp.extra?.router;\n\n    if (isReactServerComponentsEnabled && exp.web?.output === 'static') {\n      throw new CommandError(\n        `Experimental server component support does not support 'web.output: ${exp.web!.output}' yet. Use 'web.output: \"server\"' during the experimental phase.`\n      );\n    }\n\n    // Error early about the window.location polyfill when React Server Components are enabled.\n    if (isReactServerComponentsEnabled && exp?.extra?.router?.origin === false) {\n      const configPath = config.dynamicConfigPath ?? config.staticConfigPath ?? '/app.json';\n      const configFileName = path.basename(configPath);\n      throw new CommandError(\n        `The Expo Router \"origin\" property in the Expo config (${configFileName}) cannot be \"false\" when React Server Components is enabled. Remove it from the ${configFileName} file and try again.`\n      );\n    }\n\n    const instanceMetroOptions = {\n      isExporting: !!options.isExporting,\n      baseUrl,\n      mode,\n      routerRoot,\n      reactCompiler,\n      minify: options.minify,\n      asyncRoutes,\n      // Options that are changing between platforms like engine, platform, and environment aren't set here.\n    };\n    this.instanceMetroOptions = instanceMetroOptions;\n\n    const parsedOptions = {\n      port: options.port,\n      maxWorkers: options.maxWorkers,\n      resetCache: options.resetDevServer,\n    };\n\n    // Required for symbolication:\n    process.env.EXPO_DEV_SERVER_ORIGIN = `http://localhost:${options.port}`;\n\n    const { metro, hmrServer, server, middleware, messageSocket } = await instantiateMetroAsync(\n      this,\n      parsedOptions,\n      {\n        isExporting: !!options.isExporting,\n        exp,\n      }\n    );\n\n    if (!options.isExporting) {\n      const manifestMiddleware = await this.getManifestMiddlewareAsync(options);\n\n      // Important that we noop source maps for context modules as soon as possible.\n      prependMiddleware(middleware, new ContextModuleSourceMapsMiddleware().getHandler());\n\n      // We need the manifest handler to be the first middleware to run so our\n      // routes take precedence over static files. For example, the manifest is\n      // served from '/' and if the user has an index.html file in their project\n      // then the manifest handler will never run, the static middleware will run\n      // and serve index.html instead of the manifest.\n      // https://github.com/expo/expo/issues/13114\n      prependMiddleware(middleware, manifestMiddleware.getHandler());\n\n      middleware.use(\n        new InterstitialPageMiddleware(this.projectRoot, {\n          // TODO: Prevent this from becoming stale.\n          scheme: options.location.scheme ?? null,\n        }).getHandler()\n      );\n      middleware.use(\n        new DevToolsPluginMiddleware(this.projectRoot, this.devToolsPluginManager).getHandler()\n      );\n\n      const deepLinkMiddleware = new RuntimeRedirectMiddleware(this.projectRoot, {\n        getLocation: ({ runtime }) => {\n          if (runtime === 'custom') {\n            return this.urlCreator?.constructDevClientUrl();\n          } else {\n            return this.urlCreator?.constructUrl({\n              scheme: 'exp',\n            });\n          }\n        },\n      });\n      middleware.use(deepLinkMiddleware.getHandler());\n\n      const serverRoot = getMetroServerRoot(this.projectRoot);\n\n      const domComponentRenderer = createDomComponentsMiddleware(\n        {\n          metroRoot: serverRoot,\n          projectRoot: this.projectRoot,\n        },\n        instanceMetroOptions\n      );\n      // Add support for DOM components.\n      // TODO: Maybe put behind a flag for now?\n      middleware.use(domComponentRenderer);\n\n      middleware.use(new CreateFileMiddleware(this.projectRoot).getHandler());\n\n      // Append support for redirecting unhandled requests to the index.html page on web.\n      if (this.isTargetingWeb()) {\n        // This MUST be after the manifest middleware so it doesn't have a chance to serve the template `public/index.html`.\n        middleware.use(new ServeStaticMiddleware(this.projectRoot).getHandler());\n\n        // This should come after the static middleware so it doesn't serve the favicon from `public/favicon.ico`.\n        middleware.use(new FaviconMiddleware(this.projectRoot).getHandler());\n      }\n\n      if (useServerRendering || isReactServerComponentsEnabled) {\n        observeAnyFileChanges(\n          {\n            metro,\n            server,\n          },\n          (events) => {\n            if (hasApiRoutes) {\n              // NOTE(EvanBacon): We aren't sure what files the API routes are using so we'll just invalidate\n              // aggressively to ensure we always have the latest. The only caching we really get here is for\n              // cases where the user is making subsequent requests to the same API route without changing anything.\n              // This is useful for testing but pretty suboptimal. Luckily our caching is pretty aggressive so it makes\n              // up for a lot of the overhead.\n              this.invalidateApiRouteCache();\n            } else if (!hasWarnedAboutApiRoutes()) {\n              for (const event of events) {\n                if (\n                  // If the user did not delete a file that matches the Expo Router API Route convention, then we should warn that\n                  // API Routes are not enabled in the project.\n                  event.metadata?.type !== 'd' &&\n                  // Ensure the file is in the project's routes directory to prevent false positives in monorepos.\n                  event.filePath.startsWith(appDir) &&\n                  isApiRouteConvention(event.filePath)\n                ) {\n                  warnInvalidWebOutput();\n                }\n              }\n            }\n          }\n        );\n      }\n\n      // If React 19 is enabled, then add RSC middleware to the dev server.\n      if (isReactServerComponentsEnabled) {\n        this.bindRSCDevModuleInjectionHandler();\n        const rscMiddleware = createServerComponentsMiddleware(this.projectRoot, {\n          instanceMetroOptions: this.instanceMetroOptions,\n          rscPath: '/_flight',\n          ssrLoadModule: this.ssrLoadModule.bind(this),\n          ssrLoadModuleArtifacts: this.metroImportAsArtifactsAsync.bind(this),\n          useClientRouter: isReactServerActionsOnlyEnabled,\n          createModuleId: metro._createModuleId.bind(metro),\n          routerOptions,\n        });\n        this.rscRenderer = rscMiddleware;\n        middleware.use(rscMiddleware.middleware);\n        this.onReloadRscEvent = rscMiddleware.onReloadRscEvent;\n      }\n\n      // Append support for redirecting unhandled requests to the index.html page on web.\n      if (this.isTargetingWeb()) {\n        if (!useServerRendering) {\n          // This MUST run last since it's the fallback.\n          middleware.use(\n            new HistoryFallbackMiddleware(manifestMiddleware.getHandler().internal).getHandler()\n          );\n        } else {\n          middleware.use(\n            createRouteHandlerMiddleware(this.projectRoot, {\n              appDir,\n              routerRoot,\n              config,\n              ...config.exp.extra?.router,\n              bundleApiRoute: (functionFilePath) =>\n                this.ssrImportApiRoute(functionFilePath, { platform: 'web' }),\n              getStaticPageAsync: async (pathname) => {\n                // TODO: Add server rendering when RSC is enabled.\n                if (isReactServerComponentsEnabled) {\n                  // NOTE: This is a temporary hack to return the SPA/template index.html in development when RSC is enabled.\n                  // While this technically works, it doesn't provide the correct experience of server rendering the React code to HTML first.\n                  const html = await manifestMiddleware.getSingleHtmlTemplateAsync();\n                  return { content: html };\n                }\n\n                // Non-RSC apps will bundle the static HTML for a given pathname and respond with it.\n                return this.getStaticPageAsync(pathname);\n              },\n            })\n          );\n        }\n      }\n    } else {\n      // If React 19 is enabled, then add RSC middleware to the dev server.\n      if (isReactServerComponentsEnabled) {\n        this.bindRSCDevModuleInjectionHandler();\n        const rscMiddleware = createServerComponentsMiddleware(this.projectRoot, {\n          instanceMetroOptions: this.instanceMetroOptions,\n          rscPath: '/_flight',\n          ssrLoadModule: this.ssrLoadModule.bind(this),\n          ssrLoadModuleArtifacts: this.metroImportAsArtifactsAsync.bind(this),\n          useClientRouter: isReactServerActionsOnlyEnabled,\n          createModuleId: metro._createModuleId.bind(metro),\n          routerOptions,\n        });\n        this.rscRenderer = rscMiddleware;\n      }\n    }\n    // Extend the close method to ensure that we clean up the local info.\n    const originalClose = server.close.bind(server);\n\n    server.close = (callback?: (err?: Error) => void) => {\n      return originalClose((err?: Error) => {\n        this.instance = null;\n        this.metro = null;\n        this.hmrServer = null;\n        this.ssrHmrClients = new Map();\n        callback?.(err);\n      });\n    };\n\n    assertMetroPrivateServer(metro);\n    this.metro = metro;\n    this.hmrServer = hmrServer;\n    return {\n      server,\n      location: {\n        // The port is the main thing we want to send back.\n        port: options.port,\n        // localhost isn't always correct.\n        host: 'localhost',\n        // http is the only supported protocol on native.\n        url: `http://localhost:${options.port}`,\n        protocol: 'http',\n      },\n      middleware,\n      messageSocket,\n    };\n  }\n\n  private onReloadRscEvent: ((platform: string) => void) | null = null;\n\n  private async registerSsrHmrAsync(url: string, onReload: (platform: string[]) => void) {\n    if (!this.hmrServer || this.ssrHmrClients.has(url)) {\n      return;\n    }\n\n    debug('[SSR] Register HMR:', url);\n\n    const sendFn = (message: string) => {\n      const data = JSON.parse(String(message)) as { type: string; body: any };\n\n      switch (data.type) {\n        case 'bundle-registered':\n        case 'update-done':\n        case 'update-start':\n          break;\n        case 'update':\n          {\n            const update = data.body;\n            const {\n              isInitialUpdate,\n              added,\n              modified,\n              deleted,\n            }: {\n              isInitialUpdate?: boolean;\n              added: {\n                module: [number | string, string];\n                sourceURL: string;\n                sourceMappingURL: string;\n              }[];\n              modified: {\n                module: [number | string, string];\n                sourceURL: string;\n                sourceMappingURL: string;\n              }[];\n              deleted: (number | string)[];\n            } = update;\n\n            const hasUpdate = added.length || modified.length || deleted.length;\n\n            // NOTE: We throw away the updates and instead simply send a trigger to the client to re-fetch the server route.\n            if (!isInitialUpdate && hasUpdate) {\n              // Clear all SSR modules before sending the reload event. This ensures that the next event will rebuild the in-memory state from scratch.\n              // @ts-expect-error\n              if (typeof globalThis.__c === 'function') globalThis.__c();\n\n              const allModuleIds = new Set(\n                [...added, ...modified].map((m) => m.module[0]).concat(deleted)\n              );\n\n              const platforms = unique(\n                Array.from(allModuleIds)\n                  .map((moduleId) => {\n                    if (typeof moduleId !== 'string') {\n                      return null;\n                    }\n                    // Extract platforms from the module IDs.\n                    return moduleId.match(/[?&]platform=([\\w]+)/)?.[1] ?? null;\n                  })\n                  .filter(Boolean)\n              ) as string[];\n\n              onReload(platforms);\n            }\n          }\n          break;\n        case 'error':\n          // GraphNotFound can mean that we have an issue in metroOptions where the URL doesn't match the object props.\n          Log.error('[SSR] HMR Error: ' + JSON.stringify(data, null, 2));\n\n          if (data.body?.type === 'GraphNotFoundError') {\n            Log.error(\n              'Available SSR HMR keys:',\n              // @ts-expect-error\n              (this.metro?._bundler._revisionsByGraphId as Map).keys()\n            );\n          }\n          break;\n        default:\n          debug('Unknown HMR message:', data);\n          break;\n      }\n    };\n\n    const client = await this.hmrServer!.onClientConnect(url, sendFn);\n    this.ssrHmrClients.set(url, client);\n    // Opt in...\n    client.optedIntoHMR = true;\n    await this.hmrServer!._registerEntryPoint(client, url, sendFn);\n  }\n\n  public async waitForTypeScriptAsync(): Promise<boolean> {\n    if (!this.instance) {\n      throw new Error('Cannot wait for TypeScript without a running server.');\n    }\n\n    return new Promise<boolean>((resolve) => {\n      if (!this.metro) {\n        // This can happen when the run command is used and the server is already running in another\n        // process. In this case we can't wait for the TypeScript check to complete because we don't\n        // have access to the Metro server.\n        debug('Skipping TypeScript check because Metro is not running (headless).');\n        return resolve(false);\n      }\n\n      const off = metroWatchTypeScriptFiles({\n        projectRoot: this.projectRoot,\n        server: this.instance!.server,\n        metro: this.metro,\n        tsconfig: true,\n        throttle: true,\n        eventTypes: ['change', 'add'],\n        callback: async () => {\n          // Run once, this prevents the TypeScript project prerequisite from running on every file change.\n          off();\n          const { TypeScriptProjectPrerequisite } = await import(\n            '../../doctor/typescript/TypeScriptProjectPrerequisite.js'\n          );\n\n          try {\n            const req = new TypeScriptProjectPrerequisite(this.projectRoot);\n            await req.bootstrapAsync();\n            resolve(true);\n          } catch (error: any) {\n            // Ensure the process doesn't fail if the TypeScript check fails.\n            // This could happen during the install.\n            Log.log();\n            Log.error(\n              chalk.red`Failed to automatically setup TypeScript for your project. Try restarting the dev server to fix.`\n            );\n            Log.exception(error);\n            resolve(false);\n          }\n        },\n      });\n    });\n  }\n\n  public async startTypeScriptServices() {\n    return startTypescriptTypeGenerationAsync({\n      server: this.instance?.server,\n      metro: this.metro,\n      projectRoot: this.projectRoot,\n    });\n  }\n\n  protected getConfigModuleIds(): string[] {\n    return ['./metro.config.js', './metro.config.json', './rn-cli.config.js'];\n  }\n\n  // API Routes\n\n  private pendingRouteOperations = new Map<string, Promise<SSRModuleContentsResult | null>>();\n\n  // Bundle the API Route with Metro and return the string contents to be evaluated in the server.\n  private async bundleApiRoute(\n    filePath: string,\n    { platform }: { platform: string }\n  ): Promise<SSRModuleContentsResult | null | undefined> {\n    if (this.pendingRouteOperations.has(filePath)) {\n      return this.pendingRouteOperations.get(filePath);\n    }\n    const bundleAsync = async (): Promise<SSRModuleContentsResult> => {\n      try {\n        debug('Bundle API route:', this.instanceMetroOptions.routerRoot, filePath);\n        return await this.ssrLoadModuleContents(filePath, {\n          isExporting: this.instanceMetroOptions.isExporting,\n          platform,\n        });\n      } catch (error: any) {\n        const appDir = this.instanceMetroOptions?.routerRoot\n          ? path.join(this.projectRoot, this.instanceMetroOptions!.routerRoot!)\n          : undefined;\n        const relativePath = appDir ? path.relative(appDir, filePath) : filePath;\n\n        // Expected errors: invalid syntax, missing resolutions.\n        // Wrap with command error for better error messages.\n        const err = new CommandError(\n          'API_ROUTE',\n          chalk`Failed to bundle API Route: {bold ${relativePath}}\\n\\n` + error.message\n        );\n\n        for (const key in error) {\n          // @ts-expect-error\n          err[key] = error[key];\n        }\n\n        throw err;\n      } finally {\n        // pendingRouteOperations.delete(filepath);\n      }\n    };\n    const route = bundleAsync();\n\n    this.pendingRouteOperations.set(filePath, route);\n    return route;\n  }\n\n  private async ssrImportApiRoute(\n    filePath: string,\n    { platform }: { platform: string }\n  ): Promise<null | Record<string, Function> | Response> {\n    // TODO: Cache the evaluated function.\n    try {\n      const apiRoute = await this.bundleApiRoute(filePath, { platform });\n\n      if (!apiRoute?.src) {\n        return null;\n      }\n      return evalMetroNoHandling(this.projectRoot, apiRoute.src, apiRoute.filename);\n    } catch (error) {\n      // Format any errors that were thrown in the global scope of the evaluation.\n      if (error instanceof Error) {\n        try {\n          const htmlServerError = await getErrorOverlayHtmlAsync({\n            error,\n            projectRoot: this.projectRoot,\n            routerRoot: this.instanceMetroOptions.routerRoot!,\n          });\n\n          return new Response(htmlServerError, {\n            status: 500,\n            headers: {\n              'Content-Type': 'text/html',\n            },\n          });\n        } catch (internalError) {\n          debug('Failed to generate Metro server error UI for API Route error:', internalError);\n          throw error;\n        }\n      } else {\n        throw error;\n      }\n    }\n  }\n\n  private invalidateApiRouteCache() {\n    this.pendingRouteOperations.clear();\n  }\n\n  // Ensure the global is available for SSR CSS modules to inject client updates.\n  private bindRSCDevModuleInjectionHandler() {\n    // Used by SSR CSS modules to broadcast client updates.\n    // @ts-expect-error\n    globalThis.__expo_rsc_inject_module = this.sendClientModule.bind(this);\n  }\n\n  // NOTE: This can only target a single platform at a time (web).\n  // used for sending RSC CSS to the root client in development.\n  private sendClientModule({ code, id }: { code: string; id: string }) {\n    this.broadcastMessage('sendDevCommand', {\n      name: 'module-import',\n      data: {\n        code,\n        id,\n      },\n    });\n  }\n\n  // Metro HMR\n\n  private setupHmr(url: URL) {\n    const onReload = (platforms: string[] = []) => {\n      // Send reload command to client from Fast Refresh code.\n\n      if (!platforms.length) {\n        // TODO: When is this called?\n        this.broadcastMessage('sendDevCommand', {\n          name: 'rsc-reload',\n        });\n      } else {\n        for (const platform of platforms) {\n          this.onReloadRscEvent?.(platform);\n          this.broadcastMessage('sendDevCommand', {\n            name: 'rsc-reload',\n            platform,\n          });\n        }\n      }\n    };\n\n    this.registerSsrHmrAsync(url.toString(), onReload);\n  }\n\n  // Direct Metro access\n\n  // Emulates the Metro dev server .bundle endpoint without having to go through a server.\n  private async _bundleDirectAsync(\n    resolvedEntryFilePath: string,\n    {\n      transformOptions,\n      resolverOptions,\n      graphOptions,\n      serializerOptions,\n    }: {\n      transformOptions: TransformInputOptions;\n      resolverOptions: {\n        customResolverOptions: CustomResolverOptions;\n        dev: boolean;\n      };\n      serializerOptions: {\n        modulesOnly: boolean;\n        runModule: boolean;\n        sourceMapUrl: string;\n        sourceUrl: string;\n        inlineSourceMap: boolean;\n        excludeSource: boolean;\n      };\n      graphOptions: {\n        shallow: boolean;\n        lazy: boolean;\n      };\n    }\n  ): Promise<BundleDirectResult> {\n    assert(this.metro, 'Metro server must be running to bundle directly.');\n    const config = this.metro._config;\n    const buildNumber = this.metro.getNewBuildNumber();\n    const bundlePerfLogger = config.unstable_perfLoggerFactory?.('BUNDLING_REQUEST', {\n      key: buildNumber,\n    });\n\n    const onProgress: MetroOnProgress = (transformedFileCount: number, totalFileCount: number) => {\n      this.metro?._reporter?.update?.({\n        buildID: getBuildID(buildNumber),\n        type: 'bundle_transform_progressed',\n        transformedFileCount,\n        totalFileCount,\n      });\n    };\n\n    const revPromise = this.getMetroRevision(resolvedEntryFilePath, {\n      graphOptions,\n      transformOptions,\n      resolverOptions,\n    });\n\n    bundlePerfLogger?.point('resolvingAndTransformingDependencies_start');\n    bundlePerfLogger?.annotate({\n      bool: {\n        initial_build: revPromise == null,\n      },\n    });\n    this.metro?._reporter.update({\n      buildID: getBuildID(buildNumber),\n      bundleDetails: {\n        bundleType: transformOptions.type,\n        dev: transformOptions.dev,\n        entryFile: resolvedEntryFilePath,\n        minify: transformOptions.minify,\n        platform: transformOptions.platform,\n        customResolverOptions: resolverOptions.customResolverOptions,\n        customTransformOptions: transformOptions.customTransformOptions ?? {},\n      },\n      isPrefetch: false,\n      type: 'bundle_build_started',\n    });\n\n    try {\n      let delta: DeltaResult;\n      let revision: GraphRevision;\n\n      try {\n        // TODO: Some bug in Metro/RSC causes this to break when changing imports in server components.\n        // We should resolve the bug because it results in ~6x faster bundling to reuse the graph revision.\n        if (transformOptions.customTransformOptions?.environment === 'react-server') {\n          const props = await this.metro.getBundler().initializeGraph(\n            // NOTE: Using absolute path instead of relative input path is a breaking change.\n            // entryFile,\n            resolvedEntryFilePath,\n\n            transformOptions,\n            resolverOptions,\n            {\n              onProgress,\n              shallow: graphOptions.shallow,\n              lazy: graphOptions.lazy,\n            }\n          );\n          delta = props.delta;\n          revision = props.revision;\n        } else {\n          const props = await (revPromise != null\n            ? this.metro.getBundler().updateGraph(await revPromise, false)\n            : this.metro.getBundler().initializeGraph(\n                // NOTE: Using absolute path instead of relative input path is a breaking change.\n                // entryFile,\n                resolvedEntryFilePath,\n\n                transformOptions,\n                resolverOptions,\n                {\n                  onProgress,\n                  shallow: graphOptions.shallow,\n                  lazy: graphOptions.lazy,\n                }\n              ));\n          delta = props.delta;\n          revision = props.revision;\n        }\n      } catch (error) {\n        if (error instanceof Error) {\n          // Space out build failures.\n          const cause = error.cause as undefined | { _expoImportStack?: string };\n          if (cause && '_expoImportStack' in cause) {\n            error.message += '\\n\\n' + cause._expoImportStack;\n          }\n        }\n\n        throw error;\n      }\n\n      bundlePerfLogger?.annotate({\n        int: {\n          graph_node_count: revision.graph.dependencies.size,\n        },\n      });\n      bundlePerfLogger?.point('resolvingAndTransformingDependencies_end');\n      bundlePerfLogger?.point('serializingBundle_start');\n\n      const shouldAddToIgnoreList = this.metro._shouldAddModuleToIgnoreList.bind(this.metro);\n\n      const serializer = this.getMetroSerializer();\n\n      const bundle = await serializer(\n        // NOTE: Using absolute path instead of relative input path is a breaking change.\n        // entryFile,\n        resolvedEntryFilePath,\n\n        revision.prepend as any,\n        revision.graph as any,\n        {\n          asyncRequireModulePath: await this.metro._resolveRelativePath(\n            config.transformer.asyncRequireModulePath,\n            {\n              relativeTo: 'project',\n              resolverOptions,\n              transformOptions,\n            }\n          ),\n          // ...serializerOptions,\n          processModuleFilter: config.serializer.processModuleFilter,\n          createModuleId: this.metro._createModuleId,\n          getRunModuleStatement: config.serializer.getRunModuleStatement,\n          includeAsyncPaths: graphOptions.lazy,\n          dev: transformOptions.dev,\n          projectRoot: config.projectRoot,\n          modulesOnly: serializerOptions.modulesOnly,\n          runBeforeMainModule: config.serializer.getModulesRunBeforeMainModule(\n            resolvedEntryFilePath\n            // path.relative(config.projectRoot, entryFile)\n          ),\n          runModule: serializerOptions.runModule,\n          sourceMapUrl: serializerOptions.sourceMapUrl,\n          sourceUrl: serializerOptions.sourceUrl,\n          inlineSourceMap: serializerOptions.inlineSourceMap,\n          serverRoot: config.server.unstable_serverRoot ?? config.projectRoot,\n          shouldAddToIgnoreList,\n\n          // @ts-expect-error: passed to our serializer to enable non-serial return values.\n          serializerOptions,\n        }\n      );\n\n      this.metro._reporter.update({\n        buildID: getBuildID(buildNumber),\n        type: 'bundle_build_done',\n      });\n\n      bundlePerfLogger?.point('serializingBundle_end');\n\n      let bundleCode: string | null = null;\n      let bundleMap: string | null = null;\n\n      // @ts-expect-error: If the output is multi-bundle...\n      if (serializerOptions.output === 'static') {\n        try {\n          const parsed = typeof bundle === 'string' ? JSON.parse(bundle) : bundle;\n\n          assert(\n            'artifacts' in parsed && Array.isArray(parsed.artifacts),\n            'Expected serializer to return an object with key artifacts to contain an array of serial assets.'\n          );\n\n          const artifacts = parsed.artifacts as SerialAsset[];\n          const assets = parsed.assets;\n\n          const bundleCode = artifacts.filter((asset) => asset.type === 'js')[0];\n          const bundleMap = artifacts.filter((asset) => asset.type === 'map')?.[0]?.source ?? '';\n\n          return {\n            numModifiedFiles: delta.reset\n              ? delta.added.size + revision.prepend.length\n              : delta.added.size + delta.modified.size + delta.deleted.size,\n            lastModifiedDate: revision.date,\n            nextRevId: revision.id,\n            bundle: bundleCode.source,\n            map: bundleMap,\n            artifacts,\n            assets,\n          };\n        } catch (error: any) {\n          throw new Error(\n            'Serializer did not return expected format. The project copy of `expo/metro-config` may be out of date. Error: ' +\n              error.message\n          );\n        }\n      }\n\n      if (typeof bundle === 'string') {\n        bundleCode = bundle;\n\n        // Create the source map in a second pass...\n        let { prepend, graph } = revision;\n        if (serializerOptions.modulesOnly) {\n          prepend = [];\n        }\n\n        bundleMap = await sourceMapStringAsync(\n          [\n            //\n            ...prepend,\n            ...this.metro._getSortedModules(graph),\n          ],\n          {\n            excludeSource: serializerOptions.excludeSource,\n            processModuleFilter: config.serializer.processModuleFilter,\n            shouldAddToIgnoreList,\n          }\n        );\n      } else {\n        bundleCode = bundle.code;\n        bundleMap = bundle.map;\n      }\n\n      return {\n        numModifiedFiles: delta.reset\n          ? delta.added.size + revision.prepend.length\n          : delta.added.size + delta.modified.size + delta.deleted.size,\n        lastModifiedDate: revision.date,\n        nextRevId: revision.id,\n        bundle: bundleCode,\n        map: bundleMap,\n      };\n    } catch (error) {\n      // Mark the error so we know how to format and return it later.\n      // @ts-expect-error\n      error[IS_METRO_BUNDLE_ERROR_SYMBOL] = true;\n\n      this.metro._reporter.update({\n        buildID: getBuildID(buildNumber),\n        type: 'bundle_build_failed',\n      });\n\n      throw error;\n    }\n  }\n\n  private getMetroSerializer() {\n    return (\n      this.metro?._config?.serializer.customSerializer ||\n      ((entryPoint, preModules, graph, options) =>\n        bundleToString(baseJSBundle(entryPoint, preModules, graph, options)).code)\n    );\n  }\n\n  private getMetroRevision(\n    resolvedEntryFilePath: string,\n    {\n      graphOptions,\n      transformOptions,\n      resolverOptions,\n    }: {\n      transformOptions: TransformInputOptions;\n      resolverOptions: {\n        customResolverOptions: CustomResolverOptions;\n        dev: boolean;\n      };\n      graphOptions: {\n        shallow: boolean;\n        lazy: boolean;\n      };\n    }\n  ) {\n    assert(this.metro, 'Metro server must be running to bundle directly.');\n    const config = this.metro._config;\n\n    const graphId = getGraphId(resolvedEntryFilePath, transformOptions, {\n      unstable_allowRequireContext: config.transformer.unstable_allowRequireContext,\n      resolverOptions,\n      shallow: graphOptions.shallow,\n      lazy: graphOptions.lazy,\n    });\n    return this.metro.getBundler().getRevisionByGraphId(graphId);\n  }\n\n  private async resolveRelativePathAsync(\n    moduleId: string,\n    {\n      resolverOptions,\n      transformOptions,\n    }: {\n      transformOptions: TransformInputOptions;\n      resolverOptions: {\n        customResolverOptions: CustomResolverOptions;\n        dev: boolean;\n      };\n    }\n  ) {\n    assert(this.metro, 'cannot invoke resolveRelativePathAsync without metro instance');\n    return await this.metro._resolveRelativePath(convertPathToModuleSpecifier(moduleId), {\n      relativeTo: 'server',\n      resolverOptions,\n      transformOptions,\n    });\n  }\n}\n\nfunction getBuildID(buildNumber: number): string {\n  return buildNumber.toString(36);\n}\n\nfunction wrapBundle(str: string) {\n  // Skip the metro runtime so debugging is a bit easier.\n  // Replace the __r() call with an export statement.\n  // Use gm to apply to the last require line. This is needed when the bundle has side-effects.\n  return str.replace(/^(__r\\(.*\\);)$/gm, 'module.exports = $1');\n}\n\nasync function sourceMapStringAsync(\n  modules: readonly import('metro/src/DeltaBundler/types').Module<any>[],\n  options: SourceMapGeneratorOptions\n): Promise<string> {\n  return (await sourceMapGeneratorNonBlocking(modules, options)).toString(undefined, {\n    excludeSource: options.excludeSource,\n  });\n}\n\nfunction unique<T>(array: T[]): T[] {\n  return Array.from(new Set(array));\n}\n"], "names": ["MetroBundlerDevServer", "debug", "require", "EXPO_GO_METRO_PORT", "DEV_CLIENT_METRO_PORT", "BundlerDevServer", "name", "resolvePortAsync", "options", "port", "devClient", "Number", "process", "env", "RCT_METRO_PORT", "getFreePortAsync", "exportExpoRouterApiRoutesAsync", "includeSourceMaps", "outputDir", "prerenderManifest", "platform", "routerRoot", "instanceMetroOptions", "assert", "appDir", "path", "join", "projectRoot", "manifest", "getExpoRouterRoutesManifestAsync", "files", "Map", "rscPath", "isReactServerComponentsEnabled", "apiRoutes", "find", "route", "page", "startsWith", "push", "file", "resolveFrom", "namedRegex", "routeKeys", "rsc", "filepath", "isAbsolute", "contents", "bundleApiRoute", "artifactFilename", "convertPathToModuleSpecifier", "relative", "replace", "src", "map", "artifactBasename", "encodeURIComponent", "basename", "parsedMap", "JSON", "parse", "set", "stringify", "version", "sources", "source", "sourcesContent", "Array", "length", "fill", "names", "mappings", "apiRouteId", "targetDomain", "htmlRoutes", "exp", "getConfig", "fetchManifest", "extra", "router", "preserveRedirectAndRewrites", "as<PERSON><PERSON>", "CommandError", "getServerManifestAsync", "getBuildTimeServerManifestAsync", "getManifest", "ssrLoadModule", "environment", "isReactServerRoutesEnabled", "serverManifest", "htmlManifest", "getStaticRenderFunctionAsync", "url", "getDevServerUrlOrAssert", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "preserveApiRoutes", "renderAsync", "URL", "getStaticResourcesAsync", "mainModuleName", "clientBoundaries", "mode", "minify", "isExporting", "baseUrl", "reactCompiler", "asyncRoutes", "resolvedMainModuleName", "resolveMainModuleName", "metroImportAsArtifactsAsync", "splitChunks", "EXPO_NO_BUNDLE_SPLITTING", "serializerIncludeMaps", "lazy", "shouldEnableAsyncImports", "bytecode", "getStaticPageAsync", "pathname", "devBundleUrlPathname", "createBundleUrlPath", "bundleStaticHtml", "location", "artifacts", "resources", "staticHtml", "Promise", "all", "content", "serializeHtmlWithAssets", "template", "devBundleUrl", "hydrate", "EXPO_WEB_DEV_HYDRATE", "filePath", "specificOptions", "results", "ssrLoadModuleContents", "serializerOutput", "assets", "filename", "metroLoadModuleContents", "extraOptions", "opts", "inlineSourceMap", "engine", "expoBundleOptions", "getMetroDirectBundleOptions", "resolverOptions", "customResolverOptions", "dev", "transformOptions", "hot", "type", "unstable_transformProfile", "customTransformOptions", "Object", "create", "runtimeBytecodeVersion", "resolvedEntryFilePath", "resolveRelativePathAsync", "createBundleOsPath", "_bundleDirectAsync", "graphOptions", "shallow", "serializerOptions", "modulesOnly", "runModule", "sourceUrl", "sourceMapUrl", "bundle", "rest", "scriptContents", "wrapBundle", "cachedSourceMaps", "nativeExportBundleAsync", "singlePageReactServerComponentExportAsync", "legacySinglePageExportBundleAsync", "getReactServerReferences", "unique", "filter", "a", "artifact", "metadata", "reactServerReferences", "ref", "fileURLToFilePath", "flat", "Boolean", "reactClientReferences", "serverActionReferencesInServer", "cssModules", "rsc<PERSON><PERSON><PERSON>", "getExpoRouterClientReferencesAsync", "domRoot", "processClientBoundaries", "newReactServerReferences", "Error", "allKnownReactServerReferences", "nestedClientBoundaries", "exportServerActionsAsync", "entryPoints", "hasUniqueClientBoundaries", "some", "boundary", "includes", "concat", "serverRoot", "getMetroServerRoot", "clientBoundariesAsOpaqueIds", "toPosixPath", "moduleIdToSplitBundle", "paths", "values", "reduce", "acc", "ssrManifest", "keys", "for<PERSON>ach", "routerOptions", "exportRoutesAsync", "fromEntries", "from", "entries", "key", "value", "output", "watchEnvironmentVariables", "instance", "metro", "envFiles", "runtimeEnv", "getFiles", "NODE_ENV", "fileName", "observeFileChanges", "server", "load", "force", "startImplementationAsync", "urlCreator", "getUrlCreator", "config", "skipSDKVersionRequirement", "experiments", "reactServerComponentRoutes", "reactServerFunctions", "isReactServerActionsOnlyEnabled", "useServerRendering", "web", "hasApiRoutes", "getBaseUrlFromExpoConfig", "getAsyncRoutesFromExpoConfig", "getRouterDirectoryModuleIdWithManifest", "origin", "config<PERSON><PERSON>", "dynamicConfigPath", "staticConfigPath", "configFileName", "parsedOptions", "maxWorkers", "resetCache", "resetDevServer", "EXPO_DEV_SERVER_ORIGIN", "hmrServer", "middleware", "messageSocket", "instantiateMetroAsync", "manifestMiddleware", "getManifestMiddlewareAsync", "prependMiddleware", "ContextModuleSourceMapsMiddleware", "<PERSON><PERSON><PERSON><PERSON>", "use", "InterstitialPageMiddleware", "scheme", "DevToolsPluginMiddleware", "devToolsPluginManager", "deepLinkMiddleware", "RuntimeRedirectMiddleware", "getLocation", "runtime", "constructDevClientUrl", "constructUrl", "domComponent<PERSON><PERSON><PERSON>", "createDomComponentsMiddleware", "metroRoot", "CreateFileMiddleware", "isTargetingWeb", "ServeStaticMiddleware", "FaviconMiddleware", "observeAnyFileChanges", "events", "invalidateApiRouteCache", "hasWarnedAboutApiRoutes", "event", "isApiRouteConvention", "warnInvalidWebOutput", "bindRSCDevModuleInjectionHandler", "rscMiddleware", "createServerComponentsMiddleware", "bind", "ssrLoadModuleArtifacts", "useClientRouter", "createModuleId", "_createModuleId", "onReloadRscEvent", "HistoryFallbackMiddleware", "internal", "createRouteHandlerMiddleware", "functionFilePath", "ssrImportApiRoute", "html", "getSingleHtmlTemplateAsync", "originalClose", "close", "callback", "err", "ssrHmrClients", "assertMetroPrivateServer", "host", "protocol", "registerSsrHmrAsync", "onReload", "has", "sendFn", "message", "data", "String", "update", "body", "isInitialUpdate", "added", "modified", "deleted", "hasUpdate", "globalThis", "__c", "allModuleIds", "Set", "m", "module", "platforms", "moduleId", "match", "Log", "error", "_bundler", "_revisionsByGraphId", "client", "onClientConnect", "optedIntoHMR", "_registerEntryPoint", "waitForTypeScriptAsync", "resolve", "off", "metroWatchTypeScriptFiles", "tsconfig", "throttle", "eventTypes", "TypeScriptProjectPrerequisite", "req", "bootstrapAsync", "log", "chalk", "red", "exception", "startTypeScriptServices", "startTypescriptTypeGenerationAsync", "getConfigModuleIds", "pendingRouteOperations", "get", "bundleAsync", "undefined", "relativePath", "apiRoute", "evalMetroNoHandling", "htmlServerError", "getErrorOverlayHtmlAsync", "Response", "status", "headers", "internalError", "clear", "__expo_rsc_inject_module", "sendClientModule", "code", "id", "broadcastMessage", "setupHmr", "toString", "_config", "buildNumber", "getNewBuildNumber", "bundlePerfLogger", "unstable_perfLoggerFactory", "onProgress", "transformedFileCount", "totalFileCount", "_reporter", "buildID", "getBuildID", "revPromise", "getMetroRevision", "point", "annotate", "bool", "initial_build", "bundleDetails", "bundleType", "entryFile", "isPrefetch", "delta", "revision", "props", "getBundler", "initializeGraph", "updateGraph", "cause", "_expoImportStack", "int", "graph_node_count", "graph", "dependencies", "size", "shouldAddToIgnoreList", "_shouldAddModuleToIgnoreList", "serializer", "getMetroSerializer", "prepend", "asyncRequireModulePath", "_resolveRelativePath", "transformer", "relativeTo", "processModuleFilter", "getRunModuleStatement", "includeAsyncPaths", "runBeforeMainModule", "getModulesRunBeforeMainModule", "unstable_serverRoot", "bundleCode", "bundleMap", "parsed", "isArray", "asset", "numModifiedFiles", "reset", "lastModifiedDate", "date", "nextRevId", "sourceMapStringAsync", "_getSortedModules", "excludeSource", "IS_METRO_BUNDLE_ERROR_SYMBOL", "customSerializer", "entryPoint", "preModules", "bundleToString", "baseJSBundle", "graphId", "getGraphId", "unstable_allowRequireContext", "getRevisionByGraphId", "extras", "res", "evalMetroAndWrapFunctions", "str", "modules", "sourceMapGeneratorNonBlocking", "array"], "mappings": "AAAA;;;;;CAKC;;;;+BAqHYA;;;eAAAA;;;;yBApHyB;;;;;;;yBACH;;;;;;;iEACP;;;;;;;gEAET;;;;;;;gEACD;;;;;;;gEAEO;;;;;;;yBAIlB;;;;;;;gEAIoB;;;;;;;gEACJ;;;;;;;gEAGN;;;;;;;gEACO;;;;;;kDAKjB;6CACsC;qCACa;kCACpB;qCACiC;oCACV;2CACnB;wBAMnC;+BACiC;qDACkB;qBAEtC;sBACA;wBACS;0BACD;sBACK;kCACwC;0CAKlE;mDAC2C;sCACb;0CACI;yCACK;mCACZ;2CACQ;4CACC;oCACL;2CACI;uCACJ;8BAU/B;2BAC2B;+CACiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCnD,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,qDAAqD,GACrD,MAAMC,qBAAqB;AAE3B,iGAAiG,GACjG,MAAMC,wBAAwB;AAEvB,MAAMJ,8BAA8BK,kCAAgB;IAOzD,IAAIC,OAAe;QACjB,OAAO;IACT;IAEA,MAAMC,iBAAiBC,UAAwC,CAAC,CAAC,EAAmB;QAClF,MAAMC,OACJ,yEAAyE;QACzED,QAAQC,IAAI,IACZ,8DAA8D;QAC7DD,CAAAA,QAAQE,SAAS,GAEdC,OAAOC,QAAQC,GAAG,CAACC,cAAc,KAAKV,wBAEtC,MAAMW,IAAAA,sBAAgB,EAACZ,mBAAkB;QAE/C,OAAOM;IACT;IAEA,MAAMO,+BAA+B,EACnCC,iBAAiB,EACjBC,SAAS,EACTC,iBAAiB,EACjBC,QAAQ,EAOT,EAAoF;QACnF,MAAM,EAAEC,UAAU,EAAE,GAAG,IAAI,CAACC,oBAAoB;QAChDC,IAAAA,iBAAM,EACJF,cAAc,MACd;QAGF,MAAMG,SAASC,eAAI,CAACC,IAAI,CAAC,IAAI,CAACC,WAAW,EAAEN;QAC3C,MAAMO,WAAW,MAAM,IAAI,CAACC,gCAAgC,CAAC;YAAEL;QAAO;QAEtE,MAAMM,QAAwB,IAAIC;QAElC,yBAAyB;QACzB,MAAMC,UAAU;QAEhB,IACE,IAAI,CAACC,8BAA8B,IACnC,2DAA2D;QAC3D,CAACL,SAASM,SAAS,CAACC,IAAI,CAAC,CAACC,QAAUA,MAAMC,IAAI,CAACC,UAAU,CAAC,eAC1D;YACArC,MAAM,qCAAqC+B;YAC3C,wEAAwE;YACxEJ,SAASM,SAAS,CAACK,IAAI,CAAC;gBACtBC,MAAMC,IAAAA,sBAAW,EAAC,IAAI,CAACd,WAAW,EAAE;gBACpCU,MAAML;gBACNU,YAAY;gBACZC,WAAW;oBAAEC,KAAK;gBAAM;YAC1B;QACF;QAEA,KAAK,MAAMR,SAASR,SAASM,SAAS,CAAE;YACtC,MAAMW,WAAWpB,eAAI,CAACqB,UAAU,CAACV,MAAMI,IAAI,IAAIJ,MAAMI,IAAI,GAAGf,eAAI,CAACC,IAAI,CAACF,QAAQY,MAAMI,IAAI;YACxF,MAAMO,WAAW,MAAM,IAAI,CAACC,cAAc,CAACH,UAAU;gBAAEzB;YAAS;YAEhE,MAAM6B,mBACJb,MAAMC,IAAI,KAAKL,UAEXkB,IAAAA,0CAA4B,EAACzB,eAAI,CAACC,IAAI,CAACR,WAAW,MAAMc,UAAU,UAClEkB,IAAAA,0CAA4B,EAC1BzB,eAAI,CAACC,IAAI,CAACR,WAAWO,eAAI,CAAC0B,QAAQ,CAAC3B,QAAQqB,SAASO,OAAO,CAAC,cAAc;YAGlF,IAAIL,UAAU;gBACZ,IAAIM,MAAMN,SAASM,GAAG;gBAEtB,IAAIpC,qBAAqB8B,SAASO,GAAG,EAAE;oBACrC,+DAA+D;oBAC/D,uHAAuH;oBACvH,wDAAwD;oBACxD,MAAMC,mBAAmBC,mBAAmB/B,eAAI,CAACgC,QAAQ,CAACR,oBAAoB;oBAC9EI,MAAMA,IAAID,OAAO,CACf,8BACA,CAAC,qBAAqB,EAAEG,kBAAkB;oBAG5C,MAAMG,YACJ,OAAOX,SAASO,GAAG,KAAK,WAAWK,KAAKC,KAAK,CAACb,SAASO,GAAG,IAAIP,SAASO,GAAG;oBAC5ExB,MAAM+B,GAAG,CAACZ,mBAAmB,QAAQ;wBACnCF,UAAUY,KAAKG,SAAS,CAAC;4BACvBC,SAASL,UAAUK,OAAO;4BAC1BC,SAASN,UAAUM,OAAO,CAACV,GAAG,CAAC,CAACW;gCAC9BA,SACE,OAAOA,WAAW,YAAYA,OAAO3B,UAAU,CAAC,IAAI,CAACX,WAAW,IAC5DF,eAAI,CAAC0B,QAAQ,CAAC,IAAI,CAACxB,WAAW,EAAEsC,UAChCA;gCACN,OAAOf,IAAAA,0CAA4B,EAACe;4BACtC;4BACAC,gBAAgB,IAAIC,MAAMT,UAAUM,OAAO,CAACI,MAAM,EAAEC,IAAI,CAAC;4BACzDC,OAAOZ,UAAUY,KAAK;4BACtBC,UAAUb,UAAUa,QAAQ;wBAC9B;wBACAC,YAAYpC,MAAMC,IAAI;wBACtBoC,cAAc;oBAChB;gBACF;gBACA3C,MAAM+B,GAAG,CAACZ,kBAAkB;oBAC1BF,UAAUM;oBACVmB,YAAYpC,MAAMC,IAAI;oBACtBoC,cAAc;gBAChB;YACF;YACA,0DAA0D;YAC1DrC,MAAMI,IAAI,GAAGS;QACf;QAEA,OAAO;YACLrB,UAAU;gBACR,GAAGA,QAAQ;gBACX8C,YAAYvD,kBAAkBuD,UAAU;YAC1C;YACA5C;QACF;IACF;IAEA,MAAMD,iCAAiC,EAAEL,MAAM,EAAsB,EAAE;YAIhEmD;QAHL,6BAA6B;QAC7B,MAAM,EAAEA,GAAG,EAAE,GAAGC,IAAAA,mBAAS,EAAC,IAAI,CAACjD,WAAW;QAC1C,MAAMC,WAAW,MAAMiD,IAAAA,kCAAa,EAAC,IAAI,CAAClD,WAAW,EAAE;gBAClDgD,aAAAA,IAAIG,KAAK,qBAATH,WAAWI,MAAM,AAApB;YACAC,6BAA6B;YAC7BC,QAAQ;YACRzD;QACF;QAEA,IAAI,CAACI,UAAU;YACb,MAAM,IAAIsD,oBAAY,CACpB,+BACA;QAEJ;QAEA,OAAOtD;IACT;IAEA,MAAMuD,yBAGH;YAW4DR,YACtBA;QAXvC,MAAM,EAAEA,GAAG,EAAE,GAAGC,IAAAA,mBAAS,EAAC,IAAI,CAACjD,WAAW;QAC1C,4GAA4G;QAC5G,MAAM,EAAEyD,+BAA+B,EAAEC,WAAW,EAAE,GAAG,MAAM,IAAI,CAACC,aAAa,CAE/E,iDAAiD;YACjD,iGAAiG;YACjGC,aAAa,IAAI,CAACC,0BAA0B,GAAG,iBAAiB;QAClE;QAEA,OAAO;YACLC,gBAAgB,MAAML,gCAAgC;oBAAKT,aAAAA,IAAIG,KAAK,qBAATH,WAAWI,MAAM,AAApB;YAAqB;YAC7EW,cAAc,MAAML,YAAY;oBAAKV,cAAAA,IAAIG,KAAK,qBAATH,YAAWI,MAAM,AAApB;YAAqB;QACzD;IACF;IAEA,MAAMY,+BAIH;YAiBMhB,YAGsDA;QAnB7D,MAAMiB,MAAM,IAAI,CAACC,uBAAuB;QAExC,MAAM,EAAEC,gBAAgB,EAAET,WAAW,EAAED,+BAA+B,EAAE,GACtE,MAAM,IAAI,CAACE,aAAa,CACtB,8BACA;YACE,gGAAgG;YAChG,uEAAuE;YACvEC,aAAa;QACf;QAGJ,MAAM,EAAEZ,GAAG,EAAE,GAAGC,IAAAA,mBAAS,EAAC,IAAI,CAACjD,WAAW;QAE1C,OAAO;YACL8D,gBAAgB,MAAML,gCAAgC;oBACjDT,aAAAA,IAAIG,KAAK,qBAATH,WAAWI,MAAM,AAApB;YACF;YACA,+BAA+B;YAC/BnD,UAAU,MAAMyD,YAAY;gBAAEU,mBAAmB;oBAAUpB,cAAAA,IAAIG,KAAK,qBAATH,YAAWI,MAAM,AAApB;YAAqB;YAC7E,gCAAgC;YAChC,MAAMiB,aAAYvE,IAAY;gBAC5B,OAAO,MAAMqE,iBAAiB,IAAIG,IAAIxE,MAAMmE;YAC9C;QACF;IACF;IAEA,MAAMM,wBAAwB,EAC5BjF,iBAAiB,EACjBkF,cAAc,EACdC,mBAAmB,IAAI,CAAC9E,oBAAoB,CAAC8E,gBAAgB,IAAI,EAAE,EACnEhF,WAAW,KAAK,EAMjB,GAAG,CAAC,CAAC,EAAE;QACN,MAAM,EAAEiF,IAAI,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAEC,aAAa,EAAEpF,UAAU,EAAEqF,WAAW,EAAE,GAClF,IAAI,CAACpF,oBAAoB;QAC3BC,IAAAA,iBAAM,EACJ8E,QAAQ,QACNE,eAAe,QACfC,WAAW,QACXnF,cAAc,QACdoF,iBAAiB,QACjBC,eAAe,MACjB;QAGF,MAAMC,yBACJR,kBAAkB,OAAOS,IAAAA,yCAAqB,EAAC,IAAI,CAACjF,WAAW,EAAE;YAAEP;QAAS;QAC9E,OAAO,MAAM,IAAI,CAACyF,2BAA2B,CAACF,wBAAwB;YACpEG,aAAaP,eAAe,CAAC1F,SAAG,CAACkG,wBAAwB;YACzD3F;YACAiF;YACAC;YACAf,aAAa;YACbyB,uBAAuB/F;YACvBkF,gBAAgBQ;YAChBM,MAAMC,IAAAA,sCAAwB,EAAC,IAAI,CAACvF,WAAW;YAC/C+E;YACAF;YACAD;YACAlF;YACA+E;YACAK;YACAU,UAAU;QACZ;IACF;IAEA,MAAcC,mBAAmBC,QAAgB,EAAE;QACjD,MAAM,EAAEhB,IAAI,EAAEE,WAAW,EAAEH,gBAAgB,EAAEI,OAAO,EAAEC,aAAa,EAAEpF,UAAU,EAAEqF,WAAW,EAAE,GAC5F,IAAI,CAACpF,oBAAoB;QAC3BC,IAAAA,iBAAM,EACJ8E,QAAQ,QACNE,eAAe,QACfC,WAAW,QACXC,iBAAiB,QACjBpF,cAAc,QACdqF,eAAe,MACjB;QAEF,MAAMtF,WAAW;QAEjB,MAAMkG,uBAAuBC,IAAAA,iCAAmB,EAAC;YAC/CT,aAAaP,eAAe,CAAC1F,SAAG,CAACkG,wBAAwB;YACzD3F;YACAiF;YACAd,aAAa;YACbkB;YACAN,gBAAgBS,IAAAA,yCAAqB,EAAC,IAAI,CAACjF,WAAW,EAAE;gBAAEP;YAAS;YACnE6F,MAAMC,IAAAA,sCAAwB,EAAC,IAAI,CAACvF,WAAW;YAC/C6E;YACAD;YACAG;YACArF;YACA+E;YACAe,UAAU;QACZ;QAEA,MAAMK,mBAAmB;YACvB,MAAM,EAAE1B,gBAAgB,EAAE,GAAG,MAAM,IAAI,CAACR,aAAa,CAEnD,8BAA8B;gBAC9B,gGAAgG;gBAChG,uEAAuE;gBACvEC,aAAa;gBACbe,QAAQ;gBACRC;gBACAnF;YACF;YAEA,MAAMqG,WAAW,IAAIxB,IAAIoB,UAAU,IAAI,CAACxB,uBAAuB;YAC/D,OAAO,MAAMC,iBAAiB2B;QAChC;QAEA,MAAM,CAAC,EAAEC,WAAWC,SAAS,EAAE,EAAEC,WAAW,GAAG,MAAMC,QAAQC,GAAG,CAAC;YAC/D,IAAI,CAAC5B,uBAAuB,CAAC;gBAC3BE,kBAAkB,EAAE;YACtB;YACAoB;SACD;QACD,MAAMO,UAAUC,IAAAA,sCAAuB,EAAC;YACtCzB;YACAoB;YACAM,UAAUL;YACVM,cAAcZ;YACdd;YACA2B,SAAStH,SAAG,CAACuH,oBAAoB;QACnC;QACA,OAAO;YACLL;YACAJ;QACF;IACF;IAgCA,MAAcd,4BACZwB,QAAgB,EAChBC,kBAAuE,CAAC,CAAC,EACzE;QACA,MAAMC,UAAU,MAAM,IAAI,CAACC,qBAAqB,CAACH,UAAU;YACzDI,kBAAkB;YAClB,GAAGH,eAAe;QACpB;QAEA,mEAAmE;QACnE,IAAIC,QAAQb,SAAS,IAAIa,QAAQG,MAAM,EAAE;YACvC,OAAO;gBACLhB,WAAWa,QAAQb,SAAS;gBAC5BgB,QAAQH,QAAQG,MAAM;gBACtBrF,KAAKkF,QAAQlF,GAAG;gBAChBsF,UAAUJ,QAAQI,QAAQ;gBAC1BrF,KAAKiF,QAAQjF,GAAG;YAClB;QACF;QACA,MAAM,IAAI4B,oBAAY,CAAC,8BAA8BqD;IACvD;IAEA,MAAcK,wBACZP,QAAgB,EAChBC,eAAiC,EACjCO,eAGI,CAAC,CAAC,EAC8B;QACpC,MAAM,EAAErC,OAAO,EAAE,GAAG,IAAI,CAAClF,oBAAoB;QAC7CC,IAAAA,iBAAM,EAACiF,WAAW,MAAM;QAExB,MAAMsC,OAAyB;YAC7B,4DAA4D;YAC5D,4BAA4B;YAC5B7B,MAAM;YACNP,aAAa;YACbqC,iBAAiB;YACjBC,QAAQ;YACR1C,QAAQ;YACR,mBAAmB;YACnB,kCAAkC;YAClCf,aAAa;YACb,mBAAmB;YACnB,uBAAuB;YACvB,EAAE;YACF,GAAG,IAAI,CAACjE,oBAAoB;YAC5BkF;YACA,cAAc;YACd,eAAe;YACf,GAAG8B,eAAe;QACpB;QAEA,MAAMW,oBAAoBC,IAAAA,yCAA2B,EAACJ;QAEtD,MAAMK,kBAAkB;YACtBC,uBAAuBH,kBAAkBG,qBAAqB,IAAI,CAAC;YACnEC,KAAKJ,kBAAkBI,GAAG,IAAI;QAChC;QAEA,MAAMC,mBAA0C;YAC9CD,KAAKJ,kBAAkBI,GAAG,IAAI;YAC9BE,KAAK;YACLjD,QAAQ2C,kBAAkB3C,MAAM,IAAI;YACpCkD,MAAM;YACNC,2BACEZ,aAAaY,yBAAyB,IACtCR,kBAAkBQ,yBAAyB,IAC3C;YACFC,wBAAwBT,kBAAkBS,sBAAsB,IAAIC,OAAOC,MAAM,CAAC;YAClFxI,UAAU6H,kBAAkB7H,QAAQ,IAAI;YACxC,8GAA8G;YAC9GyI,wBAAwBZ,kBAAkBY,sBAAsB;QAClE;QAEA,MAAMC,wBAAwB,MAAM,IAAI,CAACC,wBAAwB,CAAC1B,UAAU;YAC1Ec;YACAG;QACF;QAEA,MAAMX,WAAWqB,IAAAA,gCAAkB,EAAC;YAClC,GAAGlB,IAAI;YACP3C,gBAAgB2D;QAClB;QAEA,wIAAwI;QACxI,MAAMvB,UAAU,MAAM,IAAI,CAAC0B,kBAAkB,CAACH,uBAAuB;YACnEI,cAAc;gBACZjD,MAAMgC,kBAAkBhC,IAAI,IAAI;gBAChCkD,SAASlB,kBAAkBkB,OAAO,IAAI;YACxC;YACAhB;YACAiB,mBAAmB;gBACjB,GAAGnB,kBAAkBmB,iBAAiB;gBAEtCrB,iBAAiBE,kBAAkBF,eAAe,IAAI;gBACtDsB,aAAapB,kBAAkBoB,WAAW,IAAI;gBAC9CC,WAAWrB,kBAAkBqB,SAAS,IAAI;gBAC1C,mBAAmB;gBACnBC,WAAWtB,kBAAkBsB,SAAS;gBACtC,mBAAmB;gBACnBC,cAAc3B,aAAa2B,YAAY,IAAIvB,kBAAkBuB,YAAY;YAC3E;YACAlB;QACF;QAEA,OAAO;YACL,GAAGf,OAAO;YACVI;QACF;IACF;IAEA,MAAcH,sBACZH,QAAgB,EAChBC,kBAA6C,CAAC,CAAC,EACb;QAClC,MAAM,EAAE9B,OAAO,EAAEnF,UAAU,EAAEkF,WAAW,EAAE,GAAG,IAAI,CAACjF,oBAAoB;QACtEC,IAAAA,iBAAM,EACJiF,WAAW,QAAQnF,cAAc,QAAQkF,eAAe,MACxD;QAGF,MAAMuC,OAAyB;YAC7B,4DAA4D;YAC5D3C,gBAAgBjD,IAAAA,0CAA4B,EAACmF;YAC7CpB,MAAM;YACNP,aAAa;YACbqC,iBAAiB;YACjBC,QAAQ;YACR1C,QAAQ;YACRa,UAAU;YACV,wDAAwD;YACxD5B,aAAa,IAAI,CAACtD,8BAA8B,GAAG,iBAAiB;YACpEb,UAAU;YACViF,MAAM;YACN,EAAE;YACF,GAAG,IAAI,CAAC/E,oBAAoB;YAE5B,0CAA0C;YAC1CmF,eAAe;YACfD;YACAnF;YACAkF;YAEA,GAAG+B,eAAe;QACpB;QAEA,wIAAwI;QACxI,MAAM,EAAEK,QAAQ,EAAE8B,MAAM,EAAEnH,GAAG,EAAE,GAAGoH,MAAM,GAAG,MAAM,IAAI,CAAC9B,uBAAuB,CAACP,UAAUS;QACxF,MAAM6B,iBAAiBC,WAAWH;QAElC,IAAInH,KAAK;YACPrD,MAAM,mCAAmC0I;YACzCkC,0CAAgB,CAAChH,GAAG,CAAC8E,UAAU;gBAAE/C,KAAK,IAAI,CAACjE,WAAW;gBAAE2B;YAAI;QAC9D,OAAO;YACLrD,MAAM,gCAAgC0I;QACxC;QAEA,OAAO;YACL,GAAG+B,IAAI;YACPrH,KAAKsH;YACLhC;YACArF;QACF;IACF;IAEA,MAAMwH,wBACJnG,GAAe,EACfnE,OAGC,EACDsB,KAAqB,EACrB+G,eAGI,CAAC,CAAC,EAKL;QACD,IAAI,IAAI,CAAC5G,8BAA8B,EAAE;YACvC,OAAO,IAAI,CAAC8I,yCAAyC,CAACpG,KAAKnE,SAASsB,OAAO+G;QAC7E;QAEA,OAAO,IAAI,CAACmC,iCAAiC,CAACxK,SAASqI;IACzD;IAEA,MAAckC,0CACZpG,GAAe,EACfnE,OAGC,EACDsB,KAAqB,EACrB+G,eAGI,CAAC,CAAC,EAKL;YAsIqBlE;QArItB,MAAMsG,2BAA2B,CAACvD;YAChC,iEAAiE;YACjE,OAAOwD,OACLxD,UACGyD,MAAM,CAAC,CAACC,IAAMA,EAAE5B,IAAI,KAAK,MACzBlG,GAAG,CAAC,CAAC+H;oBACJA;wBAAAA,2CAAAA,SAASC,QAAQ,CAACC,qBAAqB,qBAAvCF,yCAAyC/H,GAAG,CAAC,CAACkI,MAAQC,IAAAA,mDAAiB,EAACD;cAE1E,yCAAyC;aACxCE,IAAI,GACJP,MAAM,CAACQ;QAEd;QAEA,wFAAwF;QACxF,IAAI,EACFC,uBAAuBxF,gBAAgB,EACvCmF,uBAAuBM,8BAA8B,EACrDC,UAAU,EACX,GAAG,MAAM,IAAI,CAACC,WAAW,CAAEC,kCAAkC,CAC5D;YACE5K,UAAUZ,QAAQY,QAAQ;YAC1B6K,SAASzL,QAAQyL,OAAO;QAC1B,GACAnK;QAGF,iFAAiF;QAEjF,MAAMoK,0BAA0B,OAC9BX;YAKAtL,MAAM,gCAAgCmG;YAEtC,2DAA2D;YAC3D,MAAMqE,SAAS,MAAM,IAAI,CAACO,iCAAiC,CACzD;gBACE,GAAGxK,OAAO;gBACV4F;YACF,GACAyC;YAGF,iEAAiE;YACjE,MAAMsD,2BAA2BlB,yBAAyBR,OAAO/C,SAAS;YAE1E,IAAI,CAACyE,0BAA0B;gBAC7B,mDAAmD;gBACnD,MAAM,IAAIC,MACR;YAEJ;YACAnM,MAAM,+CAA+CkM;YAErD,MAAME,gCAAgCnB,OAAO;mBACxCK;mBACAY;aACJ;YAED,4IAA4I;YAC5I,MAAM,EAAE/F,kBAAkBkG,sBAAsB,EAAE,GAChD,MAAM,IAAI,CAACP,WAAW,CAAEQ,wBAAwB,CAC9C;gBACEnL,UAAUZ,QAAQY,QAAQ;gBAC1B6K,SAASzL,QAAQyL,OAAO;gBACxBO,aAAaH;YACf,GACAvK;YAGJ,iEAAiE;YACjE,MAAM2K,4BAA4BH,uBAAuBI,IAAI,CAC3D,CAACC,WAAa,CAACvG,iBAAiBwG,QAAQ,CAACD;YAG3C,IAAI,CAACF,2BAA2B;gBAC9B,OAAOhC;YACT;YAEAxK,MAAM,qDAAqDqM;YAE3DlG,mBAAmB8E,OAAO9E,iBAAiByG,MAAM,CAACP;YAElD,4HAA4H;YAC5H,2DAA2D;YAC3D,OAAOJ,wBAAwBG;QACjC;QAEA,MAAM5B,SAAS,MAAMyB,wBAAwBL;QAE7C,oEAAoE;QACpEpB,OAAO/C,SAAS,CAACnF,IAAI,IAAIuJ;QAEzB,MAAMgB,aAAaC,IAAAA,2BAAkB,EAAC,IAAI,CAACpL,WAAW;QAEtD,qDAAqD;QACrD,MAAMqL,8BAA8B5G,iBAAiB9C,GAAG,CAAC,CAACqJ,WACxD,kFAAkF;YAClFM,IAAAA,qBAAW,EAACxL,eAAI,CAAC0B,QAAQ,CAAC2J,YAAYH;QAExC,MAAMO,wBAAwB,AAC5BzC,OAAO/C,SAAS,CACbpE,GAAG,CAAC,CAAC+H;gBAAaA;mBAAAA,CAAAA,6BAAAA,qBAAAA,SAAUC,QAAQ,qBAAlBD,mBAAoB8B,KAAK,KAAIxD,OAAOyD,MAAM,CAAC/B,SAASC,QAAQ,CAAC6B,KAAK;WACpFhC,MAAM,CAACQ,SACPD,IAAI,GACP2B,MAAM,CAAC,CAACC,KAAKH,QAAW,CAAA;gBAAE,GAAGG,GAAG;gBAAE,GAAGH,KAAK;YAAC,CAAA,GAAI,CAAC;QAElDlN,MAAM,iBAAiBiN,uBAAuBF;QAE9C,MAAMO,cAAc,IAAIxL;QAExB,IAAI4H,OAAO6D,IAAI,CAACN,uBAAuB9I,MAAM,EAAE;YAC7C4I,4BAA4BS,OAAO,CAAC,CAACd;gBACnC,IAAIA,YAAYO,uBAAuB;oBACrCK,YAAY1J,GAAG,CAAC8I,UAAUO,qBAAqB,CAACP,SAAS;gBAC3D,OAAO;oBACL,MAAM,IAAIP,MACR,CAAC,yBAAyB,EAAEO,SAAS,kCAAkC,EAAEhD,OAAO6D,IAAI,CAACN,uBAAuBxL,IAAI,CAAC,OAAO;gBAE5H;YACF;QACF,OAAO;YACL,8CAA8C;YAC9CzB,MAAM;YACN+M,4BAA4BS,OAAO,CAAC,CAACd;gBACnC,mBAAmB;gBACnBY,YAAY1J,GAAG,CAAC8I,UAAU;YAC5B;QACF;QAEA,MAAMe,iBAAgB/I,aAAAA,IAAIG,KAAK,qBAATH,WAAWI,MAAM;QAEvC,8BAA8B;QAC9B,MAAM,IAAI,CAACgH,WAAW,CAAE4B,iBAAiB,CACvC;YACEvM,UAAUZ,QAAQY,QAAQ;YAC1BmM;YACAG;QACF,GACA5L;QAGF,4GAA4G;QAC5GA,MAAM+B,GAAG,CAAC,CAAC,UAAU,EAAErD,QAAQY,QAAQ,CAAC,gBAAgB,CAAC,EAAE;YACzDqD,cAAc;YACd1B,UACE,sBACAY,KAAKG,SAAS,CACZ,yGAAyG;YACzG6F,OAAOiE,WAAW,CAChBzJ,MAAM0J,IAAI,CAACN,YAAYO,OAAO,IAAIxK,GAAG,CAAC,CAAC,CAACyK,KAAKC,MAAM,GAAK;oBACtD,2BAA2B;oBAC3B,OAAOf,IAAAA,qBAAW,EAACxL,eAAI,CAAC0B,QAAQ,CAAC,IAAI,CAACxB,WAAW,EAAEF,eAAI,CAACC,IAAI,CAACoL,YAAYiB;oBACzE;wBAACA;wBAAKC;qBAAM;iBACb;QAGT;QAEA,OAAO;YAAE,GAAGvD,MAAM;YAAE3I;QAAM;IAC5B;IAEA,MAAMkJ,kCACJxK,OAGC,EACDqI,eAGI,CAAC,CAAC,EAC+E;QACrF,MAAM,EAAErC,OAAO,EAAEnF,UAAU,EAAEkF,WAAW,EAAE,GAAG,IAAI,CAACjF,oBAAoB;QACtEC,IAAAA,iBAAM,EAACf,QAAQ2F,cAAc,IAAI,MAAM;QACvC5E,IAAAA,iBAAM,EACJiF,WAAW,QAAQnF,cAAc,QAAQkF,eAAe,MACxD;QAGF,MAAMuC,OAAyB;YAC7B,GAAG,IAAI,CAACxH,oBAAoB;YAC5BkF;YACAnF;YACAkF;YACA,GAAG/F,OAAO;YACV+E,aAAa;YACbkD,kBAAkB;QACpB;QAEA,wIAAwI;QACxI,IAAI,CAACK,KAAK3C,cAAc,CAAC7D,UAAU,CAAC,QAAQ,CAACb,eAAI,CAACqB,UAAU,CAACgG,KAAK3C,cAAc,GAAG;YACjF2C,KAAK3C,cAAc,GAAG,OAAO2C,KAAK3C,cAAc;QAClD;QAEA,MAAM8H,SAAS,MAAM,IAAI,CAACrF,uBAAuB,CAACE,KAAK3C,cAAc,EAAE2C,MAAMD;QAE7E,OAAO;YACLnB,WAAWuG,OAAOvG,SAAS;YAC3BgB,QAAQuF,OAAOvF,MAAM;QACvB;IACF;IAEA,MAAMwF,4BAA4B;QAChC,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;YAClB,MAAM,IAAI/B,MACR;QAEJ;QACA,IAAI,CAAC,IAAI,CAACgC,KAAK,EAAE;YACf,4FAA4F;YAC5F,WAAW;YACXnO,MAAM;YACN;QACF;QAEA,MAAMoO,WAAWC,OACdC,QAAQ,CAAC3N,QAAQC,GAAG,CAAC2N,QAAQ,EAC7BlL,GAAG,CAAC,CAACmL,WAAahN,eAAI,CAACC,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE8M;QAEjDC,IAAAA,uDAAkB,EAChB;YACEN,OAAO,IAAI,CAACA,KAAK;YACjBO,QAAQ,IAAI,CAACR,QAAQ,CAACQ,MAAM;QAC9B,GACAN,UACA;YACEpO,MAAM;YACN,0CAA0C;YAC1CqO,OAAWM,IAAI,CAAC,IAAI,CAACjN,WAAW,EAAE;gBAAEkN,OAAO;YAAK;QAClD;IAEJ;IAIA,MAAgBC,yBACdtO,OAA4B,EACA;YAQxBmE,kBAAiDA,mBAElDA,mBAAiDA,mBAEhBA,mBAEqBA,UACFA,WAI/BA,mBAIFA,YAEgBA,WAOAA,mBAAAA;QA/BtCnE,QAAQC,IAAI,GAAG,MAAM,IAAI,CAACF,gBAAgB,CAACC;QAC3C,IAAI,CAACuO,UAAU,GAAG,IAAI,CAACC,aAAa,CAACxO;QAErC,MAAMyO,SAASrK,IAAAA,mBAAS,EAAC,IAAI,CAACjD,WAAW,EAAE;YAAEuN,2BAA2B;QAAK;QAC7E,MAAM,EAAEvK,GAAG,EAAE,GAAGsK;QAChB,+HAA+H;QAC/H,MAAMhN,iCACJ,CAAC,GAAC0C,mBAAAA,IAAIwK,WAAW,qBAAfxK,iBAAiByK,0BAA0B,KAAI,CAAC,GAACzK,oBAAAA,IAAIwK,WAAW,qBAAfxK,kBAAiB0K,oBAAoB;QAC1F,MAAMC,kCACJ,GAAC3K,oBAAAA,IAAIwK,WAAW,qBAAfxK,kBAAiByK,0BAA0B,KAAI,CAAC,GAACzK,oBAAAA,IAAIwK,WAAW,qBAAfxK,kBAAiB0K,oBAAoB;QACzF,IAAI,CAACpN,8BAA8B,GAAGA;QACtC,IAAI,CAACuD,0BAA0B,GAAG,CAAC,GAACb,oBAAAA,IAAIwK,WAAW,qBAAfxK,kBAAiByK,0BAA0B;QAE/E,MAAMG,qBAAqB;YAAC;YAAU;SAAS,CAAC3C,QAAQ,CAACjI,EAAAA,WAAAA,IAAI6K,GAAG,qBAAP7K,SAASsJ,MAAM,KAAI;QAC5E,MAAMwB,eAAexN,kCAAkC0C,EAAAA,YAAAA,IAAI6K,GAAG,qBAAP7K,UAASsJ,MAAM,MAAK;QAC3E,MAAMzH,UAAUkJ,IAAAA,sCAAwB,EAAC/K;QACzC,MAAM+B,cAAciJ,IAAAA,0CAA4B,EAAChL,KAAKnE,QAAQ6F,IAAI,IAAI,eAAe;QACrF,MAAMhF,aAAauO,IAAAA,8CAAsC,EAAC,IAAI,CAACjO,WAAW,EAAEgD;QAC5E,MAAM8B,gBAAgB,CAAC,GAAC9B,oBAAAA,IAAIwK,WAAW,qBAAfxK,kBAAiB8B,aAAa;QACtD,MAAMjF,SAASC,eAAI,CAACC,IAAI,CAAC,IAAI,CAACC,WAAW,EAAEN;QAC3C,MAAMgF,OAAO7F,QAAQ6F,IAAI,IAAI;QAE7B,MAAMqH,iBAAgB/I,aAAAA,IAAIG,KAAK,qBAATH,WAAWI,MAAM;QAEvC,IAAI9C,kCAAkC0C,EAAAA,YAAAA,IAAI6K,GAAG,qBAAP7K,UAASsJ,MAAM,MAAK,UAAU;YAClE,MAAM,IAAI/I,oBAAY,CACpB,CAAC,oEAAoE,EAAEP,IAAI6K,GAAG,CAAEvB,MAAM,CAAC,gEAAgE,CAAC;QAE5J;QAEA,2FAA2F;QAC3F,IAAIhM,kCAAkC0C,CAAAA,wBAAAA,cAAAA,IAAKG,KAAK,sBAAVH,oBAAAA,YAAYI,MAAM,qBAAlBJ,kBAAoBkL,MAAM,MAAK,OAAO;YAC1E,MAAMC,aAAab,OAAOc,iBAAiB,IAAId,OAAOe,gBAAgB,IAAI;YAC1E,MAAMC,iBAAiBxO,eAAI,CAACgC,QAAQ,CAACqM;YACrC,MAAM,IAAI5K,oBAAY,CACpB,CAAC,sDAAsD,EAAE+K,eAAe,gFAAgF,EAAEA,eAAe,oBAAoB,CAAC;QAElM;QAEA,MAAM3O,uBAAuB;YAC3BiF,aAAa,CAAC,CAAC/F,QAAQ+F,WAAW;YAClCC;YACAH;YACAhF;YACAoF;YACAH,QAAQ9F,QAAQ8F,MAAM;YACtBI;QAEF;QACA,IAAI,CAACpF,oBAAoB,GAAGA;QAE5B,MAAM4O,gBAAgB;YACpBzP,MAAMD,QAAQC,IAAI;YAClB0P,YAAY3P,QAAQ2P,UAAU;YAC9BC,YAAY5P,QAAQ6P,cAAc;QACpC;QAEA,8BAA8B;QAC9BzP,QAAQC,GAAG,CAACyP,sBAAsB,GAAG,CAAC,iBAAiB,EAAE9P,QAAQC,IAAI,EAAE;QAEvE,MAAM,EAAE2N,KAAK,EAAEmC,SAAS,EAAE5B,MAAM,EAAE6B,UAAU,EAAEC,aAAa,EAAE,GAAG,MAAMC,IAAAA,uCAAqB,EACzF,IAAI,EACJR,eACA;YACE3J,aAAa,CAAC,CAAC/F,QAAQ+F,WAAW;YAClC5B;QACF;QAGF,IAAI,CAACnE,QAAQ+F,WAAW,EAAE;YACxB,MAAMoK,qBAAqB,MAAM,IAAI,CAACC,0BAA0B,CAACpQ;YAEjE,8EAA8E;YAC9EqQ,IAAAA,4BAAiB,EAACL,YAAY,IAAIM,oEAAiC,GAAGC,UAAU;YAEhF,wEAAwE;YACxE,yEAAyE;YACzE,0EAA0E;YAC1E,2EAA2E;YAC3E,gDAAgD;YAChD,4CAA4C;YAC5CF,IAAAA,4BAAiB,EAACL,YAAYG,mBAAmBI,UAAU;YAE3DP,WAAWQ,GAAG,CACZ,IAAIC,sDAA0B,CAAC,IAAI,CAACtP,WAAW,EAAE;gBAC/C,0CAA0C;gBAC1CuP,QAAQ1Q,QAAQiH,QAAQ,CAACyJ,MAAM,IAAI;YACrC,GAAGH,UAAU;YAEfP,WAAWQ,GAAG,CACZ,IAAIG,kDAAwB,CAAC,IAAI,CAACxP,WAAW,EAAE,IAAI,CAACyP,qBAAqB,EAAEL,UAAU;YAGvF,MAAMM,qBAAqB,IAAIC,oDAAyB,CAAC,IAAI,CAAC3P,WAAW,EAAE;gBACzE4P,aAAa,CAAC,EAAEC,OAAO,EAAE;oBACvB,IAAIA,YAAY,UAAU;4BACjB;wBAAP,QAAO,mBAAA,IAAI,CAACzC,UAAU,qBAAf,iBAAiB0C,qBAAqB;oBAC/C,OAAO;4BACE;wBAAP,QAAO,oBAAA,IAAI,CAAC1C,UAAU,qBAAf,kBAAiB2C,YAAY,CAAC;4BACnCR,QAAQ;wBACV;oBACF;gBACF;YACF;YACAV,WAAWQ,GAAG,CAACK,mBAAmBN,UAAU;YAE5C,MAAMjE,aAAaC,IAAAA,2BAAkB,EAAC,IAAI,CAACpL,WAAW;YAEtD,MAAMgQ,uBAAuBC,IAAAA,sDAA6B,EACxD;gBACEC,WAAW/E;gBACXnL,aAAa,IAAI,CAACA,WAAW;YAC/B,GACAL;YAEF,kCAAkC;YAClC,yCAAyC;YACzCkP,WAAWQ,GAAG,CAACW;YAEfnB,WAAWQ,GAAG,CAAC,IAAIc,0CAAoB,CAAC,IAAI,CAACnQ,WAAW,EAAEoP,UAAU;YAEpE,mFAAmF;YACnF,IAAI,IAAI,CAACgB,cAAc,IAAI;gBACzB,oHAAoH;gBACpHvB,WAAWQ,GAAG,CAAC,IAAIgB,4CAAqB,CAAC,IAAI,CAACrQ,WAAW,EAAEoP,UAAU;gBAErE,0GAA0G;gBAC1GP,WAAWQ,GAAG,CAAC,IAAIiB,oCAAiB,CAAC,IAAI,CAACtQ,WAAW,EAAEoP,UAAU;YACnE;YAEA,IAAIxB,sBAAsBtN,gCAAgC;gBACxDiQ,IAAAA,0DAAqB,EACnB;oBACE9D;oBACAO;gBACF,GACA,CAACwD;oBACC,IAAI1C,cAAc;wBAChB,+FAA+F;wBAC/F,+FAA+F;wBAC/F,sGAAsG;wBACtG,yGAAyG;wBACzG,gCAAgC;wBAChC,IAAI,CAAC2C,uBAAuB;oBAC9B,OAAO,IAAI,CAACC,IAAAA,+BAAuB,KAAI;wBACrC,KAAK,MAAMC,SAASH,OAAQ;gCAExB,gHAAgH;4BAChH,6CAA6C;4BAC7CG;4BAHF,IAGEA,EAAAA,kBAAAA,MAAMhH,QAAQ,qBAAdgH,gBAAgB9I,IAAI,MAAK,OACzB,gGAAgG;4BAChG8I,MAAMjK,QAAQ,CAAC/F,UAAU,CAACd,WAC1B+Q,IAAAA,4BAAoB,EAACD,MAAMjK,QAAQ,GACnC;gCACAmK,IAAAA,4BAAoB;4BACtB;wBACF;oBACF;gBACF;YAEJ;YAEA,qEAAqE;YACrE,IAAIvQ,gCAAgC;gBAClC,IAAI,CAACwQ,gCAAgC;gBACrC,MAAMC,gBAAgBC,IAAAA,kEAAgC,EAAC,IAAI,CAAChR,WAAW,EAAE;oBACvEL,sBAAsB,IAAI,CAACA,oBAAoB;oBAC/CU,SAAS;oBACTsD,eAAe,IAAI,CAACA,aAAa,CAACsN,IAAI,CAAC,IAAI;oBAC3CC,wBAAwB,IAAI,CAAChM,2BAA2B,CAAC+L,IAAI,CAAC,IAAI;oBAClEE,iBAAiBxD;oBACjByD,gBAAgB3E,MAAM4E,eAAe,CAACJ,IAAI,CAACxE;oBAC3CV;gBACF;gBACA,IAAI,CAAC3B,WAAW,GAAG2G;gBACnBlC,WAAWQ,GAAG,CAAC0B,cAAclC,UAAU;gBACvC,IAAI,CAACyC,gBAAgB,GAAGP,cAAcO,gBAAgB;YACxD;YAEA,mFAAmF;YACnF,IAAI,IAAI,CAAClB,cAAc,IAAI;gBACzB,IAAI,CAACxC,oBAAoB;oBACvB,8CAA8C;oBAC9CiB,WAAWQ,GAAG,CACZ,IAAIkC,oDAAyB,CAACvC,mBAAmBI,UAAU,GAAGoC,QAAQ,EAAEpC,UAAU;gBAEtF,OAAO;wBAME9B;oBALPuB,WAAWQ,GAAG,CACZoC,IAAAA,yDAA4B,EAAC,IAAI,CAACzR,WAAW,EAAE;wBAC7CH;wBACAH;wBACA4N;4BACGA,oBAAAA,OAAOtK,GAAG,CAACG,KAAK,qBAAhBmK,kBAAkBlK,MAAM,AAA3B;wBACA/B,gBAAgB,CAACqQ,mBACf,IAAI,CAACC,iBAAiB,CAACD,kBAAkB;gCAAEjS,UAAU;4BAAM;wBAC7DgG,oBAAoB,OAAOC;4BACzB,kDAAkD;4BAClD,IAAIpF,gCAAgC;gCAClC,2GAA2G;gCAC3G,4HAA4H;gCAC5H,MAAMsR,OAAO,MAAM5C,mBAAmB6C,0BAA0B;gCAChE,OAAO;oCAAEzL,SAASwL;gCAAK;4BACzB;4BAEA,qFAAqF;4BACrF,OAAO,IAAI,CAACnM,kBAAkB,CAACC;wBACjC;oBACF;gBAEJ;YACF;QACF,OAAO;YACL,qEAAqE;YACrE,IAAIpF,gCAAgC;gBAClC,IAAI,CAACwQ,gCAAgC;gBACrC,MAAMC,gBAAgBC,IAAAA,kEAAgC,EAAC,IAAI,CAAChR,WAAW,EAAE;oBACvEL,sBAAsB,IAAI,CAACA,oBAAoB;oBAC/CU,SAAS;oBACTsD,eAAe,IAAI,CAACA,aAAa,CAACsN,IAAI,CAAC,IAAI;oBAC3CC,wBAAwB,IAAI,CAAChM,2BAA2B,CAAC+L,IAAI,CAAC,IAAI;oBAClEE,iBAAiBxD;oBACjByD,gBAAgB3E,MAAM4E,eAAe,CAACJ,IAAI,CAACxE;oBAC3CV;gBACF;gBACA,IAAI,CAAC3B,WAAW,GAAG2G;YACrB;QACF;QACA,qEAAqE;QACrE,MAAMe,gBAAgB9E,OAAO+E,KAAK,CAACd,IAAI,CAACjE;QAExCA,OAAO+E,KAAK,GAAG,CAACC;YACd,OAAOF,cAAc,CAACG;gBACpB,IAAI,CAACzF,QAAQ,GAAG;gBAChB,IAAI,CAACC,KAAK,GAAG;gBACb,IAAI,CAACmC,SAAS,GAAG;gBACjB,IAAI,CAACsD,aAAa,GAAG,IAAI9R;gBACzB4R,4BAAAA,SAAWC;YACb;QACF;QAEAE,IAAAA,4CAAwB,EAAC1F;QACzB,IAAI,CAACA,KAAK,GAAGA;QACb,IAAI,CAACmC,SAAS,GAAGA;QACjB,OAAO;YACL5B;YACAlH,UAAU;gBACR,mDAAmD;gBACnDhH,MAAMD,QAAQC,IAAI;gBAClB,kCAAkC;gBAClCsT,MAAM;gBACN,iDAAiD;gBACjDnO,KAAK,CAAC,iBAAiB,EAAEpF,QAAQC,IAAI,EAAE;gBACvCuT,UAAU;YACZ;YACAxD;YACAC;QACF;IACF;IAIA,MAAcwD,oBAAoBrO,GAAW,EAAEsO,QAAsC,EAAE;QACrF,IAAI,CAAC,IAAI,CAAC3D,SAAS,IAAI,IAAI,CAACsD,aAAa,CAACM,GAAG,CAACvO,MAAM;YAClD;QACF;QAEA3F,MAAM,uBAAuB2F;QAE7B,MAAMwO,SAAS,CAACC;YACd,MAAMC,OAAO3Q,KAAKC,KAAK,CAAC2Q,OAAOF;YAE/B,OAAQC,KAAK9K,IAAI;gBACf,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH;gBACF,KAAK;oBACH;wBACE,MAAMgL,SAASF,KAAKG,IAAI;wBACxB,MAAM,EACJC,eAAe,EACfC,KAAK,EACLC,QAAQ,EACRC,OAAO,EACR,GAaGL;wBAEJ,MAAMM,YAAYH,MAAMvQ,MAAM,IAAIwQ,SAASxQ,MAAM,IAAIyQ,QAAQzQ,MAAM;wBAEnE,gHAAgH;wBAChH,IAAI,CAACsQ,mBAAmBI,WAAW;4BACjC,yIAAyI;4BACzI,mBAAmB;4BACnB,IAAI,OAAOC,WAAWC,GAAG,KAAK,YAAYD,WAAWC,GAAG;4BAExD,MAAMC,eAAe,IAAIC,IACvB;mCAAIP;mCAAUC;6BAAS,CAACtR,GAAG,CAAC,CAAC6R,IAAMA,EAAEC,MAAM,CAAC,EAAE,EAAEvI,MAAM,CAACgI;4BAGzD,MAAMQ,YAAYnK,OAChB/G,MAAM0J,IAAI,CAACoH,cACR3R,GAAG,CAAC,CAACgS;oCAKGA;gCAJP,IAAI,OAAOA,aAAa,UAAU;oCAChC,OAAO;gCACT;gCACA,yCAAyC;gCACzC,OAAOA,EAAAA,kBAAAA,SAASC,KAAK,CAAC,4CAAfD,eAAwC,CAAC,EAAE,KAAI;4BACxD,GACCnK,MAAM,CAACQ;4BAGZuI,SAASmB;wBACX;oBACF;oBACA;gBACF,KAAK;wBAICf;oBAHJ,6GAA6G;oBAC7GkB,QAAG,CAACC,KAAK,CAAC,sBAAsB9R,KAAKG,SAAS,CAACwQ,MAAM,MAAM;oBAE3D,IAAIA,EAAAA,aAAAA,KAAKG,IAAI,qBAATH,WAAW9K,IAAI,MAAK,sBAAsB;4BAG1C,mBAAmB;wBAClB;wBAHHgM,QAAG,CAACC,KAAK,CACP,2BAEA,EAAC,cAAA,IAAI,CAACrH,KAAK,qBAAV,YAAYsH,QAAQ,CAACC,mBAAmB,EAASnI,IAAI;oBAE1D;oBACA;gBACF;oBACEvN,MAAM,wBAAwBqU;oBAC9B;YACJ;QACF;QAEA,MAAMsB,SAAS,MAAM,IAAI,CAACrF,SAAS,CAAEsF,eAAe,CAACjQ,KAAKwO;QAC1D,IAAI,CAACP,aAAa,CAAChQ,GAAG,CAAC+B,KAAKgQ;QAC5B,YAAY;QACZA,OAAOE,YAAY,GAAG;QACtB,MAAM,IAAI,CAACvF,SAAS,CAAEwF,mBAAmB,CAACH,QAAQhQ,KAAKwO;IACzD;IAEA,MAAa4B,yBAA2C;QACtD,IAAI,CAAC,IAAI,CAAC7H,QAAQ,EAAE;YAClB,MAAM,IAAI/B,MAAM;QAClB;QAEA,OAAO,IAAIvE,QAAiB,CAACoO;YAC3B,IAAI,CAAC,IAAI,CAAC7H,KAAK,EAAE;gBACf,4FAA4F;gBAC5F,4FAA4F;gBAC5F,mCAAmC;gBACnCnO,MAAM;gBACN,OAAOgW,QAAQ;YACjB;YAEA,MAAMC,MAAMC,IAAAA,oDAAyB,EAAC;gBACpCxU,aAAa,IAAI,CAACA,WAAW;gBAC7BgN,QAAQ,IAAI,CAACR,QAAQ,CAAEQ,MAAM;gBAC7BP,OAAO,IAAI,CAACA,KAAK;gBACjBgI,UAAU;gBACVC,UAAU;gBACVC,YAAY;oBAAC;oBAAU;iBAAM;gBAC7B3C,UAAU;oBACR,iGAAiG;oBACjGuC;oBACA,MAAM,EAAEK,6BAA6B,EAAE,GAAG,MAAM,mEAAA,QAC9C;oBAGF,IAAI;wBACF,MAAMC,MAAM,IAAID,8BAA8B,IAAI,CAAC5U,WAAW;wBAC9D,MAAM6U,IAAIC,cAAc;wBACxBR,QAAQ;oBACV,EAAE,OAAOR,OAAY;wBACnB,iEAAiE;wBACjE,wCAAwC;wBACxCD,QAAG,CAACkB,GAAG;wBACPlB,QAAG,CAACC,KAAK,CACPkB,gBAAK,CAACC,GAAG,CAAC,gGAAgG,CAAC;wBAE7GpB,QAAG,CAACqB,SAAS,CAACpB;wBACdQ,QAAQ;oBACV;gBACF;YACF;QACF;IACF;IAEA,MAAaa,0BAA0B;YAE3B;QADV,OAAOC,IAAAA,iEAAkC,EAAC;YACxCpI,MAAM,GAAE,iBAAA,IAAI,CAACR,QAAQ,qBAAb,eAAeQ,MAAM;YAC7BP,OAAO,IAAI,CAACA,KAAK;YACjBzM,aAAa,IAAI,CAACA,WAAW;QAC/B;IACF;IAEUqV,qBAA+B;QACvC,OAAO;YAAC;YAAqB;YAAuB;SAAqB;IAC3E;IAMA,gGAAgG;IAChG,MAAchU,eACZqF,QAAgB,EAChB,EAAEjH,QAAQ,EAAwB,EACmB;QACrD,IAAI,IAAI,CAAC6V,sBAAsB,CAAC9C,GAAG,CAAC9L,WAAW;YAC7C,OAAO,IAAI,CAAC4O,sBAAsB,CAACC,GAAG,CAAC7O;QACzC;QACA,MAAM8O,cAAc;YAClB,IAAI;gBACFlX,MAAM,qBAAqB,IAAI,CAACqB,oBAAoB,CAACD,UAAU,EAAEgH;gBACjE,OAAO,MAAM,IAAI,CAACG,qBAAqB,CAACH,UAAU;oBAChD9B,aAAa,IAAI,CAACjF,oBAAoB,CAACiF,WAAW;oBAClDnF;gBACF;YACF,EAAE,OAAOqU,OAAY;oBACJ;gBAAf,MAAMjU,SAAS,EAAA,6BAAA,IAAI,CAACF,oBAAoB,qBAAzB,2BAA2BD,UAAU,IAChDI,eAAI,CAACC,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE,IAAI,CAACL,oBAAoB,CAAED,UAAU,IACjE+V;gBACJ,MAAMC,eAAe7V,SAASC,eAAI,CAAC0B,QAAQ,CAAC3B,QAAQ6G,YAAYA;gBAEhE,wDAAwD;gBACxD,qDAAqD;gBACrD,MAAMuL,MAAM,IAAI1O,oBAAY,CAC1B,aACAyR,IAAAA,gBAAK,CAAA,CAAC,kCAAkC,EAAEU,aAAa,KAAK,CAAC,GAAG5B,MAAMpB,OAAO;gBAG/E,IAAK,MAAMtG,OAAO0H,MAAO;oBACvB,mBAAmB;oBACnB7B,GAAG,CAAC7F,IAAI,GAAG0H,KAAK,CAAC1H,IAAI;gBACvB;gBAEA,MAAM6F;YACR,SAAU;YACR,2CAA2C;YAC7C;QACF;QACA,MAAMxR,QAAQ+U;QAEd,IAAI,CAACF,sBAAsB,CAACpT,GAAG,CAACwE,UAAUjG;QAC1C,OAAOA;IACT;IAEA,MAAckR,kBACZjL,QAAgB,EAChB,EAAEjH,QAAQ,EAAwB,EACmB;QACrD,sCAAsC;QACtC,IAAI;YACF,MAAMkW,WAAW,MAAM,IAAI,CAACtU,cAAc,CAACqF,UAAU;gBAAEjH;YAAS;YAEhE,IAAI,EAACkW,4BAAAA,SAAUjU,GAAG,GAAE;gBAClB,OAAO;YACT;YACA,OAAOkU,IAAAA,6CAAmB,EAAC,IAAI,CAAC5V,WAAW,EAAE2V,SAASjU,GAAG,EAAEiU,SAAS3O,QAAQ;QAC9E,EAAE,OAAO8M,OAAO;YACd,4EAA4E;YAC5E,IAAIA,iBAAiBrJ,OAAO;gBAC1B,IAAI;oBACF,MAAMoL,kBAAkB,MAAMC,IAAAA,6CAAwB,EAAC;wBACrDhC;wBACA9T,aAAa,IAAI,CAACA,WAAW;wBAC7BN,YAAY,IAAI,CAACC,oBAAoB,CAACD,UAAU;oBAClD;oBAEA,OAAO,IAAIqW,SAASF,iBAAiB;wBACnCG,QAAQ;wBACRC,SAAS;4BACP,gBAAgB;wBAClB;oBACF;gBACF,EAAE,OAAOC,eAAe;oBACtB5X,MAAM,iEAAiE4X;oBACvE,MAAMpC;gBACR;YACF,OAAO;gBACL,MAAMA;YACR;QACF;IACF;IAEQrD,0BAA0B;QAChC,IAAI,CAAC6E,sBAAsB,CAACa,KAAK;IACnC;IAEA,+EAA+E;IACvErF,mCAAmC;QACzC,uDAAuD;QACvD,mBAAmB;QACnBsC,WAAWgD,wBAAwB,GAAG,IAAI,CAACC,gBAAgB,CAACpF,IAAI,CAAC,IAAI;IACvE;IAEA,gEAAgE;IAChE,8DAA8D;IACtDoF,iBAAiB,EAAEC,IAAI,EAAEC,EAAE,EAAgC,EAAE;QACnE,IAAI,CAACC,gBAAgB,CAAC,kBAAkB;YACtC7X,MAAM;YACNgU,MAAM;gBACJ2D;gBACAC;YACF;QACF;IACF;IAEA,YAAY;IAEJE,SAASxS,GAAQ,EAAE;QACzB,MAAMsO,WAAW,CAACmB,YAAsB,EAAE;YACxC,wDAAwD;YAExD,IAAI,CAACA,UAAUjR,MAAM,EAAE;gBACrB,6BAA6B;gBAC7B,IAAI,CAAC+T,gBAAgB,CAAC,kBAAkB;oBACtC7X,MAAM;gBACR;YACF,OAAO;gBACL,KAAK,MAAMc,YAAYiU,UAAW;oBAChC,IAAI,CAACpC,gBAAgB,oBAArB,IAAI,CAACA,gBAAgB,MAArB,IAAI,EAAoB7R;oBACxB,IAAI,CAAC+W,gBAAgB,CAAC,kBAAkB;wBACtC7X,MAAM;wBACNc;oBACF;gBACF;YACF;QACF;QAEA,IAAI,CAAC6S,mBAAmB,CAACrO,IAAIyS,QAAQ,IAAInE;IAC3C;IAEA,sBAAsB;IAEtB,wFAAwF;IACxF,MAAcjK,mBACZH,qBAA6B,EAC7B,EACER,gBAAgB,EAChBH,eAAe,EACfe,YAAY,EACZE,iBAAiB,EAmBlB,EAC4B;YA6B7B;QA5BA7I,IAAAA,iBAAM,EAAC,IAAI,CAAC6M,KAAK,EAAE;QACnB,MAAMa,SAAS,IAAI,CAACb,KAAK,CAACkK,OAAO;QACjC,MAAMC,cAAc,IAAI,CAACnK,KAAK,CAACoK,iBAAiB;QAChD,MAAMC,mBAAmBxJ,OAAOyJ,0BAA0B,oBAAjCzJ,OAAOyJ,0BAA0B,MAAjCzJ,QAAoC,oBAAoB;YAC/ElB,KAAKwK;QACP;QAEA,MAAMI,aAA8B,CAACC,sBAA8BC;gBACjE,8BAAA,uBAAA;aAAA,cAAA,IAAI,CAACzK,KAAK,sBAAV,wBAAA,YAAY0K,SAAS,sBAArB,+BAAA,sBAAuBtE,MAAM,qBAA7B,kCAAA,uBAAgC;gBAC9BuE,SAASC,WAAWT;gBACpB/O,MAAM;gBACNoP;gBACAC;YACF;QACF;QAEA,MAAMI,aAAa,IAAI,CAACC,gBAAgB,CAACpP,uBAAuB;YAC9DI;YACAZ;YACAH;QACF;QAEAsP,oCAAAA,iBAAkBU,KAAK,CAAC;QACxBV,oCAAAA,iBAAkBW,QAAQ,CAAC;YACzBC,MAAM;gBACJC,eAAeL,cAAc;YAC/B;QACF;SACA,cAAA,IAAI,CAAC7K,KAAK,qBAAV,YAAY0K,SAAS,CAACtE,MAAM,CAAC;YAC3BuE,SAASC,WAAWT;YACpBgB,eAAe;gBACbC,YAAYlQ,iBAAiBE,IAAI;gBACjCH,KAAKC,iBAAiBD,GAAG;gBACzBoQ,WAAW3P;gBACXxD,QAAQgD,iBAAiBhD,MAAM;gBAC/BlF,UAAUkI,iBAAiBlI,QAAQ;gBACnCgI,uBAAuBD,gBAAgBC,qBAAqB;gBAC5DM,wBAAwBJ,iBAAiBI,sBAAsB,IAAI,CAAC;YACtE;YACAgQ,YAAY;YACZlQ,MAAM;QACR;QAEA,IAAI;YACF,IAAImQ;YACJ,IAAIC;YAEJ,IAAI;oBAGEtQ;gBAFJ,+FAA+F;gBAC/F,mGAAmG;gBACnG,IAAIA,EAAAA,2CAAAA,iBAAiBI,sBAAsB,qBAAvCJ,yCAAyC/D,WAAW,MAAK,gBAAgB;oBAC3E,MAAMsU,QAAQ,MAAM,IAAI,CAACzL,KAAK,CAAC0L,UAAU,GAAGC,eAAe,CACzD,iFAAiF;oBACjF,aAAa;oBACbjQ,uBAEAR,kBACAH,iBACA;wBACEwP;wBACAxO,SAASD,aAAaC,OAAO;wBAC7BlD,MAAMiD,aAAajD,IAAI;oBACzB;oBAEF0S,QAAQE,MAAMF,KAAK;oBACnBC,WAAWC,MAAMD,QAAQ;gBAC3B,OAAO;oBACL,MAAMC,QAAQ,MAAOZ,CAAAA,cAAc,OAC/B,IAAI,CAAC7K,KAAK,CAAC0L,UAAU,GAAGE,WAAW,CAAC,MAAMf,YAAY,SACtD,IAAI,CAAC7K,KAAK,CAAC0L,UAAU,GAAGC,eAAe,CACrC,iFAAiF;oBACjF,aAAa;oBACbjQ,uBAEAR,kBACAH,iBACA;wBACEwP;wBACAxO,SAASD,aAAaC,OAAO;wBAC7BlD,MAAMiD,aAAajD,IAAI;oBACzB,EACF;oBACJ0S,QAAQE,MAAMF,KAAK;oBACnBC,WAAWC,MAAMD,QAAQ;gBAC3B;YACF,EAAE,OAAOnE,OAAO;gBACd,IAAIA,iBAAiBrJ,OAAO;oBAC1B,4BAA4B;oBAC5B,MAAM6N,QAAQxE,MAAMwE,KAAK;oBACzB,IAAIA,SAAS,sBAAsBA,OAAO;wBACxCxE,MAAMpB,OAAO,IAAI,SAAS4F,MAAMC,gBAAgB;oBAClD;gBACF;gBAEA,MAAMzE;YACR;YAEAgD,oCAAAA,iBAAkBW,QAAQ,CAAC;gBACzBe,KAAK;oBACHC,kBAAkBR,SAASS,KAAK,CAACC,YAAY,CAACC,IAAI;gBACpD;YACF;YACA9B,oCAAAA,iBAAkBU,KAAK,CAAC;YACxBV,oCAAAA,iBAAkBU,KAAK,CAAC;YAExB,MAAMqB,wBAAwB,IAAI,CAACpM,KAAK,CAACqM,4BAA4B,CAAC7H,IAAI,CAAC,IAAI,CAACxE,KAAK;YAErF,MAAMsM,aAAa,IAAI,CAACC,kBAAkB;YAE1C,MAAMlQ,SAAS,MAAMiQ,WACnB,iFAAiF;YACjF,aAAa;YACb5Q,uBAEA8P,SAASgB,OAAO,EAChBhB,SAASS,KAAK,EACd;gBACEQ,wBAAwB,MAAM,IAAI,CAACzM,KAAK,CAAC0M,oBAAoB,CAC3D7L,OAAO8L,WAAW,CAACF,sBAAsB,EACzC;oBACEG,YAAY;oBACZ7R;oBACAG;gBACF;gBAEF,wBAAwB;gBACxB2R,qBAAqBhM,OAAOyL,UAAU,CAACO,mBAAmB;gBAC1DlI,gBAAgB,IAAI,CAAC3E,KAAK,CAAC4E,eAAe;gBAC1CkI,uBAAuBjM,OAAOyL,UAAU,CAACQ,qBAAqB;gBAC9DC,mBAAmBjR,aAAajD,IAAI;gBACpCoC,KAAKC,iBAAiBD,GAAG;gBACzB1H,aAAasN,OAAOtN,WAAW;gBAC/B0I,aAAaD,kBAAkBC,WAAW;gBAC1C+Q,qBAAqBnM,OAAOyL,UAAU,CAACW,6BAA6B,CAClEvR;gBAGFQ,WAAWF,kBAAkBE,SAAS;gBACtCE,cAAcJ,kBAAkBI,YAAY;gBAC5CD,WAAWH,kBAAkBG,SAAS;gBACtCxB,iBAAiBqB,kBAAkBrB,eAAe;gBAClD+D,YAAYmC,OAAON,MAAM,CAAC2M,mBAAmB,IAAIrM,OAAOtN,WAAW;gBACnE6Y;gBAEA,iFAAiF;gBACjFpQ;YACF;YAGF,IAAI,CAACgE,KAAK,CAAC0K,SAAS,CAACtE,MAAM,CAAC;gBAC1BuE,SAASC,WAAWT;gBACpB/O,MAAM;YACR;YAEAiP,oCAAAA,iBAAkBU,KAAK,CAAC;YAExB,IAAIoC,aAA4B;YAChC,IAAIC,YAA2B;YAE/B,qDAAqD;YACrD,IAAIpR,kBAAkB6D,MAAM,KAAK,UAAU;gBACzC,IAAI;wBAYgBvG,oBAAAA;oBAXlB,MAAM+T,SAAS,OAAOhR,WAAW,WAAW9G,KAAKC,KAAK,CAAC6G,UAAUA;oBAEjElJ,IAAAA,iBAAM,EACJ,eAAeka,UAAUtX,MAAMuX,OAAO,CAACD,OAAO/T,SAAS,GACvD;oBAGF,MAAMA,YAAY+T,OAAO/T,SAAS;oBAClC,MAAMgB,SAAS+S,OAAO/S,MAAM;oBAE5B,MAAM6S,aAAa7T,UAAUyD,MAAM,CAAC,CAACwQ,QAAUA,MAAMnS,IAAI,KAAK,KAAK,CAAC,EAAE;oBACtE,MAAMgS,YAAY9T,EAAAA,oBAAAA,UAAUyD,MAAM,CAAC,CAACwQ,QAAUA,MAAMnS,IAAI,KAAK,4BAA3C9B,qBAAAA,iBAAmD,CAAC,EAAE,qBAAtDA,mBAAwDzD,MAAM,KAAI;oBAEpF,OAAO;wBACL2X,kBAAkBjC,MAAMkC,KAAK,GACzBlC,MAAMhF,KAAK,CAAC4F,IAAI,GAAGX,SAASgB,OAAO,CAACxW,MAAM,GAC1CuV,MAAMhF,KAAK,CAAC4F,IAAI,GAAGZ,MAAM/E,QAAQ,CAAC2F,IAAI,GAAGZ,MAAM9E,OAAO,CAAC0F,IAAI;wBAC/DuB,kBAAkBlC,SAASmC,IAAI;wBAC/BC,WAAWpC,SAAS1B,EAAE;wBACtBzN,QAAQ8Q,WAAWtX,MAAM;wBACzBX,KAAKkY;wBACL9T;wBACAgB;oBACF;gBACF,EAAE,OAAO+M,OAAY;oBACnB,MAAM,IAAIrJ,MACR,mHACEqJ,MAAMpB,OAAO;gBAEnB;YACF;YAEA,IAAI,OAAO5J,WAAW,UAAU;gBAC9B8Q,aAAa9Q;gBAEb,4CAA4C;gBAC5C,IAAI,EAAEmQ,OAAO,EAAEP,KAAK,EAAE,GAAGT;gBACzB,IAAIxP,kBAAkBC,WAAW,EAAE;oBACjCuQ,UAAU,EAAE;gBACd;gBAEAY,YAAY,MAAMS,qBAChB;oBACE,EAAE;uBACCrB;uBACA,IAAI,CAACxM,KAAK,CAAC8N,iBAAiB,CAAC7B;iBACjC,EACD;oBACE8B,eAAe/R,kBAAkB+R,aAAa;oBAC9ClB,qBAAqBhM,OAAOyL,UAAU,CAACO,mBAAmB;oBAC1DT;gBACF;YAEJ,OAAO;gBACLe,aAAa9Q,OAAOwN,IAAI;gBACxBuD,YAAY/Q,OAAOnH,GAAG;YACxB;YAEA,OAAO;gBACLsY,kBAAkBjC,MAAMkC,KAAK,GACzBlC,MAAMhF,KAAK,CAAC4F,IAAI,GAAGX,SAASgB,OAAO,CAACxW,MAAM,GAC1CuV,MAAMhF,KAAK,CAAC4F,IAAI,GAAGZ,MAAM/E,QAAQ,CAAC2F,IAAI,GAAGZ,MAAM9E,OAAO,CAAC0F,IAAI;gBAC/DuB,kBAAkBlC,SAASmC,IAAI;gBAC/BC,WAAWpC,SAAS1B,EAAE;gBACtBzN,QAAQ8Q;gBACRjY,KAAKkY;YACP;QACF,EAAE,OAAO/F,OAAO;YACd,+DAA+D;YAC/D,mBAAmB;YACnBA,KAAK,CAAC2G,iDAA4B,CAAC,GAAG;YAEtC,IAAI,CAAChO,KAAK,CAAC0K,SAAS,CAACtE,MAAM,CAAC;gBAC1BuE,SAASC,WAAWT;gBACpB/O,MAAM;YACR;YAEA,MAAMiM;QACR;IACF;IAEQkF,qBAAqB;YAEzB,qBAAA;QADF,OACE,EAAA,cAAA,IAAI,CAACvM,KAAK,sBAAV,sBAAA,YAAYkK,OAAO,qBAAnB,oBAAqBoC,UAAU,CAAC2B,gBAAgB,KAC/C,CAAA,CAACC,YAAYC,YAAYlC,OAAO7Z,UAC/Bgc,IAAAA,yBAAc,EAACC,IAAAA,uBAAY,EAACH,YAAYC,YAAYlC,OAAO7Z,UAAUyX,IAAI,AAAD;IAE9E;IAEQiB,iBACNpP,qBAA6B,EAC7B,EACEI,YAAY,EACZZ,gBAAgB,EAChBH,eAAe,EAWhB,EACD;QACA5H,IAAAA,iBAAM,EAAC,IAAI,CAAC6M,KAAK,EAAE;QACnB,MAAMa,SAAS,IAAI,CAACb,KAAK,CAACkK,OAAO;QAEjC,MAAMoE,UAAUC,IAAAA,qBAAU,EAAC7S,uBAAuBR,kBAAkB;YAClEsT,8BAA8B3N,OAAO8L,WAAW,CAAC6B,4BAA4B;YAC7EzT;YACAgB,SAASD,aAAaC,OAAO;YAC7BlD,MAAMiD,aAAajD,IAAI;QACzB;QACA,OAAO,IAAI,CAACmH,KAAK,CAAC0L,UAAU,GAAG+C,oBAAoB,CAACH;IACtD;IAEA,MAAc3S,yBACZuL,QAAgB,EAChB,EACEnM,eAAe,EACfG,gBAAgB,EAOjB,EACD;QACA/H,IAAAA,iBAAM,EAAC,IAAI,CAAC6M,KAAK,EAAE;QACnB,OAAO,MAAM,IAAI,CAACA,KAAK,CAAC0M,oBAAoB,CAAC5X,IAAAA,0CAA4B,EAACoS,WAAW;YACnF0F,YAAY;YACZ7R;YACAG;QACF;IACF;;QA/nDK,qBACG8E,QAAmC,WACnCmC,YAAmC,WACnCsD,gBAA6C,IAAI9R,OAoTzD,kCAAkC;aAC1BT,uBAAkD,CAAC,QAEnDgE,gBAAmC,OACzC+C,UACAC,kBAAkB,CAAC,CAAC,EACpBwU,SAAS,CAAC,CAAC;YAEX,MAAMC,MAAM,MAAM,IAAI,CAACvU,qBAAqB,CAACH,UAAUC;YAEvD,IACE,mFAAmF;YACnFwU,OAAOvT,GAAG,IACV,IAAI,CAACjI,oBAAoB,CAACiF,WAAW,KAAK,MAC1C;gBACA,mBAAmB;gBACnB,MAAMuG,aAAaC,IAAAA,2BAAkB,EAAC,IAAI,CAACpL,WAAW;gBACtD,MAAM0V,eAAe5V,eAAI,CAAC0B,QAAQ,CAAC2J,YAAYiQ,IAAIpU,QAAQ;gBAC3D,MAAM/C,MAAM,IAAIK,IAAIoR,cAAc,IAAI,CAACxR,uBAAuB;gBAC9D,IAAI,CAACuS,QAAQ,CAACxS;YAChB;YAEA,OAAOoX,IAAAA,mDAAyB,EAC9B,IAAI,CAACrb,WAAW,EAChBob,IAAI1Z,GAAG,EACP0Z,IAAIpU,QAAQ,EACZL,gBAAgB/B,WAAW,IAAI,IAAI,CAACjF,oBAAoB,CAACiF,WAAW;QAExE,QA4bAwF,cAAmF,WAwQ3EkH,mBAAwD,MAwJhE,aAAa;aAELgE,yBAAyB,IAAIlV;;AA+cvC;AAEA,SAASiX,WAAWT,WAAmB;IACrC,OAAOA,YAAYF,QAAQ,CAAC;AAC9B;AAEA,SAASzN,WAAWqS,GAAW;IAC7B,uDAAuD;IACvD,mDAAmD;IACnD,6FAA6F;IAC7F,OAAOA,IAAI7Z,OAAO,CAAC,oBAAoB;AACzC;AAEA,eAAe6Y,qBACbiB,OAAsE,EACtE1c,OAAkC;IAElC,OAAO,AAAC,CAAA,MAAM2c,IAAAA,mDAA6B,EAACD,SAAS1c,QAAO,EAAG6X,QAAQ,CAACjB,WAAW;QACjF+E,eAAe3b,QAAQ2b,aAAa;IACtC;AACF;AAEA,SAASjR,OAAUkS,KAAU;IAC3B,OAAOjZ,MAAM0J,IAAI,CAAC,IAAIqH,IAAIkI;AAC5B"}
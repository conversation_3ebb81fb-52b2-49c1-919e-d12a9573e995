{"version": 3, "sources": ["../../../../../src/utils/telemetry/clients/FetchDetachedClient.ts"], "sourcesContent": ["import { spawn } from 'node:child_process';\nimport fs from 'node:fs';\nimport path from 'node:path';\n\nimport { createTempFilePath } from '../../createTempPath';\nimport type { TelemetryClient, TelemetryClientStrategy, TelemetryRecordInternal } from '../types';\n\nconst debug = require('debug')('expo:telemetry:client:detached') as typeof console.log;\n\nexport class FetchDetachedClient implements TelemetryClient {\n  /** This client should be used for short-lived commands */\n  readonly strategy: TelemetryClientStrategy = 'detached';\n  /** All recorded telemetry events */\n  private records: TelemetryRecordInternal[] = [];\n\n  abort() {\n    return this.records;\n  }\n\n  record(record: TelemetryRecordInternal[]) {\n    this.records.push(\n      ...record.map((record) => ({\n        ...record,\n        originalTimestamp: record.sentAt,\n      }))\n    );\n  }\n\n  async flush() {\n    try {\n      if (!this.records.length) {\n        return debug('No records to flush, skipping...');\n      }\n\n      const file = createTempFilePath('expo-telemetry.json');\n      const data = JSON.stringify({ records: this.records });\n\n      this.records = [];\n\n      await fs.promises.mkdir(path.dirname(file), { recursive: true });\n      await fs.promises.writeFile(file, data);\n\n      const child = spawn(process.execPath, [require.resolve('./flushFetchDetached'), file], {\n        detached: true,\n        windowsHide: true,\n        shell: false,\n        stdio: 'ignore',\n      });\n\n      child.unref();\n    } catch (error) {\n      // This could fail if any direct or indirect imports change during an upgrade to the `expo` dependency via `npx expo install --fix`,\n      // since this file may no longer be present after the upgrade, but before the process under the old Expo CLI version is terminated.\n      debug('Exception while initiating detached flush:', error);\n    }\n\n    debug('Detached flush started');\n  }\n}\n"], "names": ["FetchDetachedClient", "debug", "require", "abort", "records", "record", "push", "map", "originalTimestamp", "sentAt", "flush", "length", "file", "createTempFilePath", "data", "JSON", "stringify", "fs", "promises", "mkdir", "path", "dirname", "recursive", "writeFile", "child", "spawn", "process", "execPath", "resolve", "detached", "windowsHide", "shell", "stdio", "unref", "error", "strategy"], "mappings": ";;;;+BASaA;;;eAAAA;;;;yBATS;;;;;;;gEACP;;;;;;;gEACE;;;;;;gCAEkB;;;;;;AAGnC,MAAMC,QAAQC,QAAQ,SAAS;AAExB,MAAMF;IAMXG,QAAQ;QACN,OAAO,IAAI,CAACC,OAAO;IACrB;IAEAC,OAAOA,MAAiC,EAAE;QACxC,IAAI,CAACD,OAAO,CAACE,IAAI,IACZD,OAAOE,GAAG,CAAC,CAACF,SAAY,CAAA;gBACzB,GAAGA,MAAM;gBACTG,mBAAmBH,OAAOI,MAAM;YAClC,CAAA;IAEJ;IAEA,MAAMC,QAAQ;QACZ,IAAI;YACF,IAAI,CAAC,IAAI,CAACN,OAAO,CAACO,MAAM,EAAE;gBACxB,OAAOV,MAAM;YACf;YAEA,MAAMW,OAAOC,IAAAA,kCAAkB,EAAC;YAChC,MAAMC,OAAOC,KAAKC,SAAS,CAAC;gBAAEZ,SAAS,IAAI,CAACA,OAAO;YAAC;YAEpD,IAAI,CAACA,OAAO,GAAG,EAAE;YAEjB,MAAMa,iBAAE,CAACC,QAAQ,CAACC,KAAK,CAACC,mBAAI,CAACC,OAAO,CAACT,OAAO;gBAAEU,WAAW;YAAK;YAC9D,MAAML,iBAAE,CAACC,QAAQ,CAACK,SAAS,CAACX,MAAME;YAElC,MAAMU,QAAQC,IAAAA,0BAAK,EAACC,QAAQC,QAAQ,EAAE;gBAACzB,QAAQ0B,OAAO,CAAC;gBAAyBhB;aAAK,EAAE;gBACrFiB,UAAU;gBACVC,aAAa;gBACbC,OAAO;gBACPC,OAAO;YACT;YAEAR,MAAMS,KAAK;QACb,EAAE,OAAOC,OAAO;YACd,oIAAoI;YACpI,mIAAmI;YACnIjC,MAAM,8CAA8CiC;QACtD;QAEAjC,MAAM;IACR;;QA/CA,wDAAwD,QAC/CkC,WAAoC;QAC7C,kCAAkC,QAC1B/B,UAAqC,EAAE;;AA6CjD"}
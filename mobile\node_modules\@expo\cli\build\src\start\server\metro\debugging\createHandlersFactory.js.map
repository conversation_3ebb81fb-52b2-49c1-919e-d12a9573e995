{"version": 3, "sources": ["../../../../../../src/start/server/metro/debugging/createHandlersFactory.ts"], "sourcesContent": ["import type { CreateCustomMessageHandlerFn } from '@react-native/dev-middleware';\n\nimport { NetworkResponseHandler } from './messageHandlers/NetworkResponse';\nimport { VscodeDebuggerGetPossibleBreakpointsHandler } from './messageHandlers/VscodeDebuggerGetPossibleBreakpoints';\nimport { VscodeDebuggerSetBreakpointByUrlHandler } from './messageHandlers/VscodeDebuggerSetBreakpointByUrl';\nimport { VscodeRuntimeCallFunctionOnHandler } from './messageHandlers/VscodeRuntimeCallFunctionOn';\nimport { VscodeRuntimeEvaluateHandler } from './messageHandlers/VscodeRuntimeEvaluate';\nimport { VscodeRuntimeGetPropertiesHandler } from './messageHandlers/VscodeRuntimeGetProperties';\nimport { pageIsSupported } from './pageIsSupported';\n\nconst debug = require('debug')('expo:metro:debugging:messageHandlers') as typeof console.log;\n\nexport function createHandlersFactory(): CreateCustomMessageHandlerFn {\n  return (connection) => {\n    debug('Initializing for connection: ', connection.page.title);\n\n    if (!pageIsSupported(connection.page)) {\n      debug('Aborted, unsupported page capabiltiies:', connection.page.capabilities);\n      return null;\n    }\n\n    const handlers = [\n      // Generic handlers\n      new NetworkResponseHandler(connection),\n      // Vscode-specific handlers\n      new VscodeDebuggerGetPossibleBreakpointsHandler(connection),\n      new VscodeDebuggerSetBreakpointByUrlHandler(connection),\n      new VscodeRuntimeGetPropertiesHandler(connection),\n      new VscodeRuntimeCallFunctionOnHandler(connection),\n      new VscodeRuntimeEvaluateHandler(connection),\n    ].filter((middleware) => middleware.isEnabled());\n\n    if (!handlers.length) {\n      debug('Aborted, all handlers are disabled');\n      return null;\n    }\n\n    debug(\n      'Initialized with handlers: ',\n      handlers.map((middleware) => middleware.constructor.name).join(', ')\n    );\n\n    return {\n      handleDeviceMessage: (message: any) =>\n        withMessageDebug(\n          'device',\n          message,\n          handlers.some((middleware) => middleware.handleDeviceMessage?.(message))\n        ),\n      handleDebuggerMessage: (message: any) =>\n        withMessageDebug(\n          'debugger',\n          message,\n          handlers.some((middleware) => middleware.handleDebuggerMessage?.(message))\n        ),\n    };\n  };\n}\n\nfunction withMessageDebug(type: 'device' | 'debugger', message: any, result?: null | boolean) {\n  const status = result ? 'handled' : 'ignored';\n  const prefix = type === 'device' ? '(debugger) <- (device)' : '(debugger) -> (device)';\n\n  try {\n    debug(`%s = %s:`, prefix, status, JSON.stringify(message));\n  } catch {\n    debug(`%s = %s:`, prefix, status, 'message not serializable');\n  }\n\n  return result || undefined;\n}\n"], "names": ["createHandlersFactory", "debug", "require", "connection", "page", "title", "pageIsSupported", "capabilities", "handlers", "NetworkResponseHandler", "VscodeDebuggerGetPossibleBreakpointsHandler", "VscodeDebuggerSetBreakpointByUrlHandler", "VscodeRuntimeGetPropertiesHandler", "VscodeRuntimeCallFunctionOnHandler", "VscodeRuntimeEvaluateHandler", "filter", "middleware", "isEnabled", "length", "map", "constructor", "name", "join", "handleDeviceMessage", "message", "withMessageDebug", "some", "handleDebuggerMessage", "type", "result", "status", "prefix", "JSON", "stringify", "undefined"], "mappings": ";;;;+BAYgBA;;;eAAAA;;;iCAVuB;sDACqB;kDACJ;6CACL;uCACN;4CACK;iCAClB;AAEhC,MAAMC,QAAQC,QAAQ,SAAS;AAExB,SAASF;IACd,OAAO,CAACG;QACNF,MAAM,iCAAiCE,WAAWC,IAAI,CAACC,KAAK;QAE5D,IAAI,CAACC,IAAAA,gCAAe,EAACH,WAAWC,IAAI,GAAG;YACrCH,MAAM,2CAA2CE,WAAWC,IAAI,CAACG,YAAY;YAC7E,OAAO;QACT;QAEA,MAAMC,WAAW;YACf,mBAAmB;YACnB,IAAIC,uCAAsB,CAACN;YAC3B,2BAA2B;YAC3B,IAAIO,iFAA2C,CAACP;YAChD,IAAIQ,yEAAuC,CAACR;YAC5C,IAAIS,6DAAiC,CAACT;YACtC,IAAIU,+DAAkC,CAACV;YACvC,IAAIW,mDAA4B,CAACX;SAClC,CAACY,MAAM,CAAC,CAACC,aAAeA,WAAWC,SAAS;QAE7C,IAAI,CAACT,SAASU,MAAM,EAAE;YACpBjB,MAAM;YACN,OAAO;QACT;QAEAA,MACE,+BACAO,SAASW,GAAG,CAAC,CAACH,aAAeA,WAAWI,WAAW,CAACC,IAAI,EAAEC,IAAI,CAAC;QAGjE,OAAO;YACLC,qBAAqB,CAACC,UACpBC,iBACE,UACAD,SACAhB,SAASkB,IAAI,CAAC,CAACV,aAAeA,WAAWO,mBAAmB,oBAA9BP,WAAWO,mBAAmB,MAA9BP,YAAiCQ;YAEnEG,uBAAuB,CAACH,UACtBC,iBACE,YACAD,SACAhB,SAASkB,IAAI,CAAC,CAACV,aAAeA,WAAWW,qBAAqB,oBAAhCX,WAAWW,qBAAqB,MAAhCX,YAAmCQ;QAEvE;IACF;AACF;AAEA,SAASC,iBAAiBG,IAA2B,EAAEJ,OAAY,EAAEK,MAAuB;IAC1F,MAAMC,SAASD,SAAS,YAAY;IACpC,MAAME,SAASH,SAAS,WAAW,2BAA2B;IAE9D,IAAI;QACF3B,MAAM,CAAC,QAAQ,CAAC,EAAE8B,QAAQD,QAAQE,KAAKC,SAAS,CAACT;IACnD,EAAE,OAAM;QACNvB,MAAM,CAAC,QAAQ,CAAC,EAAE8B,QAAQD,QAAQ;IACpC;IAEA,OAAOD,UAAUK;AACnB"}
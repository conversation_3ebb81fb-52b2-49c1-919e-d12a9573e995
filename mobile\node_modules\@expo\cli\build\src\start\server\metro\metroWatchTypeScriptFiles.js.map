{"version": 3, "sources": ["../../../../../src/start/server/metro/metroWatchTypeScriptFiles.ts"], "sourcesContent": ["import path from 'path';\n\nimport type { ServerLike } from '../BundlerDevServer';\n\nconst debug = require('debug')(\n  'expo:start:server:metro:metroWatchTypeScriptFiles'\n) as typeof console.log;\n\nexport interface MetroWatchTypeScriptFilesOptions {\n  projectRoot: string;\n  metro: import('metro').Server;\n  server: ServerLike;\n  /* Include tsconfig.json in the watcher */\n  tsconfig?: boolean;\n  callback: (event: WatchEvent) => void;\n  /* Array of eventTypes to watch. Defaults to all events */\n  eventTypes?: string[];\n  /* Throlle the callback. When true and  a group of events are recieved, callback it will only be called with the\n   * first event */\n  throttle?: boolean;\n}\n\ninterface WatchEvent {\n  filePath: string;\n  metadata?: {\n    type: 'f' | 'd' | 'l'; // Regular file / Directory / Symlink\n  } | null;\n  type: string;\n}\n\n/**\n * Use the native file watcher / Metro ruleset to detect if a\n * TypeScript file is added to the project during development.\n */\nexport function metroWatchTypeScriptFiles({\n  metro,\n  server,\n  projectRoot,\n  callback,\n  tsconfig = false,\n  throttle = false,\n  eventTypes = ['add', 'change', 'delete'],\n}: MetroWatchTypeScriptFilesOptions): () => void {\n  const watcher = metro.getBundler().getBundler().getWatcher();\n\n  const tsconfigPath = path.join(projectRoot, 'tsconfig.json');\n\n  const listener = ({ eventsQueue }: { eventsQueue: WatchEvent[] }) => {\n    for (const event of eventsQueue) {\n      if (\n        eventTypes.includes(event.type) &&\n        event.metadata?.type !== 'd' &&\n        // We need to ignore node_modules because Metro will add all of the files in node_modules to the watcher.\n        !/node_modules/.test(event.filePath) &&\n        // Ignore declaration files\n        !/\\.d\\.ts$/.test(event.filePath)\n      ) {\n        const { filePath } = event;\n        // Is TypeScript?\n        if (\n          // If the user adds a TypeScript file to the observable files in their project.\n          /\\.tsx?$/.test(filePath) ||\n          // Or if the user adds a tsconfig.json file to the project root.\n          (tsconfig && filePath === tsconfigPath)\n        ) {\n          debug('Detected TypeScript file changed in the project: ', filePath);\n          callback(event);\n\n          if (throttle) {\n            return;\n          }\n        }\n      }\n    }\n  };\n\n  debug('Waiting for TypeScript files to be added to the project...');\n  watcher.addListener('change', listener);\n  watcher.addListener('add', listener);\n\n  const off = () => {\n    watcher.removeListener('change', listener);\n    watcher.removeListener('add', listener);\n  };\n\n  server.addListener?.('close', off);\n  return off;\n}\n"], "names": ["metroWatchTypeScriptFiles", "debug", "require", "metro", "server", "projectRoot", "callback", "tsconfig", "throttle", "eventTypes", "watcher", "getBundler", "getW<PERSON>er", "tsconfigPath", "path", "join", "listener", "eventsQueue", "event", "includes", "type", "metadata", "test", "filePath", "addListener", "off", "removeListener"], "mappings": ";;;;+BAkCgBA;;;eAAAA;;;;gEAlCC;;;;;;;;;;;AAIjB,MAAMC,QAAQC,QAAQ,SACpB;AA6BK,SAASF,0BAA0B,EACxCG,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,QAAQ,EACRC,WAAW,KAAK,EAChBC,WAAW,KAAK,EAChBC,aAAa;IAAC;IAAO;IAAU;CAAS,EACP;IACjC,MAAMC,UAAUP,MAAMQ,UAAU,GAAGA,UAAU,GAAGC,UAAU;IAE1D,MAAMC,eAAeC,eAAI,CAACC,IAAI,CAACV,aAAa;IAE5C,MAAMW,WAAW,CAAC,EAAEC,WAAW,EAAiC;QAC9D,KAAK,MAAMC,SAASD,YAAa;gBAG7BC;YAFF,IACET,WAAWU,QAAQ,CAACD,MAAME,IAAI,KAC9BF,EAAAA,kBAAAA,MAAMG,QAAQ,qBAAdH,gBAAgBE,IAAI,MAAK,OACzB,yGAAyG;YACzG,CAAC,eAAeE,IAAI,CAACJ,MAAMK,QAAQ,KACnC,2BAA2B;YAC3B,CAAC,WAAWD,IAAI,CAACJ,MAAMK,QAAQ,GAC/B;gBACA,MAAM,EAAEA,QAAQ,EAAE,GAAGL;gBACrB,iBAAiB;gBACjB,IACE,+EAA+E;gBAC/E,UAAUI,IAAI,CAACC,aACf,gEAAgE;gBAC/DhB,YAAYgB,aAAaV,cAC1B;oBACAZ,MAAM,qDAAqDsB;oBAC3DjB,SAASY;oBAET,IAAIV,UAAU;wBACZ;oBACF;gBACF;YACF;QACF;IACF;IAEAP,MAAM;IACNS,QAAQc,WAAW,CAAC,UAAUR;IAC9BN,QAAQc,WAAW,CAAC,OAAOR;IAE3B,MAAMS,MAAM;QACVf,QAAQgB,cAAc,CAAC,UAAUV;QACjCN,QAAQgB,cAAc,CAAC,OAAOV;IAChC;IAEAZ,OAAOoB,WAAW,oBAAlBpB,OAAOoB,WAAW,MAAlBpB,QAAqB,SAASqB;IAC9B,OAAOA;AACT"}
{"version": 3, "sources": ["../../../src/utils/env.ts"], "sourcesContent": ["import { boolish, int, string } from 'getenv';\nimport process from 'node:process';\n\n// @expo/webpack-config -> expo-pwa -> @expo/image-utils: EXPO_IMAGE_UTILS_NO_SHARP\n\n// TODO: EXPO_CLI_USERNAME, EXPO_CLI_PASSWORD\n\nclass Env {\n  /** Enable profiling metrics */\n  get EXPO_PROFILE() {\n    return boolish('EXPO_PROFILE', false);\n  }\n\n  /** Enable debug logging */\n  get EXPO_DEBUG() {\n    return boolish('EXPO_DEBUG', false);\n  }\n\n  /** Disable all network requests */\n  get EXPO_OFFLINE() {\n    return boolish('EXPO_OFFLINE', false);\n  }\n\n  /** Enable the beta version of Expo (TODO: Should this just be in the beta version of expo releases?) */\n  get EXPO_BETA() {\n    return boolish('EXPO_BETA', false);\n  }\n\n  /** Enable staging API environment */\n  get EXPO_STAGING() {\n    return boolish('EXPO_STAGING', false);\n  }\n\n  /** Enable local API environment */\n  get EXPO_LOCAL() {\n    return boolish('EXPO_LOCAL', false);\n  }\n\n  /** Is running in non-interactive CI mode */\n  get CI() {\n    return boolish('CI', false);\n  }\n\n  /** Disable telemetry (analytics) */\n  get EXPO_NO_TELEMETRY() {\n    return boolish('EXPO_NO_TELEMETRY', false);\n  }\n\n  /** Disable detaching telemetry to separate process */\n  get EXPO_NO_TELEMETRY_DETACH() {\n    return boolish('EXPO_NO_TELEMETRY_DETACH', false);\n  }\n\n  /** local directory to the universe repo for testing locally */\n  get EXPO_UNIVERSE_DIR() {\n    return string('EXPO_UNIVERSE_DIR', '');\n  }\n\n  /** @deprecated Default Webpack host string */\n  get WEB_HOST() {\n    return string('WEB_HOST', '0.0.0.0');\n  }\n\n  /** Skip warning users about a dirty git status */\n  get EXPO_NO_GIT_STATUS() {\n    return boolish('EXPO_NO_GIT_STATUS', false);\n  }\n  /** Disable auto web setup */\n  get EXPO_NO_WEB_SETUP() {\n    return boolish('EXPO_NO_WEB_SETUP', false);\n  }\n  /** Disable auto TypeScript setup */\n  get EXPO_NO_TYPESCRIPT_SETUP() {\n    return boolish('EXPO_NO_TYPESCRIPT_SETUP', false);\n  }\n  /** Disable all API caches. Does not disable bundler caches. */\n  get EXPO_NO_CACHE() {\n    return boolish('EXPO_NO_CACHE', false);\n  }\n  /** Disable the app select redirect page. */\n  get EXPO_NO_REDIRECT_PAGE() {\n    return boolish('EXPO_NO_REDIRECT_PAGE', false);\n  }\n  /** The React Metro port that's baked into react-native scripts and tools. */\n  get RCT_METRO_PORT() {\n    return int('RCT_METRO_PORT', 0);\n  }\n  /** Skip validating the manifest during `export`. */\n  get EXPO_SKIP_MANIFEST_VALIDATION_TOKEN(): boolean {\n    return !!string('EXPO_SKIP_MANIFEST_VALIDATION_TOKEN', '');\n  }\n\n  /** Public folder path relative to the project root. Default to `public` */\n  get EXPO_PUBLIC_FOLDER(): string {\n    return string('EXPO_PUBLIC_FOLDER', 'public');\n  }\n\n  /** Higher priority `$EDIOTR` variable for indicating which editor to use when pressing `o` in the Terminal UI. */\n  get EXPO_EDITOR(): string {\n    return string('EXPO_EDITOR', '');\n  }\n\n  /**\n   * Overwrite the dev server URL, disregarding the `--port`, `--host`, `--tunnel`, `--lan`, `--localhost` arguments.\n   * This is useful for browser editors that require custom proxy URLs.\n   */\n  get EXPO_PACKAGER_PROXY_URL(): string {\n    return string('EXPO_PACKAGER_PROXY_URL', '');\n  }\n\n  /**\n   * **Experimental** - Disable using `exp.direct` as the hostname for\n   * `--tunnel` connections. This enables **https://** forwarding which\n   * can be used to test universal links on iOS.\n   *\n   * This may cause issues with `expo-linking` and Expo Go.\n   *\n   * Select the exact subdomain by passing a string value that is not one of: `true`, `false`, `1`, `0`.\n   */\n  get EXPO_TUNNEL_SUBDOMAIN(): string | boolean {\n    const subdomain = string('EXPO_TUNNEL_SUBDOMAIN', '');\n    if (['0', 'false', ''].includes(subdomain)) {\n      return false;\n    } else if (['1', 'true'].includes(subdomain)) {\n      return true;\n    }\n    return subdomain;\n  }\n\n  /**\n   * Force Expo CLI to use the [`resolver.resolverMainFields`](https://facebook.github.io/metro/docs/configuration/#resolvermainfields) from the project `metro.config.js` for all platforms.\n   *\n   * By default, Expo CLI will use `['browser', 'module', 'main']` (default for Webpack) for web and the user-defined main fields for other platforms.\n   */\n  get EXPO_METRO_NO_MAIN_FIELD_OVERRIDE(): boolean {\n    return boolish('EXPO_METRO_NO_MAIN_FIELD_OVERRIDE', false);\n  }\n\n  /**\n   * HTTP/HTTPS proxy to connect to for network requests. Configures [https-proxy-agent](https://www.npmjs.com/package/https-proxy-agent).\n   */\n  get HTTP_PROXY(): string {\n    return process.env.HTTP_PROXY || process.env.http_proxy || '';\n  }\n\n  /**\n   * Use the network inspector by overriding the metro inspector proxy with a custom version.\n   * @deprecated This has been replaced by `@react-native/dev-middleware` and is now unused.\n   */\n  get EXPO_NO_INSPECTOR_PROXY(): boolean {\n    return boolish('EXPO_NO_INSPECTOR_PROXY', false);\n  }\n\n  /** Disable lazy bundling in Metro bundler. */\n  get EXPO_NO_METRO_LAZY() {\n    return boolish('EXPO_NO_METRO_LAZY', false);\n  }\n\n  /** Enable the unstable inverse dependency stack trace for Metro bundling errors. */\n  get EXPO_METRO_UNSTABLE_ERRORS() {\n    return boolish('EXPO_METRO_UNSTABLE_ERRORS', false);\n  }\n\n  /** Enable the unstable fast resolver for Metro. */\n  get EXPO_USE_FAST_RESOLVER() {\n    return boolish('EXPO_USE_FAST_RESOLVER', false);\n  }\n\n  /** Disable Environment Variable injection in client bundles. */\n  get EXPO_NO_CLIENT_ENV_VARS(): boolean {\n    return boolish('EXPO_NO_CLIENT_ENV_VARS', false);\n  }\n\n  /** Set the default `user` that should be passed to `--user` with ADB commands. Used for installing APKs on Android devices with multiple profiles. Defaults to `0`. */\n  get EXPO_ADB_USER(): string {\n    return string('EXPO_ADB_USER', '0');\n  }\n\n  /** Used internally to enable E2E utilities. This behavior is not stable to external users. */\n  get __EXPO_E2E_TEST(): boolean {\n    return boolish('__EXPO_E2E_TEST', false);\n  }\n\n  /** Unstable: Force single-bundle exports in production. */\n  get EXPO_NO_BUNDLE_SPLITTING(): boolean {\n    return boolish('EXPO_NO_BUNDLE_SPLITTING', false);\n  }\n\n  /**\n   * Enable Atlas to gather bundle information during development or export.\n   * Note, because this used to be an experimental feature, both `EXPO_ATLAS` and `EXPO_UNSTABLE_ATLAS` are supported.\n   */\n  get EXPO_ATLAS() {\n    return boolish('EXPO_ATLAS', boolish('EXPO_UNSTABLE_ATLAS', false));\n  }\n\n  /** Unstable: Enable tree shaking for Metro. */\n  get EXPO_UNSTABLE_TREE_SHAKING() {\n    return boolish('EXPO_UNSTABLE_TREE_SHAKING', false);\n  }\n\n  /** Unstable: Enable eager bundling where transformation runs uncached after the entire bundle has been created. This is required for production tree shaking and less optimized for development bundling. */\n  get EXPO_UNSTABLE_METRO_OPTIMIZE_GRAPH() {\n    return boolish('EXPO_UNSTABLE_METRO_OPTIMIZE_GRAPH', false);\n  }\n\n  /** Enable the use of Expo's custom metro require implementation. The custom require supports better debugging, tree shaking, and React Server Components. */\n  get EXPO_USE_METRO_REQUIRE() {\n    return boolish('EXPO_USE_METRO_REQUIRE', false);\n  }\n\n  /** Internal key used to pass eager bundle data from the CLI to the native run scripts during `npx expo run` commands. */\n  get __EXPO_EAGER_BUNDLE_OPTIONS() {\n    return string('__EXPO_EAGER_BUNDLE_OPTIONS', '');\n  }\n\n  /** Disable server deployment during production builds (during `expo export:embed`). This is useful for testing API routes and server components against a local server. */\n  get EXPO_NO_DEPLOY(): boolean {\n    return boolish('EXPO_NO_DEPLOY', false);\n  }\n\n  /** Enable hydration during development when rendering Expo Web */\n  get EXPO_WEB_DEV_HYDRATE(): boolean {\n    return boolish('EXPO_WEB_DEV_HYDRATE', false);\n  }\n\n  /** Enable experimental React Server Functions support. */\n  get EXPO_UNSTABLE_SERVER_FUNCTIONS(): boolean {\n    return boolish('EXPO_UNSTABLE_SERVER_FUNCTIONS', false);\n  }\n\n  /** Enable unstable/experimental mode where React Native Web isn't required to run Expo apps on web. */\n  get EXPO_NO_REACT_NATIVE_WEB(): boolean {\n    return boolish('EXPO_NO_REACT_NATIVE_WEB', false);\n  }\n\n  /** Enable unstable/experimental support for deploying the native server in `npx expo run` commands. */\n  get EXPO_UNSTABLE_DEPLOY_SERVER(): boolean {\n    return boolish('EXPO_UNSTABLE_DEPLOY_SERVER', false);\n  }\n\n  /** Is running in EAS Build. This is set by EAS: https://docs.expo.dev/eas/environment-variables/ */\n  get EAS_BUILD(): boolean {\n    return boolish('EAS_BUILD', false);\n  }\n\n  /** Disable the React Native Directory compatibility check for new architecture when installing packages */\n  get EXPO_NO_NEW_ARCH_COMPAT_CHECK(): boolean {\n    return boolish('EXPO_NO_NEW_ARCH_COMPAT_CHECK', false);\n  }\n\n  /** Disable the dependency validation when installing other dependencies and starting the project */\n  get EXPO_NO_DEPENDENCY_VALIDATION(): boolean {\n    // Default to disabling when running in a web container (stackblitz, bolt, etc).\n    const isWebContainer = process.versions.webcontainer != null;\n    return boolish('EXPO_NO_DEPENDENCY_VALIDATION', isWebContainer);\n  }\n\n  /** Force Expo CLI to run in webcontainer mode, this has impact on which URL Expo is using by default */\n  get EXPO_FORCE_WEBCONTAINER_ENV(): boolean {\n    return boolish('EXPO_FORCE_WEBCONTAINER_ENV', false);\n  }\n}\n\nexport const env = new Env();\n\nexport function envIsWebcontainer() {\n  // See: https://github.com/unjs/std-env/blob/4b1e03c4efce58249858efc2cc5f5eac727d0adb/src/providers.ts#L134-L143\n  return (\n    env.EXPO_FORCE_WEBCONTAINER_ENV ||\n    (process.env.SHELL === '/bin/jsh' && !!process.versions.webcontainer)\n  );\n}\n"], "names": ["env", "envIsWebcontainer", "Env", "EXPO_PROFILE", "boolish", "EXPO_DEBUG", "EXPO_OFFLINE", "EXPO_BETA", "EXPO_STAGING", "EXPO_LOCAL", "CI", "EXPO_NO_TELEMETRY", "EXPO_NO_TELEMETRY_DETACH", "EXPO_UNIVERSE_DIR", "string", "WEB_HOST", "EXPO_NO_GIT_STATUS", "EXPO_NO_WEB_SETUP", "EXPO_NO_TYPESCRIPT_SETUP", "EXPO_NO_CACHE", "EXPO_NO_REDIRECT_PAGE", "RCT_METRO_PORT", "int", "EXPO_SKIP_MANIFEST_VALIDATION_TOKEN", "EXPO_PUBLIC_FOLDER", "EXPO_EDITOR", "EXPO_PACKAGER_PROXY_URL", "EXPO_TUNNEL_SUBDOMAIN", "subdomain", "includes", "EXPO_METRO_NO_MAIN_FIELD_OVERRIDE", "HTTP_PROXY", "process", "http_proxy", "EXPO_NO_INSPECTOR_PROXY", "EXPO_NO_METRO_LAZY", "EXPO_METRO_UNSTABLE_ERRORS", "EXPO_USE_FAST_RESOLVER", "EXPO_NO_CLIENT_ENV_VARS", "EXPO_ADB_USER", "__EXPO_E2E_TEST", "EXPO_NO_BUNDLE_SPLITTING", "EXPO_ATLAS", "EXPO_UNSTABLE_TREE_SHAKING", "EXPO_UNSTABLE_METRO_OPTIMIZE_GRAPH", "EXPO_USE_METRO_REQUIRE", "__EXPO_EAGER_BUNDLE_OPTIONS", "EXPO_NO_DEPLOY", "EXPO_WEB_DEV_HYDRATE", "EXPO_UNSTABLE_SERVER_FUNCTIONS", "EXPO_NO_REACT_NATIVE_WEB", "EXPO_UNSTABLE_DEPLOY_SERVER", "EAS_BUILD", "EXPO_NO_NEW_ARCH_COMPAT_CHECK", "EXPO_NO_DEPENDENCY_VALIDATION", "isWebContainer", "versions", "webcontainer", "EXPO_FORCE_WEBCONTAINER_ENV", "SHELL"], "mappings": ";;;;;;;;;;;IAwQaA,GAAG;eAAHA;;IAEGC,iBAAiB;eAAjBA;;;;yBA1QqB;;;;;;;gEACjB;;;;;;;;;;;AAEpB,mFAAmF;AAEnF,6CAA6C;AAE7C,MAAMC;IACJ,6BAA6B,GAC7B,IAAIC,eAAe;QACjB,OAAOC,IAAAA,iBAAO,EAAC,gBAAgB;IACjC;IAEA,yBAAyB,GACzB,IAAIC,aAAa;QACf,OAAOD,IAAAA,iBAAO,EAAC,cAAc;IAC/B;IAEA,iCAAiC,GACjC,IAAIE,eAAe;QACjB,OAAOF,IAAAA,iBAAO,EAAC,gBAAgB;IACjC;IAEA,sGAAsG,GACtG,IAAIG,YAAY;QACd,OAAOH,IAAAA,iBAAO,EAAC,aAAa;IAC9B;IAEA,mCAAmC,GACnC,IAAII,eAAe;QACjB,OAAOJ,IAAAA,iBAAO,EAAC,gBAAgB;IACjC;IAEA,iCAAiC,GACjC,IAAIK,aAAa;QACf,OAAOL,IAAAA,iBAAO,EAAC,cAAc;IAC/B;IAEA,0CAA0C,GAC1C,IAAIM,KAAK;QACP,OAAON,IAAAA,iBAAO,EAAC,MAAM;IACvB;IAEA,kCAAkC,GAClC,IAAIO,oBAAoB;QACtB,OAAOP,IAAAA,iBAAO,EAAC,qBAAqB;IACtC;IAEA,oDAAoD,GACpD,IAAIQ,2BAA2B;QAC7B,OAAOR,IAAAA,iBAAO,EAAC,4BAA4B;IAC7C;IAEA,6DAA6D,GAC7D,IAAIS,oBAAoB;QACtB,OAAOC,IAAAA,gBAAM,EAAC,qBAAqB;IACrC;IAEA,4CAA4C,GAC5C,IAAIC,WAAW;QACb,OAAOD,IAAAA,gBAAM,EAAC,YAAY;IAC5B;IAEA,gDAAgD,GAChD,IAAIE,qBAAqB;QACvB,OAAOZ,IAAAA,iBAAO,EAAC,sBAAsB;IACvC;IACA,2BAA2B,GAC3B,IAAIa,oBAAoB;QACtB,OAAOb,IAAAA,iBAAO,EAAC,qBAAqB;IACtC;IACA,kCAAkC,GAClC,IAAIc,2BAA2B;QAC7B,OAAOd,IAAAA,iBAAO,EAAC,4BAA4B;IAC7C;IACA,6DAA6D,GAC7D,IAAIe,gBAAgB;QAClB,OAAOf,IAAAA,iBAAO,EAAC,iBAAiB;IAClC;IACA,0CAA0C,GAC1C,IAAIgB,wBAAwB;QAC1B,OAAOhB,IAAAA,iBAAO,EAAC,yBAAyB;IAC1C;IACA,2EAA2E,GAC3E,IAAIiB,iBAAiB;QACnB,OAAOC,IAAAA,aAAG,EAAC,kBAAkB;IAC/B;IACA,kDAAkD,GAClD,IAAIC,sCAA+C;QACjD,OAAO,CAAC,CAACT,IAAAA,gBAAM,EAAC,uCAAuC;IACzD;IAEA,yEAAyE,GACzE,IAAIU,qBAA6B;QAC/B,OAAOV,IAAAA,gBAAM,EAAC,sBAAsB;IACtC;IAEA,gHAAgH,GAChH,IAAIW,cAAsB;QACxB,OAAOX,IAAAA,gBAAM,EAAC,eAAe;IAC/B;IAEA;;;GAGC,GACD,IAAIY,0BAAkC;QACpC,OAAOZ,IAAAA,gBAAM,EAAC,2BAA2B;IAC3C;IAEA;;;;;;;;GAQC,GACD,IAAIa,wBAA0C;QAC5C,MAAMC,YAAYd,IAAAA,gBAAM,EAAC,yBAAyB;QAClD,IAAI;YAAC;YAAK;YAAS;SAAG,CAACe,QAAQ,CAACD,YAAY;YAC1C,OAAO;QACT,OAAO,IAAI;YAAC;YAAK;SAAO,CAACC,QAAQ,CAACD,YAAY;YAC5C,OAAO;QACT;QACA,OAAOA;IACT;IAEA;;;;GAIC,GACD,IAAIE,oCAA6C;QAC/C,OAAO1B,IAAAA,iBAAO,EAAC,qCAAqC;IACtD;IAEA;;GAEC,GACD,IAAI2B,aAAqB;QACvB,OAAOC,sBAAO,CAAChC,GAAG,CAAC+B,UAAU,IAAIC,sBAAO,CAAChC,GAAG,CAACiC,UAAU,IAAI;IAC7D;IAEA;;;GAGC,GACD,IAAIC,0BAAmC;QACrC,OAAO9B,IAAAA,iBAAO,EAAC,2BAA2B;IAC5C;IAEA,4CAA4C,GAC5C,IAAI+B,qBAAqB;QACvB,OAAO/B,IAAAA,iBAAO,EAAC,sBAAsB;IACvC;IAEA,kFAAkF,GAClF,IAAIgC,6BAA6B;QAC/B,OAAOhC,IAAAA,iBAAO,EAAC,8BAA8B;IAC/C;IAEA,iDAAiD,GACjD,IAAIiC,yBAAyB;QAC3B,OAAOjC,IAAAA,iBAAO,EAAC,0BAA0B;IAC3C;IAEA,8DAA8D,GAC9D,IAAIkC,0BAAmC;QACrC,OAAOlC,IAAAA,iBAAO,EAAC,2BAA2B;IAC5C;IAEA,qKAAqK,GACrK,IAAImC,gBAAwB;QAC1B,OAAOzB,IAAAA,gBAAM,EAAC,iBAAiB;IACjC;IAEA,4FAA4F,GAC5F,IAAI0B,kBAA2B;QAC7B,OAAOpC,IAAAA,iBAAO,EAAC,mBAAmB;IACpC;IAEA,yDAAyD,GACzD,IAAIqC,2BAAoC;QACtC,OAAOrC,IAAAA,iBAAO,EAAC,4BAA4B;IAC7C;IAEA;;;GAGC,GACD,IAAIsC,aAAa;QACf,OAAOtC,IAAAA,iBAAO,EAAC,cAAcA,IAAAA,iBAAO,EAAC,uBAAuB;IAC9D;IAEA,6CAA6C,GAC7C,IAAIuC,6BAA6B;QAC/B,OAAOvC,IAAAA,iBAAO,EAAC,8BAA8B;IAC/C;IAEA,2MAA2M,GAC3M,IAAIwC,qCAAqC;QACvC,OAAOxC,IAAAA,iBAAO,EAAC,sCAAsC;IACvD;IAEA,2JAA2J,GAC3J,IAAIyC,yBAAyB;QAC3B,OAAOzC,IAAAA,iBAAO,EAAC,0BAA0B;IAC3C;IAEA,uHAAuH,GACvH,IAAI0C,8BAA8B;QAChC,OAAOhC,IAAAA,gBAAM,EAAC,+BAA+B;IAC/C;IAEA,yKAAyK,GACzK,IAAIiC,iBAA0B;QAC5B,OAAO3C,IAAAA,iBAAO,EAAC,kBAAkB;IACnC;IAEA,gEAAgE,GAChE,IAAI4C,uBAAgC;QAClC,OAAO5C,IAAAA,iBAAO,EAAC,wBAAwB;IACzC;IAEA,wDAAwD,GACxD,IAAI6C,iCAA0C;QAC5C,OAAO7C,IAAAA,iBAAO,EAAC,kCAAkC;IACnD;IAEA,qGAAqG,GACrG,IAAI8C,2BAAoC;QACtC,OAAO9C,IAAAA,iBAAO,EAAC,4BAA4B;IAC7C;IAEA,qGAAqG,GACrG,IAAI+C,8BAAuC;QACzC,OAAO/C,IAAAA,iBAAO,EAAC,+BAA+B;IAChD;IAEA,kGAAkG,GAClG,IAAIgD,YAAqB;QACvB,OAAOhD,IAAAA,iBAAO,EAAC,aAAa;IAC9B;IAEA,yGAAyG,GACzG,IAAIiD,gCAAyC;QAC3C,OAAOjD,IAAAA,iBAAO,EAAC,iCAAiC;IAClD;IAEA,kGAAkG,GAClG,IAAIkD,gCAAyC;QAC3C,gFAAgF;QAChF,MAAMC,iBAAiBvB,sBAAO,CAACwB,QAAQ,CAACC,YAAY,IAAI;QACxD,OAAOrD,IAAAA,iBAAO,EAAC,iCAAiCmD;IAClD;IAEA,sGAAsG,GACtG,IAAIG,8BAAuC;QACzC,OAAOtD,IAAAA,iBAAO,EAAC,+BAA+B;IAChD;AACF;AAEO,MAAMJ,MAAM,IAAIE;AAEhB,SAASD;IACd,gHAAgH;IAChH,OACED,IAAI0D,2BAA2B,IAC9B1B,sBAAO,CAAChC,GAAG,CAAC2D,KAAK,KAAK,cAAc,CAAC,CAAC3B,sBAAO,CAACwB,QAAQ,CAACC,YAAY;AAExE"}
{"version": 3, "sources": ["../../../src/run/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport chalk from 'chalk';\n\nimport { logPlatformRunCommand } from './hints';\nimport { Command } from '../../bin/cli';\nimport { assertWithOptionsArgs, printHelp } from '../utils/args';\nimport { CommandError, logCmdError } from '../utils/errors';\n\nexport const expoRun: Command = async (argv) => {\n  const args = assertWithOptionsArgs(\n    {\n      // Types\n      '--help': Boolean,\n      // Aliases\n      '-h': '--help',\n    },\n    {\n      argv,\n      // Allow additional flags for both android and ios commands\n      permissive: true,\n    }\n  );\n\n  try {\n    let [platform] = args._ ?? [];\n\n    // Workaround, filter `--flag` as platform\n    if (platform?.startsWith('-')) {\n      platform = '';\n    }\n\n    // Remove the platform from raw arguments, when provided\n    const argsWithoutPlatform = !platform ? argv : argv?.splice(1);\n\n    // Do not capture `--help` when platform is provided\n    if (!platform && args['--help']) {\n      printHelp(\n        'Run the native app locally',\n        `npx expo run <android|ios>`,\n        chalk`{dim $} npx expo run <android|ios> --help  Output usage information`\n      );\n    }\n\n    if (!platform) {\n      const { selectAsync } = await import('../utils/prompts.js');\n      platform = await selectAsync('Select the platform to run', [\n        { title: 'Android', value: 'android' },\n        { title: 'iOS', value: 'ios' },\n      ]);\n    }\n\n    logPlatformRunCommand(platform, argsWithoutPlatform);\n\n    switch (platform) {\n      case 'android': {\n        const { expoRunAndroid } = await import('./android/index.js');\n        return expoRunAndroid(argsWithoutPlatform);\n      }\n\n      case 'ios': {\n        const { expoRunIos } = await import('./ios/index.js');\n        return expoRunIos(argsWithoutPlatform);\n      }\n\n      default:\n        throw new CommandError('UNSUPPORTED_PLATFORM', `Unsupported platform: ${platform}`);\n    }\n  } catch (error: any) {\n    logCmdError(error);\n  }\n};\n"], "names": ["expoRun", "argv", "args", "assertWithOptionsArgs", "Boolean", "permissive", "platform", "_", "startsWith", "argsWithoutPlatform", "splice", "printHelp", "chalk", "selectAsync", "title", "value", "logPlatformRunCommand", "expoRunAndroid", "expoRunIos", "CommandError", "error", "logCmdError"], "mappings": ";;;;;+BAQaA;;;eAAAA;;;;gEAPK;;;;;;uBAEoB;sBAEW;wBACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnC,MAAMA,UAAmB,OAAOC;IACrC,MAAMC,OAAOC,IAAAA,2BAAqB,EAChC;QACE,QAAQ;QACR,UAAUC;QACV,UAAU;QACV,MAAM;IACR,GACA;QACEH;QACA,2DAA2D;QAC3DI,YAAY;IACd;IAGF,IAAI;QACF,IAAI,CAACC,SAAS,GAAGJ,KAAKK,CAAC,IAAI,EAAE;QAE7B,0CAA0C;QAC1C,IAAID,4BAAAA,SAAUE,UAAU,CAAC,MAAM;YAC7BF,WAAW;QACb;QAEA,wDAAwD;QACxD,MAAMG,sBAAsB,CAACH,WAAWL,OAAOA,wBAAAA,KAAMS,MAAM,CAAC;QAE5D,oDAAoD;QACpD,IAAI,CAACJ,YAAYJ,IAAI,CAAC,SAAS,EAAE;YAC/BS,IAAAA,eAAS,EACP,8BACA,CAAC,0BAA0B,CAAC,EAC5BC,IAAAA,gBAAK,CAAA,CAAC,mEAAmE,CAAC;QAE9E;QAEA,IAAI,CAACN,UAAU;YACb,MAAM,EAAEO,WAAW,EAAE,GAAG,MAAM,mEAAA,QAAO;YACrCP,WAAW,MAAMO,YAAY,8BAA8B;gBACzD;oBAAEC,OAAO;oBAAWC,OAAO;gBAAU;gBACrC;oBAAED,OAAO;oBAAOC,OAAO;gBAAM;aAC9B;QACH;QAEAC,IAAAA,4BAAqB,EAACV,UAAUG;QAEhC,OAAQH;YACN,KAAK;gBAAW;oBACd,MAAM,EAAEW,cAAc,EAAE,GAAG,MAAM,mEAAA,QAAO;oBACxC,OAAOA,eAAeR;gBACxB;YAEA,KAAK;gBAAO;oBACV,MAAM,EAAES,UAAU,EAAE,GAAG,MAAM,mEAAA,QAAO;oBACpC,OAAOA,WAAWT;gBACpB;YAEA;gBACE,MAAM,IAAIU,oBAAY,CAAC,wBAAwB,CAAC,sBAAsB,EAAEb,UAAU;QACtF;IACF,EAAE,OAAOc,OAAY;QACnBC,IAAAA,mBAAW,EAACD;IACd;AACF"}
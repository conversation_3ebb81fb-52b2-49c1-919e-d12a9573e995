{"version": 3, "sources": ["../../../../src/start/server/DevelopmentSession.ts"], "sourcesContent": ["import { ExpoConfig, getConfig } from '@expo/config';\n\nimport {\n  closeDevelopmentSessionAsync,\n  updateDevelopmentSessionAsync,\n} from '../../api/updateDevelopmentSession';\nimport { hasCredentials } from '../../api/user/UserSettings';\nimport { env } from '../../utils/env';\nimport * as ProjectDevices from '../project/devices';\n\nconst debug = require('debug')('expo:start:server:developmentSession') as typeof console.log;\n\nexport class DevelopmentSession {\n  /** If the `startAsync` was successfully called */\n  private hasActiveSession = false;\n\n  constructor(\n    /** Project root directory. */\n    private projectRoot: string,\n    /** Development Server URL. */\n    public url: string | null\n  ) {}\n\n  /**\n   * Notify the Expo servers that a project is running, this enables the Expo Go app\n   * and Dev Clients to offer a \"recently in development\" section for quick access.\n   *\n   * @param projectRoot Project root folder, used for retrieving device installation IDs.\n   * @param props.exp Partial Expo config with values that will be used in the Expo Go app.\n   * @param props.runtime which runtime the app should be opened in. `native` for dev clients, `web` for web browsers.\n   */\n  public async startAsync({\n    exp = getConfig(this.projectRoot).exp,\n    runtime,\n  }: {\n    exp?: Pick<ExpoConfig, 'name' | 'description' | 'slug' | 'primaryColor'>;\n    runtime: 'native' | 'web';\n  }): Promise<void> {\n    try {\n      if (env.CI || env.EXPO_OFFLINE) {\n        debug(\n          env.CI\n            ? 'This project will not be suggested in Expo Go or Dev Clients because Expo CLI is running in CI.'\n            : 'This project will not be suggested in Expo Go or Dev Clients because Expo CLI is running in offline-mode.'\n        );\n        return;\n      }\n\n      const deviceIds = await this.getDeviceInstallationIdsAsync();\n\n      if (!hasCredentials() && !deviceIds?.length) {\n        debug(\n          'Development session will not ping because the user is not authenticated and there are no devices.'\n        );\n        return;\n      }\n\n      if (this.url) {\n        debug(`Development session ping (runtime: ${runtime}, url: ${this.url})`);\n        await updateDevelopmentSessionAsync({\n          url: this.url,\n          runtime,\n          exp,\n          deviceIds,\n        });\n        this.hasActiveSession = true;\n      }\n    } catch (error: any) {\n      debug(`Error updating development session API: ${error}`);\n    }\n  }\n\n  /** Get all recent devices for the project. */\n  private async getDeviceInstallationIdsAsync(): Promise<string[]> {\n    const { devices } = await ProjectDevices.getDevicesInfoAsync(this.projectRoot);\n    return devices.map(({ installationId }) => installationId);\n  }\n\n  /** Try to close any pending development sessions, but always resolve */\n  public async closeAsync(): Promise<boolean> {\n    if (env.CI || env.EXPO_OFFLINE || !this.hasActiveSession) {\n      return false;\n    }\n\n    // Clear out the development session, even if the call fails.\n    // This blocks subsequent calls to `stopAsync`\n    this.hasActiveSession = false;\n\n    try {\n      const deviceIds = await this.getDeviceInstallationIdsAsync();\n\n      if (!hasCredentials() && !deviceIds?.length) {\n        return false;\n      }\n\n      if (this.url) {\n        await closeDevelopmentSessionAsync({\n          url: this.url,\n          deviceIds,\n        });\n      }\n\n      return true;\n    } catch (error: any) {\n      debug(`Error closing development session API: ${error}`);\n      return false;\n    }\n  }\n}\n"], "names": ["DevelopmentSession", "debug", "require", "constructor", "projectRoot", "url", "hasActiveSession", "startAsync", "exp", "getConfig", "runtime", "env", "CI", "EXPO_OFFLINE", "deviceIds", "getDeviceInstallationIdsAsync", "hasCredentials", "length", "updateDevelopmentSessionAsync", "error", "devices", "ProjectDevices", "getDevicesInfoAsync", "map", "installationId", "closeAsync", "closeDevelopmentSessionAsync"], "mappings": ";;;;+BAYaA;;;eAAAA;;;;yBAZyB;;;;;;0CAK/B;8BACwB;qBACX;iEACY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhC,MAAMC,QAAQC,QAAQ,SAAS;AAExB,MAAMF;IAIXG,YACE,4BAA4B,GAC5B,AAAQC,WAAmB,EAC3B,4BAA4B,GAC5B,AAAOC,GAAkB,CACzB;aAHQD,cAAAA;aAEDC,MAAAA;aANDC,mBAAmB;IAOxB;IAEH;;;;;;;GAOC,GACD,MAAaC,WAAW,EACtBC,MAAMC,IAAAA,mBAAS,EAAC,IAAI,CAACL,WAAW,EAAEI,GAAG,EACrCE,OAAO,EAIR,EAAiB;QAChB,IAAI;YACF,IAAIC,QAAG,CAACC,EAAE,IAAID,QAAG,CAACE,YAAY,EAAE;gBAC9BZ,MACEU,QAAG,CAACC,EAAE,GACF,oGACA;gBAEN;YACF;YAEA,MAAME,YAAY,MAAM,IAAI,CAACC,6BAA6B;YAE1D,IAAI,CAACC,IAAAA,4BAAc,OAAM,EAACF,6BAAAA,UAAWG,MAAM,GAAE;gBAC3ChB,MACE;gBAEF;YACF;YAEA,IAAI,IAAI,CAACI,GAAG,EAAE;gBACZJ,MAAM,CAAC,mCAAmC,EAAES,QAAQ,OAAO,EAAE,IAAI,CAACL,GAAG,CAAC,CAAC,CAAC;gBACxE,MAAMa,IAAAA,uDAA6B,EAAC;oBAClCb,KAAK,IAAI,CAACA,GAAG;oBACbK;oBACAF;oBACAM;gBACF;gBACA,IAAI,CAACR,gBAAgB,GAAG;YAC1B;QACF,EAAE,OAAOa,OAAY;YACnBlB,MAAM,CAAC,wCAAwC,EAAEkB,OAAO;QAC1D;IACF;IAEA,4CAA4C,GAC5C,MAAcJ,gCAAmD;QAC/D,MAAM,EAAEK,OAAO,EAAE,GAAG,MAAMC,SAAeC,mBAAmB,CAAC,IAAI,CAAClB,WAAW;QAC7E,OAAOgB,QAAQG,GAAG,CAAC,CAAC,EAAEC,cAAc,EAAE,GAAKA;IAC7C;IAEA,sEAAsE,GACtE,MAAaC,aAA+B;QAC1C,IAAId,QAAG,CAACC,EAAE,IAAID,QAAG,CAACE,YAAY,IAAI,CAAC,IAAI,CAACP,gBAAgB,EAAE;YACxD,OAAO;QACT;QAEA,6DAA6D;QAC7D,8CAA8C;QAC9C,IAAI,CAACA,gBAAgB,GAAG;QAExB,IAAI;YACF,MAAMQ,YAAY,MAAM,IAAI,CAACC,6BAA6B;YAE1D,IAAI,CAACC,IAAAA,4BAAc,OAAM,EAACF,6BAAAA,UAAWG,MAAM,GAAE;gBAC3C,OAAO;YACT;YAEA,IAAI,IAAI,CAACZ,GAAG,EAAE;gBACZ,MAAMqB,IAAAA,sDAA4B,EAAC;oBACjCrB,KAAK,IAAI,CAACA,GAAG;oBACbS;gBACF;YACF;YAEA,OAAO;QACT,EAAE,OAAOK,OAAY;YACnBlB,MAAM,CAAC,uCAAuC,EAAEkB,OAAO;YACvD,OAAO;QACT;IACF;AACF"}
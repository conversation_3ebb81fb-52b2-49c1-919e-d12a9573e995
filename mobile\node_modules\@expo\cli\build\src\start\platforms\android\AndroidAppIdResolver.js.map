{"version": 3, "sources": ["../../../../../src/start/platforms/android/AndroidAppIdResolver.ts"], "sourcesContent": ["import { AndroidConfig } from '@expo/config-plugins';\n\nimport { AppIdResolver } from '../AppIdResolver';\n\nconst debug = require('debug')(\n  'expo:start:platforms:android:AndroidAppIdResolver'\n) as typeof console.log;\n\n/** Resolves the Android package name from the Expo config or native files. */\nexport class AndroidAppIdResolver extends AppIdResolver {\n  constructor(projectRoot: string) {\n    super(projectRoot, 'android', 'android.package');\n  }\n\n  async hasNativeProjectAsync(): Promise<boolean> {\n    try {\n      await AndroidConfig.Paths.getProjectPathOrThrowAsync(this.projectRoot);\n      return true;\n    } catch (error: any) {\n      debug('Expected error checking for native project:', error.message);\n      return false;\n    }\n  }\n\n  async resolveAppIdFromNativeAsync(): Promise<string | null> {\n    const applicationIdFromGradle = await AndroidConfig.Package.getApplicationIdAsync(\n      this.projectRoot\n    ).catch(() => null);\n    if (applicationIdFromGradle) {\n      return applicationIdFromGradle;\n    }\n\n    try {\n      const filePath = await AndroidConfig.Paths.getAndroidManifestAsync(this.projectRoot);\n      const androidManifest = await AndroidConfig.Manifest.readAndroidManifestAsync(filePath);\n      // Assert MainActivity defined.\n      await AndroidConfig.Manifest.getMainActivityOrThrow(androidManifest);\n      if (androidManifest.manifest?.$?.package) {\n        return androidManifest.manifest.$.package;\n      }\n    } catch (error: any) {\n      debug('Expected error resolving the package name from the AndroidManifest.xml:', error);\n    }\n\n    return null;\n  }\n}\n"], "names": ["AndroidAppIdResolver", "debug", "require", "AppIdResolver", "constructor", "projectRoot", "hasNativeProjectAsync", "AndroidConfig", "Paths", "getProjectPathOrThrowAsync", "error", "message", "resolveAppIdFromNativeAsync", "applicationIdFromGradle", "Package", "getApplicationIdAsync", "catch", "androidManifest", "filePath", "getAndroidManifestAsync", "Manifest", "readAndroidManifestAsync", "getMainActivityOrThrow", "manifest", "$", "package"], "mappings": ";;;;+BASaA;;;eAAAA;;;;yBATiB;;;;;;+BAEA;AAE9B,MAAMC,QAAQC,QAAQ,SACpB;AAIK,MAAMF,6BAA6BG,4BAAa;IACrDC,YAAYC,WAAmB,CAAE;QAC/B,KAAK,CAACA,aAAa,WAAW;IAChC;IAEA,MAAMC,wBAA0C;QAC9C,IAAI;YACF,MAAMC,8BAAa,CAACC,KAAK,CAACC,0BAA0B,CAAC,IAAI,CAACJ,WAAW;YACrE,OAAO;QACT,EAAE,OAAOK,OAAY;YACnBT,MAAM,+CAA+CS,MAAMC,OAAO;YAClE,OAAO;QACT;IACF;IAEA,MAAMC,8BAAsD;QAC1D,MAAMC,0BAA0B,MAAMN,8BAAa,CAACO,OAAO,CAACC,qBAAqB,CAC/E,IAAI,CAACV,WAAW,EAChBW,KAAK,CAAC,IAAM;QACd,IAAIH,yBAAyB;YAC3B,OAAOA;QACT;QAEA,IAAI;gBAKEI,6BAAAA;YAJJ,MAAMC,WAAW,MAAMX,8BAAa,CAACC,KAAK,CAACW,uBAAuB,CAAC,IAAI,CAACd,WAAW;YACnF,MAAMY,kBAAkB,MAAMV,8BAAa,CAACa,QAAQ,CAACC,wBAAwB,CAACH;YAC9E,+BAA+B;YAC/B,MAAMX,8BAAa,CAACa,QAAQ,CAACE,sBAAsB,CAACL;YACpD,KAAIA,4BAAAA,gBAAgBM,QAAQ,sBAAxBN,8BAAAA,0BAA0BO,CAAC,qBAA3BP,4BAA6BQ,OAAO,EAAE;gBACxC,OAAOR,gBAAgBM,QAAQ,CAACC,CAAC,CAACC,OAAO;YAC3C;QACF,EAAE,OAAOf,OAAY;YACnBT,MAAM,2EAA2ES;QACnF;QAEA,OAAO;IACT;AACF"}
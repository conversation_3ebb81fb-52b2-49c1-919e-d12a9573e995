{"version": 3, "sources": ["../../../src/utils/jsonSchemaDeref.ts"], "sourcesContent": ["type RefPath = readonly (string | number)[];\n\ninterface NodeRef {\n  $ref: string;\n}\n\n/** Return JSON schema ref if input is of `NodeRef` type */\nconst getRef = (node: NodeRef | unknown): string | undefined =>\n  node != null && typeof node === 'object' && '$ref' in node && typeof node.$ref === 'string'\n    ? node.$ref\n    : undefined;\n\n/** Parse a JSON schema ref into a path array, or return undefined */\nconst parseRefMaybe = (ref: string): RefPath | undefined => {\n  if (ref[0] !== '#') {\n    return undefined;\n  }\n  const props = [];\n  let startIndex = 1;\n  let index = 1;\n  let char: number;\n  while (index < ref.length) {\n    while ((char = ref.charCodeAt(index++)) && char !== 47 /*'/'*/);\n    const prop = ref.slice(startIndex, index - 1);\n    startIndex = index;\n    if (prop) props.push(prop);\n  }\n  return props.length ? props : undefined;\n};\n\nconst NOT_FOUND_SYMBOL = Symbol();\n\n/** Get value at given JSON schema path or return `NOT_FOUND_SYMBOL` */\nconst getValueAtPath = (input: unknown, ref: RefPath): unknown | typeof NOT_FOUND_SYMBOL => {\n  let node = input;\n  for (let index = 0; index < ref.length; index++) {\n    const part = ref[index];\n    if (node != null && typeof node === 'object' && part in node) {\n      node = (node as Record<string, unknown>)[part];\n    } else {\n      node = NOT_FOUND_SYMBOL;\n      break;\n    }\n  }\n  return node;\n};\n\n/** Find all JSON schema refs recursively and add them to `refs` Map */\nconst findRefsRec = (\n  node: unknown,\n  refs: Map<RefPath, RefPath>,\n  path: (string | number)[]\n): void => {\n  if (node == null || typeof node !== 'object') {\n  } else if (Array.isArray(node)) {\n    for (let index = 0, l = node.length; index < l; index++) {\n      const value = node[index];\n      const ref = getRef(value);\n      if (ref) {\n        const targetRef = parseRefMaybe(ref);\n        if (targetRef) refs.set([...path, index], targetRef);\n      } else if (value != null && typeof value === 'object') {\n        path.push(index);\n        findRefsRec(value, refs, path);\n        path.pop();\n      }\n    }\n  } else {\n    const record = node as Record<string, unknown>;\n    for (const key in record) {\n      const value = record[key];\n      const ref = getRef(value);\n      if (ref) {\n        const targetRef = parseRefMaybe(ref);\n        if (targetRef) refs.set([...path, key], targetRef);\n      } else if (value != null && typeof value === 'object') {\n        path.push(key);\n        findRefsRec(value, refs, path);\n        path.pop();\n      }\n    }\n  }\n};\n\n/** Detect whether target (where we set the source value) is a nested path inside the source path */\nconst isSelfReferencingRefEntry = (target: RefPath, source: RefPath) => {\n  for (let index = 0; index < source.length; index++) {\n    if (source[index] !== target[index]) return false;\n  }\n  return true;\n};\n\n/** Return sorted refs entries. Longest target paths will be returned first */\nconst getSortedRefEntries = (refs: Map<RefPath, RefPath>): readonly [RefPath, RefPath][] => {\n  const entries = [...refs.entries()].sort((a, b) => b[1].length - a[1].length);\n  // Filter out self-referenceing paths. If we set nested targets to source values, we'd\n  // create unserializable circular references\n  return entries.filter((entry) => !isSelfReferencingRefEntry(entry[0], entry[1]));\n};\n\n/** Dereference JSON schema pointers.\n *\n * @remarks\n * This is a minimal reimplementation of `json-schema-deref-sync` without\n * file reference, URL/web reference, and loader support.\n *\n * @see https://github.com/cvent/json-schema-deref-sync\n */\nexport function jsonSchemaDeref(input: any): any {\n  // Find all JSON schema refs paths\n  const refs = new Map<RefPath, RefPath>();\n  findRefsRec(input, refs, []);\n  // Shallow copy output\n  const output = { ...input };\n  // Process all ref entries with deepest targets first\n  nextRef: for (const [target, source] of getSortedRefEntries(refs)) {\n    let inputNode = input;\n    let outputNode = output;\n    let targetIndex = 0;\n    // For each path part on the target, traverse the output and clone the input\n    // to not pollute it\n    for (; targetIndex < target.length - 1; targetIndex++) {\n      const part = target[targetIndex];\n      if (inputNode == null || typeof inputNode !== 'object' || !(part in inputNode)) {\n        // If the part doesn't exist, we abort\n        break;\n      } else if (outputNode[part] === inputNode[part]) {\n        // Copy the input on the output if references are equal\n        outputNode[part] = Array.isArray(inputNode[part])\n          ? [...inputNode[part]]\n          : { ...inputNode[part] };\n        inputNode = inputNode[part];\n        outputNode = outputNode[part];\n      } else {\n        // If this part has already been copied, abort\n        break;\n      }\n    }\n    // For each remaining part on the target, continue traversing the output\n    for (; targetIndex < target.length - 1; targetIndex++) {\n      const part = target[targetIndex];\n      if (outputNode == null || typeof outputNode !== 'object' || !(part in outputNode)) {\n        // If the part doesn't exist, skip the entire ref\n        continue nextRef;\n      } else {\n        outputNode = outputNode[part];\n      }\n    }\n    // Get value from output\n    let sourceValue = getValueAtPath(output, source);\n    if (sourceValue === NOT_FOUND_SYMBOL) {\n      // If no value was found, try to get a value from the input instead\n      sourceValue = getValueAtPath(input, source);\n      // Otherwise, skip this ref\n      if (sourceValue === NOT_FOUND_SYMBOL) continue;\n    }\n    // Set the source value on the target path\n    // The for-loops prior have made sure that the output has already been deeply\n    // cloned and traversed for the entire path\n    outputNode[target[target.length - 1]] = sourceValue;\n  }\n  // Return the output with resolved refs\n  return output;\n}\n"], "names": ["jsonSchemaDeref", "getRef", "node", "$ref", "undefined", "parseRefMaybe", "ref", "props", "startIndex", "index", "char", "length", "charCodeAt", "prop", "slice", "push", "NOT_FOUND_SYMBOL", "Symbol", "getValueAtPath", "input", "part", "findRefsRec", "refs", "path", "Array", "isArray", "l", "value", "targetRef", "set", "pop", "record", "key", "isSelfReferencingRefEntry", "target", "source", "getSortedRefEntries", "entries", "sort", "a", "b", "filter", "entry", "Map", "output", "nextRef", "inputNode", "outputNode", "targetIndex", "sourceValue"], "mappings": ";;;;+BA4GgBA;;;eAAAA;;;AAtGhB,yDAAyD,GACzD,MAAMC,SAAS,CAACC,OACdA,QAAQ,QAAQ,OAAOA,SAAS,YAAY,UAAUA,QAAQ,OAAOA,KAAKC,IAAI,KAAK,WAC/ED,KAAKC,IAAI,GACTC;AAEN,mEAAmE,GACnE,MAAMC,gBAAgB,CAACC;IACrB,IAAIA,GAAG,CAAC,EAAE,KAAK,KAAK;QAClB,OAAOF;IACT;IACA,MAAMG,QAAQ,EAAE;IAChB,IAAIC,aAAa;IACjB,IAAIC,QAAQ;IACZ,IAAIC;IACJ,MAAOD,QAAQH,IAAIK,MAAM,CAAE;QACzB,MAAO,AAACD,CAAAA,OAAOJ,IAAIM,UAAU,CAACH,QAAO,KAAMC,SAAS,GAAG,KAAK;QAC5D,MAAMG,OAAOP,IAAIQ,KAAK,CAACN,YAAYC,QAAQ;QAC3CD,aAAaC;QACb,IAAII,MAAMN,MAAMQ,IAAI,CAACF;IACvB;IACA,OAAON,MAAMI,MAAM,GAAGJ,QAAQH;AAChC;AAEA,MAAMY,mBAAmBC;AAEzB,qEAAqE,GACrE,MAAMC,iBAAiB,CAACC,OAAgBb;IACtC,IAAIJ,OAAOiB;IACX,IAAK,IAAIV,QAAQ,GAAGA,QAAQH,IAAIK,MAAM,EAAEF,QAAS;QAC/C,MAAMW,OAAOd,GAAG,CAACG,MAAM;QACvB,IAAIP,QAAQ,QAAQ,OAAOA,SAAS,YAAYkB,QAAQlB,MAAM;YAC5DA,OAAO,AAACA,IAAgC,CAACkB,KAAK;QAChD,OAAO;YACLlB,OAAOc;YACP;QACF;IACF;IACA,OAAOd;AACT;AAEA,qEAAqE,GACrE,MAAMmB,cAAc,CAClBnB,MACAoB,MACAC;IAEA,IAAIrB,QAAQ,QAAQ,OAAOA,SAAS,UAAU,CAC9C,OAAO,IAAIsB,MAAMC,OAAO,CAACvB,OAAO;QAC9B,IAAK,IAAIO,QAAQ,GAAGiB,IAAIxB,KAAKS,MAAM,EAAEF,QAAQiB,GAAGjB,QAAS;YACvD,MAAMkB,QAAQzB,IAAI,CAACO,MAAM;YACzB,MAAMH,MAAML,OAAO0B;YACnB,IAAIrB,KAAK;gBACP,MAAMsB,YAAYvB,cAAcC;gBAChC,IAAIsB,WAAWN,KAAKO,GAAG,CAAC;uBAAIN;oBAAMd;iBAAM,EAAEmB;YAC5C,OAAO,IAAID,SAAS,QAAQ,OAAOA,UAAU,UAAU;gBACrDJ,KAAKR,IAAI,CAACN;gBACVY,YAAYM,OAAOL,MAAMC;gBACzBA,KAAKO,GAAG;YACV;QACF;IACF,OAAO;QACL,MAAMC,SAAS7B;QACf,IAAK,MAAM8B,OAAOD,OAAQ;YACxB,MAAMJ,QAAQI,MAAM,CAACC,IAAI;YACzB,MAAM1B,MAAML,OAAO0B;YACnB,IAAIrB,KAAK;gBACP,MAAMsB,YAAYvB,cAAcC;gBAChC,IAAIsB,WAAWN,KAAKO,GAAG,CAAC;uBAAIN;oBAAMS;iBAAI,EAAEJ;YAC1C,OAAO,IAAID,SAAS,QAAQ,OAAOA,UAAU,UAAU;gBACrDJ,KAAKR,IAAI,CAACiB;gBACVX,YAAYM,OAAOL,MAAMC;gBACzBA,KAAKO,GAAG;YACV;QACF;IACF;AACF;AAEA,kGAAkG,GAClG,MAAMG,4BAA4B,CAACC,QAAiBC;IAClD,IAAK,IAAI1B,QAAQ,GAAGA,QAAQ0B,OAAOxB,MAAM,EAAEF,QAAS;QAClD,IAAI0B,MAAM,CAAC1B,MAAM,KAAKyB,MAAM,CAACzB,MAAM,EAAE,OAAO;IAC9C;IACA,OAAO;AACT;AAEA,4EAA4E,GAC5E,MAAM2B,sBAAsB,CAACd;IAC3B,MAAMe,UAAU;WAAIf,KAAKe,OAAO;KAAG,CAACC,IAAI,CAAC,CAACC,GAAGC,IAAMA,CAAC,CAAC,EAAE,CAAC7B,MAAM,GAAG4B,CAAC,CAAC,EAAE,CAAC5B,MAAM;IAC5E,sFAAsF;IACtF,4CAA4C;IAC5C,OAAO0B,QAAQI,MAAM,CAAC,CAACC,QAAU,CAACT,0BAA0BS,KAAK,CAAC,EAAE,EAAEA,KAAK,CAAC,EAAE;AAChF;AAUO,SAAS1C,gBAAgBmB,KAAU;IACxC,kCAAkC;IAClC,MAAMG,OAAO,IAAIqB;IACjBtB,YAAYF,OAAOG,MAAM,EAAE;IAC3B,sBAAsB;IACtB,MAAMsB,SAAS;QAAE,GAAGzB,KAAK;IAAC;IAC1B,qDAAqD;IACrD0B,SAAS,KAAK,MAAM,CAACX,QAAQC,OAAO,IAAIC,oBAAoBd,MAAO;QACjE,IAAIwB,YAAY3B;QAChB,IAAI4B,aAAaH;QACjB,IAAII,cAAc;QAClB,4EAA4E;QAC5E,oBAAoB;QACpB,MAAOA,cAAcd,OAAOvB,MAAM,GAAG,GAAGqC,cAAe;YACrD,MAAM5B,OAAOc,MAAM,CAACc,YAAY;YAChC,IAAIF,aAAa,QAAQ,OAAOA,cAAc,YAAY,CAAE1B,CAAAA,QAAQ0B,SAAQ,GAAI;gBAE9E;YACF,OAAO,IAAIC,UAAU,CAAC3B,KAAK,KAAK0B,SAAS,CAAC1B,KAAK,EAAE;gBAC/C,uDAAuD;gBACvD2B,UAAU,CAAC3B,KAAK,GAAGI,MAAMC,OAAO,CAACqB,SAAS,CAAC1B,KAAK,IAC5C;uBAAI0B,SAAS,CAAC1B,KAAK;iBAAC,GACpB;oBAAE,GAAG0B,SAAS,CAAC1B,KAAK;gBAAC;gBACzB0B,YAAYA,SAAS,CAAC1B,KAAK;gBAC3B2B,aAAaA,UAAU,CAAC3B,KAAK;YAC/B,OAAO;gBAEL;YACF;QACF;QACA,wEAAwE;QACxE,MAAO4B,cAAcd,OAAOvB,MAAM,GAAG,GAAGqC,cAAe;YACrD,MAAM5B,OAAOc,MAAM,CAACc,YAAY;YAChC,IAAID,cAAc,QAAQ,OAAOA,eAAe,YAAY,CAAE3B,CAAAA,QAAQ2B,UAAS,GAAI;gBAEjF,SAASF;YACX,OAAO;gBACLE,aAAaA,UAAU,CAAC3B,KAAK;YAC/B;QACF;QACA,wBAAwB;QACxB,IAAI6B,cAAc/B,eAAe0B,QAAQT;QACzC,IAAIc,gBAAgBjC,kBAAkB;YACpC,mEAAmE;YACnEiC,cAAc/B,eAAeC,OAAOgB;YACpC,2BAA2B;YAC3B,IAAIc,gBAAgBjC,kBAAkB;QACxC;QACA,0CAA0C;QAC1C,6EAA6E;QAC7E,2CAA2C;QAC3C+B,UAAU,CAACb,MAAM,CAACA,OAAOvB,MAAM,GAAG,EAAE,CAAC,GAAGsC;IAC1C;IACA,uCAAuC;IACvC,OAAOL;AACT"}
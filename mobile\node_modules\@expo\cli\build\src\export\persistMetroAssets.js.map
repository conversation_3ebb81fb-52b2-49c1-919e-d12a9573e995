{"version": 3, "sources": ["../../../src/export/persistMetroAssets.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Based on the community asset persisting for Metro but with base path and web support:\n * https://github.com/facebook/react-native/blob/d6e0bc714ad4d215ede4949d3c4f44af6dea5dd3/packages/community-cli-plugin/src/commands/bundle/saveAssets.js#L1\n */\nimport fs from 'fs';\nimport type { AssetData } from 'metro';\nimport path from 'path';\n\nimport { drawableFileTypes, getAssetLocalPath } from './metroAssetLocalPath';\nimport { ExportAssetMap } from './saveAssets';\nimport { Log } from '../log';\n\nfunction cleanAssetCatalog(catalogDir: string): void {\n  const files = fs.readdirSync(catalogDir).filter((file) => file.endsWith('.imageset'));\n  for (const file of files) {\n    fs.rmSync(path.join(catalogDir, file));\n  }\n}\n\nexport async function persistMetroAssetsAsync(\n  projectRoot: string,\n  assets: readonly AssetData[],\n  {\n    platform,\n    outputDirectory,\n    baseUrl,\n    iosAssetCatalogDirectory,\n    files,\n  }: {\n    platform: string;\n    outputDirectory: string;\n    baseUrl?: string;\n    iosAssetCatalogDirectory?: string;\n    files?: ExportAssetMap;\n  }\n) {\n  if (outputDirectory == null) {\n    Log.warn('Assets destination folder is not set, skipping...');\n    return;\n  }\n\n  // For iOS, we need to ensure that the outputDirectory exists.\n  // The bundle code and images build phase script always tries to access this folder\n  if (platform === 'ios' && !fs.existsSync(outputDirectory)) {\n    fs.mkdirSync(outputDirectory, { recursive: true });\n  }\n\n  let assetsToCopy: AssetData[] = [];\n\n  // TODO: Use `files` as below to defer writing files\n  if (platform === 'ios' && iosAssetCatalogDirectory != null) {\n    // Use iOS Asset Catalog for images. This will allow Apple app thinning to\n    // remove unused scales from the optimized bundle.\n    const catalogDir = path.join(iosAssetCatalogDirectory, 'RNAssets.xcassets');\n    if (!fs.existsSync(catalogDir)) {\n      Log.error(\n        `Could not find asset catalog 'RNAssets.xcassets' in ${iosAssetCatalogDirectory}. Make sure to create it if it does not exist.`\n      );\n      return;\n    }\n\n    Log.log('Adding images to asset catalog', catalogDir);\n    cleanAssetCatalog(catalogDir);\n    for (const asset of assets) {\n      if (isCatalogAsset(asset)) {\n        const imageSet = getImageSet(\n          catalogDir,\n          asset,\n          filterPlatformAssetScales(platform, asset.scales)\n        );\n        writeImageSet(imageSet);\n      } else {\n        assetsToCopy.push(asset);\n      }\n    }\n    Log.log('Done adding images to asset catalog');\n  } else {\n    assetsToCopy = [...assets];\n  }\n  if (platform === 'android') {\n    await createKeepFileAsync(assetsToCopy, outputDirectory);\n  }\n\n  const batches: Record<string, string> = {};\n\n  for (const asset of assetsToCopy) {\n    const validScales = new Set(filterPlatformAssetScales(platform, asset.scales));\n    for (let idx = 0; idx < asset.scales.length; idx++) {\n      const scale = asset.scales[idx];\n      if (validScales.has(scale)) {\n        const src = asset.files[idx];\n        const dest = getAssetLocalPath(asset, { platform, scale, baseUrl });\n        if (files) {\n          const data = await fs.promises.readFile(src);\n          files.set(dest, {\n            contents: data,\n            assetId: getAssetIdForLogGrouping(projectRoot, asset),\n            targetDomain: platform === 'web' ? 'client' : undefined,\n          });\n        } else {\n          batches[src] = path.join(outputDirectory, dest);\n        }\n      }\n    }\n  }\n\n  if (!files) {\n    await copyInBatchesAsync(batches);\n  }\n}\n\nexport async function createKeepFileAsync(\n  assets: AssetData[],\n  outputDirectory: string\n): Promise<void> {\n  if (!assets.length) {\n    return;\n  }\n  const assetsList = [];\n  for (const asset of assets) {\n    const prefix = drawableFileTypes.has(asset.type) ? 'drawable' : 'raw';\n    assetsList.push(`@${prefix}/${getResourceIdentifier(asset)}`);\n  }\n  const keepPath = path.join(outputDirectory, 'raw/keep.xml');\n  const content = `<resources xmlns:tools=\"http://schemas.android.com/tools\" tools:keep=\"${assetsList.join(',')}\" />`;\n  await fs.promises.mkdir(path.dirname(keepPath), { recursive: true });\n  await fs.promises.writeFile(keepPath, content);\n}\n\nexport function getAssetIdForLogGrouping(\n  projectRoot: string,\n  asset: Partial<Pick<AssetData, 'fileSystemLocation' | 'name' | 'type'>>\n): string | undefined {\n  return 'fileSystemLocation' in asset && asset.fileSystemLocation != null && asset.name != null\n    ? path.relative(projectRoot, path.join(asset.fileSystemLocation, asset.name)) +\n        (asset.type ? '.' + asset.type : '')\n    : undefined;\n}\n\nfunction writeImageSet(imageSet: ImageSet): void {\n  fs.mkdirSync(imageSet.baseUrl, { recursive: true });\n\n  for (const file of imageSet.files) {\n    const dest = path.join(imageSet.baseUrl, file.name);\n    fs.copyFileSync(file.src, dest);\n  }\n\n  fs.writeFileSync(\n    path.join(imageSet.baseUrl, 'Contents.json'),\n    JSON.stringify({\n      images: imageSet.files.map((file) => ({\n        filename: file.name,\n        idiom: 'universal',\n        scale: `${file.scale}x`,\n      })),\n      info: {\n        author: 'expo',\n        version: 1,\n      },\n    })\n  );\n}\n\nfunction isCatalogAsset(asset: Pick<AssetData, 'type'>): boolean {\n  return asset.type === 'png' || asset.type === 'jpg' || asset.type === 'jpeg';\n}\n\ntype ImageSet = {\n  baseUrl: string;\n  files: { name: string; src: string; scale: number }[];\n};\n\nfunction getImageSet(\n  catalogDir: string,\n  asset: Pick<AssetData, 'httpServerLocation' | 'name' | 'type' | 'files'>,\n  scales: number[]\n): ImageSet {\n  const fileName = getResourceIdentifier(asset);\n  return {\n    baseUrl: path.join(catalogDir, `${fileName}.imageset`),\n    files: scales.map((scale, idx) => {\n      const suffix = scale === 1 ? '' : `@${scale}x`;\n      return {\n        name: `${fileName + suffix}.${asset.type}`,\n        scale,\n        src: asset.files[idx],\n      };\n    }),\n  };\n}\n\nexport function copyInBatchesAsync(filesToCopy: Record<string, string>) {\n  const queue = Object.keys(filesToCopy);\n  if (queue.length === 0) {\n    return;\n  }\n\n  Log.log(`Copying ${queue.length} asset files`);\n  return new Promise<void>((resolve, reject) => {\n    const copyNext = (error?: NodeJS.ErrnoException) => {\n      if (error) {\n        return reject(error);\n      }\n      if (queue.length) {\n        // queue.length === 0 is checked in previous branch, so this is string\n        const src = queue.shift() as string;\n        const dest = filesToCopy[src];\n        copy(src, dest, copyNext);\n      } else {\n        resolve();\n      }\n    };\n    copyNext();\n  });\n}\n\nfunction copy(src: string, dest: string, callback: (error?: NodeJS.ErrnoException) => void): void {\n  fs.mkdir(path.dirname(dest), { recursive: true }, (err?) => {\n    if (err) {\n      callback(err);\n      return;\n    }\n    fs.createReadStream(src).pipe(fs.createWriteStream(dest)).on('finish', callback);\n  });\n}\n\nconst ALLOWED_SCALES: { [key: string]: number[] } = {\n  ios: [1, 2, 3],\n};\n\nexport function filterPlatformAssetScales(platform: string, scales: number[]): number[] {\n  const whitelist: number[] = ALLOWED_SCALES[platform];\n  if (!whitelist) {\n    return scales;\n  }\n  const result = scales.filter((scale) => whitelist.includes(scale));\n  if (!result.length && scales.length) {\n    // No matching scale found, but there are some available. Ideally we don't\n    // want to be in this situation and should throw, but for now as a fallback\n    // let's just use the closest larger image\n    const maxScale = whitelist[whitelist.length - 1];\n    for (const scale of scales) {\n      if (scale > maxScale) {\n        result.push(scale);\n        break;\n      }\n    }\n\n    // There is no larger scales available, use the largest we have\n    if (!result.length) {\n      result.push(scales[scales.length - 1]);\n    }\n  }\n  return result;\n}\n\nfunction getResourceIdentifier(asset: Pick<AssetData, 'httpServerLocation' | 'name'>): string {\n  const folderPath = getBaseUrl(asset);\n  return `${folderPath}/${asset.name}`\n    .toLowerCase()\n    .replace(/\\//g, '_') // Encode folder structure in file name\n    .replace(/([^a-z0-9_])/g, '') // Remove illegal chars\n    .replace(/^assets_/, ''); // Remove \"assets_\" prefix\n}\n\nfunction getBaseUrl(asset: Pick<AssetData, 'httpServerLocation'>): string {\n  let baseUrl = asset.httpServerLocation;\n  if (baseUrl[0] === '/') {\n    baseUrl = baseUrl.substring(1);\n  }\n  return baseUrl;\n}\n"], "names": ["copyInBatchesAsync", "createKeepFileAsync", "filterPlatformAssetScales", "getAssetIdForLogGrouping", "persistMetroAssetsAsync", "cleanAssetCatalog", "catalogDir", "files", "fs", "readdirSync", "filter", "file", "endsWith", "rmSync", "path", "join", "projectRoot", "assets", "platform", "outputDirectory", "baseUrl", "iosAssetCatalogDirectory", "Log", "warn", "existsSync", "mkdirSync", "recursive", "assetsToCopy", "error", "log", "asset", "isCatalogAsset", "imageSet", "getImageSet", "scales", "writeImageSet", "push", "batches", "validScales", "Set", "idx", "length", "scale", "has", "src", "dest", "getAssetLocalPath", "data", "promises", "readFile", "set", "contents", "assetId", "targetDomain", "undefined", "assetsList", "prefix", "drawableFileTypes", "type", "getResourceIdentifier", "<PERSON><PERSON><PERSON>", "content", "mkdir", "dirname", "writeFile", "fileSystemLocation", "name", "relative", "copyFileSync", "writeFileSync", "JSON", "stringify", "images", "map", "filename", "idiom", "info", "author", "version", "fileName", "suffix", "filesToCopy", "queue", "Object", "keys", "Promise", "resolve", "reject", "copyNext", "shift", "copy", "callback", "err", "createReadStream", "pipe", "createWriteStream", "on", "ALLOWED_SCALES", "ios", "whitelist", "result", "includes", "maxScale", "folderPath", "getBaseUrl", "toLowerCase", "replace", "httpServerLocation", "substring"], "mappings": "AAAA;;;;;;;;;CASC;;;;;;;;;;;IA4LeA,kBAAkB;eAAlBA;;IAhFMC,mBAAmB;eAAnBA;;IAuHNC,yBAAyB;eAAzBA;;IArGAC,wBAAwB;eAAxBA;;IA9GMC,uBAAuB;eAAvBA;;;;gEAfP;;;;;;;gEAEE;;;;;;qCAEoC;qBAEjC;;;;;;AAEpB,SAASC,kBAAkBC,UAAkB;IAC3C,MAAMC,QAAQC,aAAE,CAACC,WAAW,CAACH,YAAYI,MAAM,CAAC,CAACC,OAASA,KAAKC,QAAQ,CAAC;IACxE,KAAK,MAAMD,QAAQJ,MAAO;QACxBC,aAAE,CAACK,MAAM,CAACC,eAAI,CAACC,IAAI,CAACT,YAAYK;IAClC;AACF;AAEO,eAAeP,wBACpBY,WAAmB,EACnBC,MAA4B,EAC5B,EACEC,QAAQ,EACRC,eAAe,EACfC,OAAO,EACPC,wBAAwB,EACxBd,KAAK,EAON;IAED,IAAIY,mBAAmB,MAAM;QAC3BG,QAAG,CAACC,IAAI,CAAC;QACT;IACF;IAEA,8DAA8D;IAC9D,mFAAmF;IACnF,IAAIL,aAAa,SAAS,CAACV,aAAE,CAACgB,UAAU,CAACL,kBAAkB;QACzDX,aAAE,CAACiB,SAAS,CAACN,iBAAiB;YAAEO,WAAW;QAAK;IAClD;IAEA,IAAIC,eAA4B,EAAE;IAElC,oDAAoD;IACpD,IAAIT,aAAa,SAASG,4BAA4B,MAAM;QAC1D,0EAA0E;QAC1E,kDAAkD;QAClD,MAAMf,aAAaQ,eAAI,CAACC,IAAI,CAACM,0BAA0B;QACvD,IAAI,CAACb,aAAE,CAACgB,UAAU,CAAClB,aAAa;YAC9BgB,QAAG,CAACM,KAAK,CACP,CAAC,oDAAoD,EAAEP,yBAAyB,8CAA8C,CAAC;YAEjI;QACF;QAEAC,QAAG,CAACO,GAAG,CAAC,kCAAkCvB;QAC1CD,kBAAkBC;QAClB,KAAK,MAAMwB,SAASb,OAAQ;YAC1B,IAAIc,eAAeD,QAAQ;gBACzB,MAAME,WAAWC,YACf3B,YACAwB,OACA5B,0BAA0BgB,UAAUY,MAAMI,MAAM;gBAElDC,cAAcH;YAChB,OAAO;gBACLL,aAAaS,IAAI,CAACN;YACpB;QACF;QACAR,QAAG,CAACO,GAAG,CAAC;IACV,OAAO;QACLF,eAAe;eAAIV;SAAO;IAC5B;IACA,IAAIC,aAAa,WAAW;QAC1B,MAAMjB,oBAAoB0B,cAAcR;IAC1C;IAEA,MAAMkB,UAAkC,CAAC;IAEzC,KAAK,MAAMP,SAASH,aAAc;QAChC,MAAMW,cAAc,IAAIC,IAAIrC,0BAA0BgB,UAAUY,MAAMI,MAAM;QAC5E,IAAK,IAAIM,MAAM,GAAGA,MAAMV,MAAMI,MAAM,CAACO,MAAM,EAAED,MAAO;YAClD,MAAME,QAAQZ,MAAMI,MAAM,CAACM,IAAI;YAC/B,IAAIF,YAAYK,GAAG,CAACD,QAAQ;gBAC1B,MAAME,MAAMd,MAAMvB,KAAK,CAACiC,IAAI;gBAC5B,MAAMK,OAAOC,IAAAA,sCAAiB,EAAChB,OAAO;oBAAEZ;oBAAUwB;oBAAOtB;gBAAQ;gBACjE,IAAIb,OAAO;oBACT,MAAMwC,OAAO,MAAMvC,aAAE,CAACwC,QAAQ,CAACC,QAAQ,CAACL;oBACxCrC,MAAM2C,GAAG,CAACL,MAAM;wBACdM,UAAUJ;wBACVK,SAASjD,yBAAyBa,aAAac;wBAC/CuB,cAAcnC,aAAa,QAAQ,WAAWoC;oBAChD;gBACF,OAAO;oBACLjB,OAAO,CAACO,IAAI,GAAG9B,eAAI,CAACC,IAAI,CAACI,iBAAiB0B;gBAC5C;YACF;QACF;IACF;IAEA,IAAI,CAACtC,OAAO;QACV,MAAMP,mBAAmBqC;IAC3B;AACF;AAEO,eAAepC,oBACpBgB,MAAmB,EACnBE,eAAuB;IAEvB,IAAI,CAACF,OAAOwB,MAAM,EAAE;QAClB;IACF;IACA,MAAMc,aAAa,EAAE;IACrB,KAAK,MAAMzB,SAASb,OAAQ;QAC1B,MAAMuC,SAASC,sCAAiB,CAACd,GAAG,CAACb,MAAM4B,IAAI,IAAI,aAAa;QAChEH,WAAWnB,IAAI,CAAC,CAAC,CAAC,EAAEoB,OAAO,CAAC,EAAEG,sBAAsB7B,QAAQ;IAC9D;IACA,MAAM8B,WAAW9C,eAAI,CAACC,IAAI,CAACI,iBAAiB;IAC5C,MAAM0C,UAAU,CAAC,sEAAsE,EAAEN,WAAWxC,IAAI,CAAC,KAAK,IAAI,CAAC;IACnH,MAAMP,aAAE,CAACwC,QAAQ,CAACc,KAAK,CAAChD,eAAI,CAACiD,OAAO,CAACH,WAAW;QAAElC,WAAW;IAAK;IAClE,MAAMlB,aAAE,CAACwC,QAAQ,CAACgB,SAAS,CAACJ,UAAUC;AACxC;AAEO,SAAS1D,yBACda,WAAmB,EACnBc,KAAuE;IAEvE,OAAO,wBAAwBA,SAASA,MAAMmC,kBAAkB,IAAI,QAAQnC,MAAMoC,IAAI,IAAI,OACtFpD,eAAI,CAACqD,QAAQ,CAACnD,aAAaF,eAAI,CAACC,IAAI,CAACe,MAAMmC,kBAAkB,EAAEnC,MAAMoC,IAAI,KACtEpC,CAAAA,MAAM4B,IAAI,GAAG,MAAM5B,MAAM4B,IAAI,GAAG,EAAC,IACpCJ;AACN;AAEA,SAASnB,cAAcH,QAAkB;IACvCxB,aAAE,CAACiB,SAAS,CAACO,SAASZ,OAAO,EAAE;QAAEM,WAAW;IAAK;IAEjD,KAAK,MAAMf,QAAQqB,SAASzB,KAAK,CAAE;QACjC,MAAMsC,OAAO/B,eAAI,CAACC,IAAI,CAACiB,SAASZ,OAAO,EAAET,KAAKuD,IAAI;QAClD1D,aAAE,CAAC4D,YAAY,CAACzD,KAAKiC,GAAG,EAAEC;IAC5B;IAEArC,aAAE,CAAC6D,aAAa,CACdvD,eAAI,CAACC,IAAI,CAACiB,SAASZ,OAAO,EAAE,kBAC5BkD,KAAKC,SAAS,CAAC;QACbC,QAAQxC,SAASzB,KAAK,CAACkE,GAAG,CAAC,CAAC9D,OAAU,CAAA;gBACpC+D,UAAU/D,KAAKuD,IAAI;gBACnBS,OAAO;gBACPjC,OAAO,GAAG/B,KAAK+B,KAAK,CAAC,CAAC,CAAC;YACzB,CAAA;QACAkC,MAAM;YACJC,QAAQ;YACRC,SAAS;QACX;IACF;AAEJ;AAEA,SAAS/C,eAAeD,KAA8B;IACpD,OAAOA,MAAM4B,IAAI,KAAK,SAAS5B,MAAM4B,IAAI,KAAK,SAAS5B,MAAM4B,IAAI,KAAK;AACxE;AAOA,SAASzB,YACP3B,UAAkB,EAClBwB,KAAwE,EACxEI,MAAgB;IAEhB,MAAM6C,WAAWpB,sBAAsB7B;IACvC,OAAO;QACLV,SAASN,eAAI,CAACC,IAAI,CAACT,YAAY,GAAGyE,SAAS,SAAS,CAAC;QACrDxE,OAAO2B,OAAOuC,GAAG,CAAC,CAAC/B,OAAOF;YACxB,MAAMwC,SAAStC,UAAU,IAAI,KAAK,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC;YAC9C,OAAO;gBACLwB,MAAM,GAAGa,WAAWC,OAAO,CAAC,EAAElD,MAAM4B,IAAI,EAAE;gBAC1ChB;gBACAE,KAAKd,MAAMvB,KAAK,CAACiC,IAAI;YACvB;QACF;IACF;AACF;AAEO,SAASxC,mBAAmBiF,WAAmC;IACpE,MAAMC,QAAQC,OAAOC,IAAI,CAACH;IAC1B,IAAIC,MAAMzC,MAAM,KAAK,GAAG;QACtB;IACF;IAEAnB,QAAG,CAACO,GAAG,CAAC,CAAC,QAAQ,EAAEqD,MAAMzC,MAAM,CAAC,YAAY,CAAC;IAC7C,OAAO,IAAI4C,QAAc,CAACC,SAASC;QACjC,MAAMC,WAAW,CAAC5D;YAChB,IAAIA,OAAO;gBACT,OAAO2D,OAAO3D;YAChB;YACA,IAAIsD,MAAMzC,MAAM,EAAE;gBAChB,sEAAsE;gBACtE,MAAMG,MAAMsC,MAAMO,KAAK;gBACvB,MAAM5C,OAAOoC,WAAW,CAACrC,IAAI;gBAC7B8C,KAAK9C,KAAKC,MAAM2C;YAClB,OAAO;gBACLF;YACF;QACF;QACAE;IACF;AACF;AAEA,SAASE,KAAK9C,GAAW,EAAEC,IAAY,EAAE8C,QAAiD;IACxFnF,aAAE,CAACsD,KAAK,CAAChD,eAAI,CAACiD,OAAO,CAAClB,OAAO;QAAEnB,WAAW;IAAK,GAAG,CAACkE;QACjD,IAAIA,KAAK;YACPD,SAASC;YACT;QACF;QACApF,aAAE,CAACqF,gBAAgB,CAACjD,KAAKkD,IAAI,CAACtF,aAAE,CAACuF,iBAAiB,CAAClD,OAAOmD,EAAE,CAAC,UAAUL;IACzE;AACF;AAEA,MAAMM,iBAA8C;IAClDC,KAAK;QAAC;QAAG;QAAG;KAAE;AAChB;AAEO,SAAShG,0BAA0BgB,QAAgB,EAAEgB,MAAgB;IAC1E,MAAMiE,YAAsBF,cAAc,CAAC/E,SAAS;IACpD,IAAI,CAACiF,WAAW;QACd,OAAOjE;IACT;IACA,MAAMkE,SAASlE,OAAOxB,MAAM,CAAC,CAACgC,QAAUyD,UAAUE,QAAQ,CAAC3D;IAC3D,IAAI,CAAC0D,OAAO3D,MAAM,IAAIP,OAAOO,MAAM,EAAE;QACnC,0EAA0E;QAC1E,2EAA2E;QAC3E,0CAA0C;QAC1C,MAAM6D,WAAWH,SAAS,CAACA,UAAU1D,MAAM,GAAG,EAAE;QAChD,KAAK,MAAMC,SAASR,OAAQ;YAC1B,IAAIQ,QAAQ4D,UAAU;gBACpBF,OAAOhE,IAAI,CAACM;gBACZ;YACF;QACF;QAEA,+DAA+D;QAC/D,IAAI,CAAC0D,OAAO3D,MAAM,EAAE;YAClB2D,OAAOhE,IAAI,CAACF,MAAM,CAACA,OAAOO,MAAM,GAAG,EAAE;QACvC;IACF;IACA,OAAO2D;AACT;AAEA,SAASzC,sBAAsB7B,KAAqD;IAClF,MAAMyE,aAAaC,WAAW1E;IAC9B,OAAO,GAAGyE,WAAW,CAAC,EAAEzE,MAAMoC,IAAI,EAAE,CACjCuC,WAAW,GACXC,OAAO,CAAC,OAAO,KAAK,uCAAuC;KAC3DA,OAAO,CAAC,iBAAiB,IAAI,uBAAuB;KACpDA,OAAO,CAAC,YAAY,KAAK,0BAA0B;AACxD;AAEA,SAASF,WAAW1E,KAA4C;IAC9D,IAAIV,UAAUU,MAAM6E,kBAAkB;IACtC,IAAIvF,OAAO,CAAC,EAAE,KAAK,KAAK;QACtBA,UAAUA,QAAQwF,SAAS,CAAC;IAC9B;IACA,OAAOxF;AACT"}
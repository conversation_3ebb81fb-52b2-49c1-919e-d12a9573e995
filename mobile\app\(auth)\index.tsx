import { useGoogleSSO } from "@/hooks/useSocialAuth";
import { ActivityIndicator, Image, Text, TouchableOpacity, View } from "react-native";
import "../../global.css";

export default function Index() {
  const { isLoading, signInWithGoogleSSO } = useGoogleSSO()
  return (
    <View className="flex-1 items-center justify-center bg-white">
      <View className=" px-6 bg-white h-[50vh] w-full">
        <Image resizeMode={"cover"} className=" w-full h-full object-center" source={require("../../public/image.png")}></Image>

      </View>
      <View className="py-4 px-14 gap-4 bg-white h-40 w-full">
        <TouchableOpacity
          disabled={isLoading}
          className="flex-row items-center justify-center bg-white border border-gray-200 rounded-full py-3 px-6"
          onPress={signInWithGoogleSSO}

          style={{
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.1,
            shadowRadius: 2,
            elevation: 2,
          }}
        >
          {isLoading ? <ActivityIndicator className="text-black py-2" /> : (<View className="flex-row  items-center gap-4 justify-between">
            <Image
              source={require("../../public/google.png")}
              className="size-10 "
              resizeMode="contain"
            />
            <Text className="text-black font-medium text-xl">Continue with Google</Text>
          </View>)}
        </TouchableOpacity>



      </View>
      <Text className="text-neutral-400 pt-8  font-medium w-full h-42 text-center   px-10 text-lg">
        By Tapping Continue and using the app, you agree to our <Text className="text-neutral-700 font-bold text-center"> Terms </Text> and
        <Text className="text-neutral-700 font-bold"> Privacy Policy</Text>
      </Text>
    </View>
  );
}

import { Text, View, Image, TouchableOpacity, ActivityIndicator } from "react-native";
import "../../global.css"
import { useSocialAuth } from "@/hooks/useSocialAuth";
export default function Index() {
  const { Loading, handleAuth } = useSocialAuth()
  return (
    <View className="flex-1 items-center justify-center bg-white">
      <View className=" px-6 bg-white h-[50vh] w-full">
        <Image resizeMode={"cover"} className=" w-full h-full object-center" source={require("../../public/image.png")}></Image>

      </View>
      <View className="py-4 px-14 gap-4 bg-white h-40 w-full">
        <TouchableOpacity
          disabled={Loading}
          className="flex-row items-center justify-center bg-white border border-gray-200 rounded-full py-3 px-6"
          onPress={() => { handleAuth("oauth_google") }}

          style={{
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.1,
            shadowRadius: 2,
            elevation: 2,
          }}
        >
          {Loading ? <ActivityIndicator className="text-black py-2" /> : (<View className="flex-row  items-center gap-4 justify-between">
            <Image
              source={require("../../public/google.png")}
              className="size-10 "
              resizeMode="contain"
            />
            <Text className="text-black font-medium text-xl">Continue with Google</Text>
          </View>)}
        </TouchableOpacity>

        <TouchableOpacity
          disabled={Loading}
          className="flex-row items-center justify-center bg-black border border-gray-300 rounded-full py-3 "
          onPress={() => { handleAuth("oauth_apple") }}

          style={{
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.1,
            shadowRadius: 2,
            elevation: 2,
          }}
        >
          {
            Loading ? <ActivityIndicator className="text-white py-2" /> : (<View className="flex-row gap-4 items-center justify-center">
              <Image
                source={require("../../public/apple.png")}
                className="size-10 "
                resizeMode="contain"
              />
              <Text className="text-white font-medium text-xl">Continue with Apple</Text>
            </View>)
          }

        </TouchableOpacity>

      </View>
      <Text className="text-neutral-400 pt-8  font-medium w-full h-42 text-center   px-10 text-lg">
        By Tapping Continue and using the app, you agree to our <Text className="text-neutral-700 font-bold text-center"> Terms </Text> and
        <Text className="text-neutral-700 font-bold"> Privacy Policy</Text>
      </Text>
    </View>
  );
}

import { View, Text, SafeAreaView, TouchableOpacity, Alert, Image } from 'react-native';
import { useUser, useClerk } from '@clerk/clerk-expo';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

export default function ProfileScreen() {
  const { user } = useUser();
  const { signOut } = useClerk();
  const router = useRouter();

  const handleSignOut = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            try {
              await signOut();
              router.replace('/(auth)');
            } catch (error) {
              Alert.alert('Error', 'Failed to sign out. Please try again.');
            }
          },
        },
      ]
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar style="dark" />
      
      {/* Header */}
      <View className="px-6 py-4 border-b border-gray-100">
        <Text className="text-2xl font-bold text-gray-900">Profile</Text>
      </View>

      {/* Profile Content */}
      <View className="flex-1 px-6 py-8">
        {/* Profile Picture and Info */}
        <View className="items-center mb-8">
          {user?.imageUrl ? (
            <Image
              source={{ uri: user.imageUrl }}
              className="w-24 h-24 rounded-full mb-4"
            />
          ) : (
            <View className="w-24 h-24 bg-gray-200 rounded-full items-center justify-center mb-4">
              <Ionicons name="person" size={40} color="#9CA3AF" />
            </View>
          )}
          
          <Text className="text-xl font-bold text-gray-900">
            {user?.fullName || 'User'}
          </Text>
          <Text className="text-gray-600 mt-1">
            {user?.emailAddresses[0]?.emailAddress}
          </Text>
        </View>

        {/* Profile Options */}
        <View className="space-y-4">
          <TouchableOpacity className="bg-gray-50 p-4 rounded-xl flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Ionicons name="person-outline" size={24} color="#6B7280" />
              <Text className="text-gray-900 font-medium ml-3">Edit Profile</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
          </TouchableOpacity>

          <TouchableOpacity className="bg-gray-50 p-4 rounded-xl flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Ionicons name="settings-outline" size={24} color="#6B7280" />
              <Text className="text-gray-900 font-medium ml-3">Settings</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
          </TouchableOpacity>

          <TouchableOpacity className="bg-gray-50 p-4 rounded-xl flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Ionicons name="help-circle-outline" size={24} color="#6B7280" />
              <Text className="text-gray-900 font-medium ml-3">Help & Support</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
          </TouchableOpacity>
        </View>

        {/* Sign Out Button */}
        <TouchableOpacity
          onPress={handleSignOut}
          className="bg-red-50 p-4 rounded-xl flex-row items-center justify-center mt-8"
        >
          <Ionicons name="log-out-outline" size={24} color="#EF4444" />
          <Text className="text-red-600 font-semibold ml-3">Sign Out</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

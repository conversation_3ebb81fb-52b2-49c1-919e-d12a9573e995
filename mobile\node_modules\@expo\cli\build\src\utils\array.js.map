{"version": 3, "sources": ["../../../src/utils/array.ts"], "sourcesContent": ["/** Returns the last index of an item based on a given criteria. */\nexport function findLastIndex<T>(array: T[], predicate: (item: T) => boolean) {\n  for (let i = array.length - 1; i >= 0; i--) {\n    if (predicate(array[i])) {\n      return i;\n    }\n  }\n  return -1;\n}\n\n/** Returns a list of items that intersect between two given arrays. */\nexport function intersecting<T>(a: T[], b: T[]): T[] {\n  const [c, d] = a.length > b.length ? [a, b] : [b, a];\n  return c.filter((value) => d.includes(value));\n}\n\nexport function replaceValue<T>(values: T[], original: T, replacement: T): T[] {\n  const index = values.indexOf(original);\n  if (index > -1) {\n    values[index] = replacement;\n  }\n  return values;\n}\n\n/** lodash.uniqBy */\nexport function uniqBy<T>(array: T[], key: (item: T) => string): T[] {\n  const seen: { [key: string]: boolean } = {};\n  return array.filter((item) => {\n    const k = key(item);\n    if (seen[k]) {\n      return false;\n    }\n    seen[k] = true;\n    return true;\n  });\n}\n\n/** `lodash.chunk` */\nexport function chunk<T>(array: T[], size: number): T[][] {\n  const chunked = [];\n  let index = 0;\n  while (index < array.length) {\n    chunked.push(array.slice(index, (index += size)));\n  }\n  return chunked;\n}\n\n/** `lodash.groupBy` */\nexport function groupBy<T, K extends keyof any>(list: T[], getKey: (item: T) => K): Record<K, T[]> {\n  return list.reduce(\n    (previous, currentItem) => {\n      const group = getKey(currentItem);\n      if (!previous[group]) {\n        previous[group] = [];\n      }\n      previous[group].push(currentItem);\n      return previous;\n    },\n    {} as Record<K, T[]>\n  );\n}\n"], "names": ["chunk", "findLastIndex", "groupBy", "intersecting", "replaceValue", "uniqBy", "array", "predicate", "i", "length", "a", "b", "c", "d", "filter", "value", "includes", "values", "original", "replacement", "index", "indexOf", "key", "seen", "item", "k", "size", "chunked", "push", "slice", "list", "<PERSON><PERSON><PERSON>", "reduce", "previous", "currentItem", "group"], "mappings": "AAAA,iEAAiE;;;;;;;;;;;IAsCjDA,KAAK;eAALA;;IArCAC,aAAa;eAAbA;;IA+CAC,OAAO;eAAPA;;IArCAC,YAAY;eAAZA;;IAKAC,YAAY;eAAZA;;IASAC,MAAM;eAANA;;;AAxBT,SAASJ,cAAiBK,KAAU,EAAEC,SAA+B;IAC1E,IAAK,IAAIC,IAAIF,MAAMG,MAAM,GAAG,GAAGD,KAAK,GAAGA,IAAK;QAC1C,IAAID,UAAUD,KAAK,CAACE,EAAE,GAAG;YACvB,OAAOA;QACT;IACF;IACA,OAAO,CAAC;AACV;AAGO,SAASL,aAAgBO,CAAM,EAAEC,CAAM;IAC5C,MAAM,CAACC,GAAGC,EAAE,GAAGH,EAAED,MAAM,GAAGE,EAAEF,MAAM,GAAG;QAACC;QAAGC;KAAE,GAAG;QAACA;QAAGD;KAAE;IACpD,OAAOE,EAAEE,MAAM,CAAC,CAACC,QAAUF,EAAEG,QAAQ,CAACD;AACxC;AAEO,SAASX,aAAgBa,MAAW,EAAEC,QAAW,EAAEC,WAAc;IACtE,MAAMC,QAAQH,OAAOI,OAAO,CAACH;IAC7B,IAAIE,QAAQ,CAAC,GAAG;QACdH,MAAM,CAACG,MAAM,GAAGD;IAClB;IACA,OAAOF;AACT;AAGO,SAASZ,OAAUC,KAAU,EAAEgB,GAAwB;IAC5D,MAAMC,OAAmC,CAAC;IAC1C,OAAOjB,MAAMQ,MAAM,CAAC,CAACU;QACnB,MAAMC,IAAIH,IAAIE;QACd,IAAID,IAAI,CAACE,EAAE,EAAE;YACX,OAAO;QACT;QACAF,IAAI,CAACE,EAAE,GAAG;QACV,OAAO;IACT;AACF;AAGO,SAASzB,MAASM,KAAU,EAAEoB,IAAY;IAC/C,MAAMC,UAAU,EAAE;IAClB,IAAIP,QAAQ;IACZ,MAAOA,QAAQd,MAAMG,MAAM,CAAE;QAC3BkB,QAAQC,IAAI,CAACtB,MAAMuB,KAAK,CAACT,OAAQA,SAASM;IAC5C;IACA,OAAOC;AACT;AAGO,SAASzB,QAAgC4B,IAAS,EAAEC,MAAsB;IAC/E,OAAOD,KAAKE,MAAM,CAChB,CAACC,UAAUC;QACT,MAAMC,QAAQJ,OAAOG;QACrB,IAAI,CAACD,QAAQ,CAACE,MAAM,EAAE;YACpBF,QAAQ,CAACE,MAAM,GAAG,EAAE;QACtB;QACAF,QAAQ,CAACE,MAAM,CAACP,IAAI,CAACM;QACrB,OAAOD;IACT,GACA,CAAC;AAEL"}
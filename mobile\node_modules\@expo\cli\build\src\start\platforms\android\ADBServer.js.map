{"version": 3, "sources": ["../../../../../src/start/platforms/android/ADBServer.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport { execFileSync } from 'child_process';\n\nimport { assertSdkRoot } from './AndroidSdk';\nimport { Log } from '../../../log';\nimport { env } from '../../../utils/env';\nimport { AbortCommandError, CommandError } from '../../../utils/errors';\nimport { installExitHooks } from '../../../utils/exit';\n\nconst debug = require('debug')('expo:start:platforms:android:adbServer') as typeof console.log;\n\nconst BEGINNING_OF_ADB_ERROR_MESSAGE = 'error: ';\n\n// This is a tricky class since it controls a system state (side-effects).\n// A more ideal solution would be to implement ADB in JS.\n// The main reason this is a class is to control the flow of testing.\n\nexport class ADBServer {\n  isRunning: boolean = false;\n  removeExitHook: () => void = () => {};\n\n  /** Returns the command line reference to ADB. */\n  getAdbExecutablePath(): string {\n    try {\n      const sdkRoot = assertSdkRoot();\n      if (sdkRoot) {\n        return `${sdkRoot}/platform-tools/adb`;\n      }\n    } catch (error: any) {\n      Log.warn(error.message);\n    }\n\n    Log.debug('Failed to resolve the Android SDK path, falling back to global adb executable');\n    return 'adb';\n  }\n\n  /** Start the ADB server. */\n  async startAsync(): Promise<boolean> {\n    if (this.isRunning) {\n      return false;\n    }\n    // clean up\n    this.removeExitHook = installExitHooks(() => {\n      if (this.isRunning) {\n        this.stopAsync();\n      }\n    });\n    const adb = this.getAdbExecutablePath();\n    const result = await this.resolveAdbPromise(spawnAsync(adb, ['start-server']));\n    const lines = result.stderr.trim().split(/\\r?\\n/);\n    const isStarted = lines.includes('* daemon started successfully');\n    this.isRunning = isStarted;\n    return isStarted;\n  }\n\n  /** Kill the ADB server. */\n  async stopAsync(): Promise<boolean> {\n    debug('Stopping ADB server');\n\n    if (!this.isRunning) {\n      debug('ADB server is not running');\n      return false;\n    }\n    this.removeExitHook();\n    try {\n      await this.runAsync(['kill-server']);\n      return true;\n    } catch (error: any) {\n      Log.error('Failed to stop ADB server: ' + error.message);\n      return false;\n    } finally {\n      debug('Stopped ADB server');\n      this.isRunning = false;\n    }\n  }\n\n  /** Execute an ADB command with given args. */\n  async runAsync(args: string[]): Promise<string> {\n    // TODO: Add a global package that installs adb to the path.\n    const adb = this.getAdbExecutablePath();\n\n    await this.startAsync();\n\n    debug([adb, ...args].join(' '));\n    const result = await this.resolveAdbPromise(spawnAsync(adb, args));\n    return result.output.join('\\n');\n  }\n\n  /** Get ADB file output. Useful for reading device state/settings. */\n  async getFileOutputAsync(args: string[]): Promise<string> {\n    // TODO: Add a global package that installs adb to the path.\n    const adb = this.getAdbExecutablePath();\n\n    await this.startAsync();\n\n    const results = await this.resolveAdbPromise(\n      execFileSync(adb, args, {\n        encoding: 'latin1',\n        stdio: 'pipe',\n      })\n    );\n    debug('[ADB] File output:\\n', results);\n    return results;\n  }\n\n  /** Formats error info. */\n  async resolveAdbPromise<T>(promise: T | Promise<T>): Promise<T> {\n    try {\n      return await promise;\n    } catch (error: any) {\n      // User pressed ctrl+c to cancel the process...\n      if (error.signal === 'SIGINT') {\n        throw new AbortCommandError();\n      }\n      if (error.status === 255 && error.stdout.includes('Bad user number')) {\n        const userNumber = error.stdout.match(/Bad user number: (.+)/)?.[1] ?? env.EXPO_ADB_USER;\n        throw new CommandError(\n          'EXPO_ADB_USER',\n          `Invalid ADB user number \"${userNumber}\" set with environment variable EXPO_ADB_USER. Run \"adb shell pm list users\" to see valid user numbers.`\n        );\n      }\n      // TODO: Support heap corruption for adb 29 (process exits with code -1073740940) (windows and linux)\n      let errorMessage = (error.stderr || error.stdout || error.message).trim();\n      if (errorMessage.startsWith(BEGINNING_OF_ADB_ERROR_MESSAGE)) {\n        errorMessage = errorMessage.substring(BEGINNING_OF_ADB_ERROR_MESSAGE.length);\n      }\n\n      error.message = errorMessage;\n      throw error;\n    }\n  }\n}\n"], "names": ["ADBServer", "debug", "require", "BEGINNING_OF_ADB_ERROR_MESSAGE", "getAdbExecutablePath", "sdkRoot", "assertSdkRoot", "error", "Log", "warn", "message", "startAsync", "isRunning", "removeExitHook", "installExitHooks", "stopAsync", "adb", "result", "resolveAdbPromise", "spawnAsync", "lines", "stderr", "trim", "split", "isStarted", "includes", "runAsync", "args", "join", "output", "getFileOutputAsync", "results", "execFileSync", "encoding", "stdio", "promise", "signal", "AbortCommandError", "status", "stdout", "userNumber", "match", "env", "EXPO_ADB_USER", "CommandError", "errorMessage", "startsWith", "substring", "length"], "mappings": ";;;;+BAiBaA;;;eAAAA;;;;gEAjBU;;;;;;;yBACM;;;;;;4BAEC;qBACV;qBACA;wBAC4B;sBACf;;;;;;AAEjC,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,MAAMC,iCAAiC;AAMhC,MAAMH;IAIX,+CAA+C,GAC/CI,uBAA+B;QAC7B,IAAI;YACF,MAAMC,UAAUC,IAAAA,yBAAa;YAC7B,IAAID,SAAS;gBACX,OAAO,GAAGA,QAAQ,mBAAmB,CAAC;YACxC;QACF,EAAE,OAAOE,OAAY;YACnBC,QAAG,CAACC,IAAI,CAACF,MAAMG,OAAO;QACxB;QAEAF,QAAG,CAACP,KAAK,CAAC;QACV,OAAO;IACT;IAEA,0BAA0B,GAC1B,MAAMU,aAA+B;QACnC,IAAI,IAAI,CAACC,SAAS,EAAE;YAClB,OAAO;QACT;QACA,WAAW;QACX,IAAI,CAACC,cAAc,GAAGC,IAAAA,sBAAgB,EAAC;YACrC,IAAI,IAAI,CAACF,SAAS,EAAE;gBAClB,IAAI,CAACG,SAAS;YAChB;QACF;QACA,MAAMC,MAAM,IAAI,CAACZ,oBAAoB;QACrC,MAAMa,SAAS,MAAM,IAAI,CAACC,iBAAiB,CAACC,IAAAA,qBAAU,EAACH,KAAK;YAAC;SAAe;QAC5E,MAAMI,QAAQH,OAAOI,MAAM,CAACC,IAAI,GAAGC,KAAK,CAAC;QACzC,MAAMC,YAAYJ,MAAMK,QAAQ,CAAC;QACjC,IAAI,CAACb,SAAS,GAAGY;QACjB,OAAOA;IACT;IAEA,yBAAyB,GACzB,MAAMT,YAA8B;QAClCd,MAAM;QAEN,IAAI,CAAC,IAAI,CAACW,SAAS,EAAE;YACnBX,MAAM;YACN,OAAO;QACT;QACA,IAAI,CAACY,cAAc;QACnB,IAAI;YACF,MAAM,IAAI,CAACa,QAAQ,CAAC;gBAAC;aAAc;YACnC,OAAO;QACT,EAAE,OAAOnB,OAAY;YACnBC,QAAG,CAACD,KAAK,CAAC,gCAAgCA,MAAMG,OAAO;YACvD,OAAO;QACT,SAAU;YACRT,MAAM;YACN,IAAI,CAACW,SAAS,GAAG;QACnB;IACF;IAEA,4CAA4C,GAC5C,MAAMc,SAASC,IAAc,EAAmB;QAC9C,4DAA4D;QAC5D,MAAMX,MAAM,IAAI,CAACZ,oBAAoB;QAErC,MAAM,IAAI,CAACO,UAAU;QAErBV,MAAM;YAACe;eAAQW;SAAK,CAACC,IAAI,CAAC;QAC1B,MAAMX,SAAS,MAAM,IAAI,CAACC,iBAAiB,CAACC,IAAAA,qBAAU,EAACH,KAAKW;QAC5D,OAAOV,OAAOY,MAAM,CAACD,IAAI,CAAC;IAC5B;IAEA,mEAAmE,GACnE,MAAME,mBAAmBH,IAAc,EAAmB;QACxD,4DAA4D;QAC5D,MAAMX,MAAM,IAAI,CAACZ,oBAAoB;QAErC,MAAM,IAAI,CAACO,UAAU;QAErB,MAAMoB,UAAU,MAAM,IAAI,CAACb,iBAAiB,CAC1Cc,IAAAA,6BAAY,EAAChB,KAAKW,MAAM;YACtBM,UAAU;YACVC,OAAO;QACT;QAEFjC,MAAM,wBAAwB8B;QAC9B,OAAOA;IACT;IAEA,wBAAwB,GACxB,MAAMb,kBAAqBiB,OAAuB,EAAc;QAC9D,IAAI;YACF,OAAO,MAAMA;QACf,EAAE,OAAO5B,OAAY;YACnB,+CAA+C;YAC/C,IAAIA,MAAM6B,MAAM,KAAK,UAAU;gBAC7B,MAAM,IAAIC,yBAAiB;YAC7B;YACA,IAAI9B,MAAM+B,MAAM,KAAK,OAAO/B,MAAMgC,MAAM,CAACd,QAAQ,CAAC,oBAAoB;oBACjDlB;gBAAnB,MAAMiC,aAAajC,EAAAA,sBAAAA,MAAMgC,MAAM,CAACE,KAAK,CAAC,6CAAnBlC,mBAA6C,CAAC,EAAE,KAAImC,QAAG,CAACC,aAAa;gBACxF,MAAM,IAAIC,oBAAY,CACpB,iBACA,CAAC,yBAAyB,EAAEJ,WAAW,uGAAuG,CAAC;YAEnJ;YACA,qGAAqG;YACrG,IAAIK,eAAe,AAACtC,CAAAA,MAAMc,MAAM,IAAId,MAAMgC,MAAM,IAAIhC,MAAMG,OAAO,AAAD,EAAGY,IAAI;YACvE,IAAIuB,aAAaC,UAAU,CAAC3C,iCAAiC;gBAC3D0C,eAAeA,aAAaE,SAAS,CAAC5C,+BAA+B6C,MAAM;YAC7E;YAEAzC,MAAMG,OAAO,GAAGmC;YAChB,MAAMtC;QACR;IACF;;aAhHAK,YAAqB;aACrBC,iBAA6B,KAAO;;AAgHtC"}
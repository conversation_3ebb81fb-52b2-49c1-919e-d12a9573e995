{"version": 3, "sources": ["../../../../src/api/user/otp.ts"], "sourcesContent": ["import assert from 'assert';\nimport chalk from 'chalk';\n\nimport { loginAsync } from './user';\nimport * as Log from '../../log';\nimport { AbortCommandError, CommandError } from '../../utils/errors';\nimport { learnMore } from '../../utils/link';\nimport { promptAsync, selectAsync } from '../../utils/prompts';\nimport { fetchAsync } from '../rest/client';\n\nexport enum UserSecondFactorDeviceMethod {\n  AUTHENTICATOR = 'authenticator',\n  SMS = 'sms',\n}\n\n/** Device properties for 2FA */\nexport type SecondFactorDevice = {\n  id: string;\n  method: UserSecondFactorDeviceMethod;\n  sms_phone_number: string | null;\n  is_primary: boolean;\n};\n\nconst nonInteractiveHelp = `Use the EXPO_TOKEN environment variable to authenticate in CI (${learnMore(\n  'https://docs.expo.dev/accounts/programmatic-access/'\n)})`;\n\n/**\n * Prompt for an OTP with the option to cancel the question by answering empty (pressing return key).\n */\nasync function promptForOTPAsync(cancelBehavior: 'cancel' | 'menu'): Promise<string | null> {\n  const enterMessage =\n    cancelBehavior === 'cancel'\n      ? chalk`press {bold Enter} to cancel`\n      : chalk`press {bold Enter} for more options`;\n  const { otp } = await promptAsync(\n    {\n      type: 'text',\n      name: 'otp',\n      message: `One-time password or backup code (${enterMessage}):`,\n    },\n    { nonInteractiveHelp }\n  );\n  return otp || null;\n}\n\n/**\n * Prompt for user to choose a backup OTP method. If selected method is SMS, a request\n * for a new OTP will be sent to that method. Then, prompt for the OTP, and retry the user login.\n */\nasync function promptForBackupOTPAsync(\n  username: string,\n  password: string,\n  secondFactorDevices: SecondFactorDevice[]\n): Promise<string | null> {\n  const nonPrimarySecondFactorDevices = secondFactorDevices.filter((device) => !device.is_primary);\n\n  if (nonPrimarySecondFactorDevices.length === 0) {\n    throw new CommandError(\n      'No other second-factor devices set up. Ensure you have set up and certified a backup device.'\n    );\n  }\n\n  const hasAuthenticatorSecondFactorDevice = nonPrimarySecondFactorDevices.find(\n    (device) => device.method === UserSecondFactorDeviceMethod.AUTHENTICATOR\n  );\n\n  const smsNonPrimarySecondFactorDevices = nonPrimarySecondFactorDevices.filter(\n    (device) => device.method === UserSecondFactorDeviceMethod.SMS\n  );\n\n  const authenticatorChoiceSentinel = -1;\n  const cancelChoiceSentinel = -2;\n\n  const deviceChoices = smsNonPrimarySecondFactorDevices.map((device, idx) => ({\n    title: device.sms_phone_number!,\n    value: idx,\n  }));\n\n  if (hasAuthenticatorSecondFactorDevice) {\n    deviceChoices.push({\n      title: 'Authenticator',\n      value: authenticatorChoiceSentinel,\n    });\n  }\n\n  deviceChoices.push({\n    title: 'Cancel',\n    value: cancelChoiceSentinel,\n  });\n\n  const selectedValue = await selectAsync('Select a second-factor device:', deviceChoices, {\n    nonInteractiveHelp,\n  });\n  if (selectedValue === cancelChoiceSentinel) {\n    return null;\n  } else if (selectedValue === authenticatorChoiceSentinel) {\n    return await promptForOTPAsync('cancel');\n  }\n\n  const device = smsNonPrimarySecondFactorDevices[selectedValue];\n\n  await fetchAsync('auth/send-sms-otp', {\n    method: 'POST',\n    body: JSON.stringify({\n      username,\n      password,\n      secondFactorDeviceID: device.id,\n    }),\n  });\n\n  return await promptForOTPAsync('cancel');\n}\n\n/**\n * Handle the special case error indicating that a second-factor is required for\n * authentication.\n *\n * There are three cases we need to handle:\n * 1. User's primary second-factor device was SMS, OTP was automatically sent by the server to that\n *    device already. In this case we should just prompt for the SMS OTP (or backup code), which the\n *    user should be receiving shortly. We should give the user a way to cancel and the prompt and move\n *    to case 3 below.\n * 2. User's primary second-factor device is authenticator. In this case we should prompt for authenticator\n *    OTP (or backup code) and also give the user a way to cancel and move to case 3 below.\n * 3. User doesn't have a primary device or doesn't have access to their primary device. In this case\n *    we should show a picker of the SMS devices that they can have an OTP code sent to, and when\n *    the user picks one we show a prompt() for the sent OTP.\n */\nexport async function retryUsernamePasswordAuthWithOTPAsync(\n  username: string,\n  password: string,\n  metadata: {\n    secondFactorDevices?: SecondFactorDevice[];\n    smsAutomaticallySent?: boolean;\n  }\n): Promise<void> {\n  const { secondFactorDevices, smsAutomaticallySent } = metadata;\n  assert(\n    secondFactorDevices !== undefined && smsAutomaticallySent !== undefined,\n    `Malformed OTP error metadata: ${metadata}`\n  );\n\n  const primaryDevice = secondFactorDevices.find((device) => device.is_primary);\n  let otp: string | null = null;\n\n  if (smsAutomaticallySent) {\n    assert(primaryDevice, 'OTP should only automatically be sent when there is a primary device');\n    Log.log(\n      `One-time password was sent to the phone number ending in ${primaryDevice.sms_phone_number}.`\n    );\n    otp = await promptForOTPAsync('menu');\n  }\n\n  if (primaryDevice?.method === UserSecondFactorDeviceMethod.AUTHENTICATOR) {\n    Log.log('One-time password from authenticator required.');\n    otp = await promptForOTPAsync('menu');\n  }\n\n  // user bailed on case 1 or 2, wants to move to case 3\n  if (!otp) {\n    otp = await promptForBackupOTPAsync(username, password, secondFactorDevices);\n  }\n\n  if (!otp) {\n    throw new AbortCommandError();\n  }\n\n  await loginAsync({\n    username,\n    password,\n    otp,\n  });\n}\n"], "names": ["UserSecondFactorDeviceMethod", "retryUsernamePasswordAuthWithOTPAsync", "nonInteractiveHelp", "learnMore", "promptForOTPAsync", "cancelBehavior", "enterMessage", "chalk", "otp", "promptAsync", "type", "name", "message", "promptForBackupOTPAsync", "username", "password", "secondFactorDevices", "nonPrimarySecondFactorDevices", "filter", "device", "is_primary", "length", "CommandError", "hasAuthenticatorSecondFactorDevice", "find", "method", "smsNonPrimarySecondFactorDevices", "authenticatorChoiceSentinel", "cancelChoiceSentinel", "deviceChoices", "map", "idx", "title", "sms_phone_number", "value", "push", "selected<PERSON><PERSON><PERSON>", "selectAsync", "fetchAsync", "body", "JSON", "stringify", "secondFactorDeviceID", "id", "metadata", "smsAutomaticallySent", "assert", "undefined", "primaryDevice", "Log", "log", "AbortCommandError", "loginAsync"], "mappings": ";;;;;;;;;;;IAUYA,4BAA4B;eAA5BA;;IAuHUC,qCAAqC;eAArCA;;;;gEAjIH;;;;;;;gEACD;;;;;;sBAES;6DACN;wBAC2B;sBACtB;yBACe;wBACd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpB,IAAA,AAAKD,sDAAAA;;;WAAAA;;AAaZ,MAAME,qBAAqB,CAAC,+DAA+D,EAAEC,IAAAA,eAAS,EACpG,uDACA,CAAC,CAAC;AAEJ;;CAEC,GACD,eAAeC,kBAAkBC,cAAiC;IAChE,MAAMC,eACJD,mBAAmB,WACfE,IAAAA,gBAAK,CAAA,CAAC,4BAA4B,CAAC,GACnCA,IAAAA,gBAAK,CAAA,CAAC,mCAAmC,CAAC;IAChD,MAAM,EAAEC,GAAG,EAAE,GAAG,MAAMC,IAAAA,oBAAW,EAC/B;QACEC,MAAM;QACNC,MAAM;QACNC,SAAS,CAAC,kCAAkC,EAAEN,aAAa,EAAE,CAAC;IAChE,GACA;QAAEJ;IAAmB;IAEvB,OAAOM,OAAO;AAChB;AAEA;;;CAGC,GACD,eAAeK,wBACbC,QAAgB,EAChBC,QAAgB,EAChBC,mBAAyC;IAEzC,MAAMC,gCAAgCD,oBAAoBE,MAAM,CAAC,CAACC,SAAW,CAACA,OAAOC,UAAU;IAE/F,IAAIH,8BAA8BI,MAAM,KAAK,GAAG;QAC9C,MAAM,IAAIC,oBAAY,CACpB;IAEJ;IAEA,MAAMC,qCAAqCN,8BAA8BO,IAAI,CAC3E,CAACL,SAAWA,OAAOM,MAAM;IAG3B,MAAMC,mCAAmCT,8BAA8BC,MAAM,CAC3E,CAACC,SAAWA,OAAOM,MAAM;IAG3B,MAAME,8BAA8B,CAAC;IACrC,MAAMC,uBAAuB,CAAC;IAE9B,MAAMC,gBAAgBH,iCAAiCI,GAAG,CAAC,CAACX,QAAQY,MAAS,CAAA;YAC3EC,OAAOb,OAAOc,gBAAgB;YAC9BC,OAAOH;QACT,CAAA;IAEA,IAAIR,oCAAoC;QACtCM,cAAcM,IAAI,CAAC;YACjBH,OAAO;YACPE,OAAOP;QACT;IACF;IAEAE,cAAcM,IAAI,CAAC;QACjBH,OAAO;QACPE,OAAON;IACT;IAEA,MAAMQ,gBAAgB,MAAMC,IAAAA,oBAAW,EAAC,kCAAkCR,eAAe;QACvF3B;IACF;IACA,IAAIkC,kBAAkBR,sBAAsB;QAC1C,OAAO;IACT,OAAO,IAAIQ,kBAAkBT,6BAA6B;QACxD,OAAO,MAAMvB,kBAAkB;IACjC;IAEA,MAAMe,SAASO,gCAAgC,CAACU,cAAc;IAE9D,MAAME,IAAAA,kBAAU,EAAC,qBAAqB;QACpCb,QAAQ;QACRc,MAAMC,KAAKC,SAAS,CAAC;YACnB3B;YACAC;YACA2B,sBAAsBvB,OAAOwB,EAAE;QACjC;IACF;IAEA,OAAO,MAAMvC,kBAAkB;AACjC;AAiBO,eAAeH,sCACpBa,QAAgB,EAChBC,QAAgB,EAChB6B,QAGC;IAED,MAAM,EAAE5B,mBAAmB,EAAE6B,oBAAoB,EAAE,GAAGD;IACtDE,IAAAA,iBAAM,EACJ9B,wBAAwB+B,aAAaF,yBAAyBE,WAC9D,CAAC,8BAA8B,EAAEH,UAAU;IAG7C,MAAMI,gBAAgBhC,oBAAoBQ,IAAI,CAAC,CAACL,SAAWA,OAAOC,UAAU;IAC5E,IAAIZ,MAAqB;IAEzB,IAAIqC,sBAAsB;QACxBC,IAAAA,iBAAM,EAACE,eAAe;QACtBC,KAAIC,GAAG,CACL,CAAC,yDAAyD,EAAEF,cAAcf,gBAAgB,CAAC,CAAC,CAAC;QAE/FzB,MAAM,MAAMJ,kBAAkB;IAChC;IAEA,IAAI4C,CAAAA,iCAAAA,cAAevB,MAAM,uBAAiD;QACxEwB,KAAIC,GAAG,CAAC;QACR1C,MAAM,MAAMJ,kBAAkB;IAChC;IAEA,sDAAsD;IACtD,IAAI,CAACI,KAAK;QACRA,MAAM,MAAMK,wBAAwBC,UAAUC,UAAUC;IAC1D;IAEA,IAAI,CAACR,KAAK;QACR,MAAM,IAAI2C,yBAAiB;IAC7B;IAEA,MAAMC,IAAAA,gBAAU,EAAC;QACftC;QACAC;QACAP;IACF;AACF"}
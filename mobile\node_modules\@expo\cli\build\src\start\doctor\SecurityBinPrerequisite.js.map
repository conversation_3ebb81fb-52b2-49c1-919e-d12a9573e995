{"version": 3, "sources": ["../../../../src/start/doctor/SecurityBinPrerequisite.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\n\nimport { Prerequisite, PrerequisiteCommandError } from './Prerequisite';\n\nexport class SecurityBinPrerequisite extends Prerequisite {\n  static instance = new SecurityBinPrerequisite();\n\n  async assertImplementation(): Promise<void> {\n    try {\n      // make sure we can run security\n      await spawnAsync('which', ['security']);\n    } catch {\n      throw new PrerequisiteCommandError(\n        'SECURITY_BIN',\n        \"Cannot code sign project because the CLI `security` is not available on your computer.\\nEnsure it's installed and try again.\"\n      );\n    }\n  }\n}\n"], "names": ["SecurityBinPrerequisite", "Prerequisite", "instance", "assertImplementation", "spawnAsync", "PrerequisiteCommandError"], "mappings": ";;;;+BAIaA;;;eAAAA;;;;gEAJU;;;;;;8BAEgC;;;;;;AAEhD,MAAMA,gCAAgCC,0BAAY;qBAChDC,WAAW,IAAIF;IAEtB,MAAMG,uBAAsC;QAC1C,IAAI;YACF,gCAAgC;YAChC,MAAMC,IAAAA,qBAAU,EAAC,SAAS;gBAAC;aAAW;QACxC,EAAE,OAAM;YACN,MAAM,IAAIC,sCAAwB,CAChC,gBACA;QAEJ;IACF;AACF"}
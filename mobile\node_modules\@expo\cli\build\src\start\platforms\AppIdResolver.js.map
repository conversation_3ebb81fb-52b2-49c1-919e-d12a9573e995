{"version": 3, "sources": ["../../../../src/start/platforms/AppIdResolver.ts"], "sourcesContent": ["import { getConfig, getProjectConfigDescriptionWithPaths } from '@expo/config';\n\nimport { CommandError, UnimplementedError } from '../../utils/errors';\nimport { get } from '../../utils/obj';\n\n/** Resolves a native app identifier (bundle identifier, package name) from the project files. */\nexport class AppIdResolver {\n  constructor(\n    protected projectRoot: string,\n    /** Platform to use. */\n    protected platform: string,\n    /** Nested key in the Expo config like `android.package` or `ios.bundleIdentifier`. */\n    protected configProperty: string\n  ) {}\n\n  /** Resolve the application ID for the project. */\n  async getAppIdAsync(): Promise<string> {\n    if (await this.hasNativeProjectAsync()) {\n      return this.getAppIdFromNativeAsync();\n    }\n    return this.getAppIdFromConfigAsync();\n  }\n\n  /** Returns `true` if the project has native project code. */\n  async hasNativeProjectAsync(): Promise<boolean> {\n    throw new UnimplementedError();\n  }\n\n  /** Return the app ID from the Expo config or assert. */\n  async getAppIdFromConfigAsync(): Promise<string> {\n    const config = getConfig(this.projectRoot);\n\n    const appId = get(config.exp, this.configProperty);\n    if (!appId) {\n      throw new CommandError(\n        'NO_APP_ID',\n        `Required property '${\n          this.configProperty\n        }' is not found in the project ${getProjectConfigDescriptionWithPaths(\n          this.projectRoot,\n          config\n        )}. This is required to open the app.`\n      );\n    }\n    return appId;\n  }\n\n  /** Return the app ID from the native project files or null if the app ID cannot be found. */\n  async resolveAppIdFromNativeAsync(): Promise<string | null> {\n    throw new UnimplementedError();\n  }\n\n  /** Return the app ID from the native project files or assert. */\n  async getAppIdFromNativeAsync(): Promise<string> {\n    const appId = await this.resolveAppIdFromNativeAsync();\n    if (!appId) {\n      throw new CommandError(\n        'NO_APP_ID',\n        `Failed to locate the ${this.platform} application identifier in the \"${this.platform}/\" folder. This is required to open the app.`\n      );\n    }\n    return appId;\n  }\n}\n"], "names": ["AppIdResolver", "constructor", "projectRoot", "platform", "configProperty", "getAppIdAsync", "hasNativeProjectAsync", "getAppIdFromNativeAsync", "getAppIdFromConfigAsync", "UnimplementedError", "config", "getConfig", "appId", "get", "exp", "CommandError", "getProjectConfigDescriptionWithPaths", "resolveAppIdFromNativeAsync"], "mappings": ";;;;+BAMaA;;;eAAAA;;;;yBANmD;;;;;;wBAEf;qBAC7B;AAGb,MAAMA;IACXC,YACE,AAAUC,WAAmB,EAC7B,qBAAqB,GACrB,AAAUC,QAAgB,EAC1B,oFAAoF,GACpF,AAAUC,cAAsB,CAChC;aALUF,cAAAA;aAEAC,WAAAA;aAEAC,iBAAAA;IACT;IAEH,gDAAgD,GAChD,MAAMC,gBAAiC;QACrC,IAAI,MAAM,IAAI,CAACC,qBAAqB,IAAI;YACtC,OAAO,IAAI,CAACC,uBAAuB;QACrC;QACA,OAAO,IAAI,CAACC,uBAAuB;IACrC;IAEA,2DAA2D,GAC3D,MAAMF,wBAA0C;QAC9C,MAAM,IAAIG,0BAAkB;IAC9B;IAEA,sDAAsD,GACtD,MAAMD,0BAA2C;QAC/C,MAAME,SAASC,IAAAA,mBAAS,EAAC,IAAI,CAACT,WAAW;QAEzC,MAAMU,QAAQC,IAAAA,QAAG,EAACH,OAAOI,GAAG,EAAE,IAAI,CAACV,cAAc;QACjD,IAAI,CAACQ,OAAO;YACV,MAAM,IAAIG,oBAAY,CACpB,aACA,CAAC,mBAAmB,EAClB,IAAI,CAACX,cAAc,CACpB,8BAA8B,EAAEY,IAAAA,8CAAoC,EACnE,IAAI,CAACd,WAAW,EAChBQ,QACA,mCAAmC,CAAC;QAE1C;QACA,OAAOE;IACT;IAEA,2FAA2F,GAC3F,MAAMK,8BAAsD;QAC1D,MAAM,IAAIR,0BAAkB;IAC9B;IAEA,+DAA+D,GAC/D,MAAMF,0BAA2C;QAC/C,MAAMK,QAAQ,MAAM,IAAI,CAACK,2BAA2B;QACpD,IAAI,CAACL,OAAO;YACV,MAAM,IAAIG,oBAAY,CACpB,aACA,CAAC,qBAAqB,EAAE,IAAI,CAACZ,QAAQ,CAAC,gCAAgC,EAAE,IAAI,CAACA,QAAQ,CAAC,4CAA4C,CAAC;QAEvI;QACA,OAAOS;IACT;AACF"}
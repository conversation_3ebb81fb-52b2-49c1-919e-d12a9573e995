{"version": 3, "sources": ["../../../src/api/getVersions.ts"], "sourcesContent": ["import { createCachedFetch, getResponseDataOrThrow } from './rest/client';\nimport { CommandError } from '../utils/errors';\n\n/** Represents version info for a particular SDK. */\nexport type SDKVersion = {\n  /** @example \"2.16.1\" */\n  iosVersion?: string;\n  /** @example \"https://dpq5q02fu5f55.cloudfront.net/Exponent-2.17.4.tar.gz\" */\n  iosClientUrl?: string;\n  /** @example \"https://dev.to/expo/expo-sdk-38-is-now-available-5aa0\" */\n  releaseNoteUrl?: string;\n  /** @example \"2.17.4\" */\n  iosClientVersion?: string;\n  /** @example \"https://d1ahtucjixef4r.cloudfront.net/Exponent-2.16.1.apk\" */\n  androidClientUrl?: string;\n  /** @example \"2.16.1\" */\n  androidClientVersion?: string;\n  /** @example { \"typescript\": \"~3.9.5\" } */\n  relatedPackages?: Record<string, string>;\n\n  facebookReactNativeVersion: string;\n\n  facebookReactVersion?: string;\n\n  expoVersion?: string;\n\n  beta?: boolean;\n};\n\nexport type SDKVersions = Record<string, SDKVersion>;\n\nexport type Versions = {\n  androidUrl: string;\n  androidVersion: string;\n  iosUrl: string;\n  iosVersion: string;\n  sdkVersions: SDKVersions;\n};\n\n/** Get versions from remote endpoint. */\nexport async function getVersionsAsync({\n  skipCache,\n}: { skipCache?: boolean } = {}): Promise<Versions> {\n  // Reconstruct the cached fetch since caching could be disabled.\n  const fetchAsync = createCachedFetch({\n    skipCache,\n    cacheDirectory: 'versions-cache',\n    // We'll use a 5 minute cache to ensure we stay relatively up to date.\n    ttl: 1000 * 60 * 5,\n  });\n\n  const results = await fetchAsync('versions/latest');\n  if (!results.ok) {\n    throw new CommandError(\n      'API',\n      `Unexpected response when fetching version info from Expo servers: ${results.statusText}.`\n    );\n  }\n\n  const json = await results.json();\n  return getResponseDataOrThrow<Versions>(json);\n}\n"], "names": ["getVersionsAsync", "<PERSON><PERSON><PERSON>", "fetchAsync", "createCachedFetch", "cacheDirectory", "ttl", "results", "ok", "CommandError", "statusText", "json", "getResponseDataOrThrow"], "mappings": ";;;;+BAwCsBA;;;eAAAA;;;wBAxCoC;wBAC7B;AAuCtB,eAAeA,iBAAiB,EACrCC,SAAS,EACe,GAAG,CAAC,CAAC;IAC7B,gEAAgE;IAChE,MAAMC,aAAaC,IAAAA,yBAAiB,EAAC;QACnCF;QACAG,gBAAgB;QAChB,sEAAsE;QACtEC,KAAK,OAAO,KAAK;IACnB;IAEA,MAAMC,UAAU,MAAMJ,WAAW;IACjC,IAAI,CAACI,QAAQC,EAAE,EAAE;QACf,MAAM,IAAIC,oBAAY,CACpB,OACA,CAAC,kEAAkE,EAAEF,QAAQG,UAAU,CAAC,CAAC,CAAC;IAE9F;IAEA,MAAMC,OAAO,MAAMJ,QAAQI,IAAI;IAC/B,OAAOC,IAAAA,8BAAsB,EAAWD;AAC1C"}
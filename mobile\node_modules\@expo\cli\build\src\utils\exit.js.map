{"version": 3, "sources": ["../../../src/utils/exit.ts"], "sourcesContent": ["import { ChildProcess } from 'node:child_process';\nimport process from 'node:process';\n\nimport { guardAsync } from './fn';\nimport { warn } from '../log';\n\nconst debug = require('debug')('expo:utils:exit') as typeof console.log;\n\ntype AsyncExitHook = (signal: NodeJS.Signals) => void | Promise<void>;\n\nconst PRE_EXIT_SIGNALS: NodeJS.Signals[] = ['SIGHUP', 'SIGINT', 'SIGTERM', 'SIGBREAK'];\n\n// We create a queue since Node.js throws an error if we try to append too many listeners:\n// (node:4405) MaxListenersExceededWarning: Possible EventEmitter memory leak detected. 11 SIGINT listeners added to [process]. Use emitter.setMaxListeners() to increase limit\nconst queue: AsyncExitHook[] = [];\n\nlet unsubscribe: (() => void) | null = null;\n\n/** Add functions that run before the process exits. Returns a function for removing the listeners. */\nexport function installExitHooks(asyncExitHook: AsyncExitHook): () => void {\n  // We need to instantiate the master listener the first time the queue is used.\n  if (!queue.length) {\n    // Track the master listener so we can remove it later.\n    unsubscribe = attachMasterListener();\n  }\n\n  queue.push(asyncExitHook);\n\n  return () => {\n    const index = queue.indexOf(asyncExitHook);\n    if (index >= 0) {\n      queue.splice(index, 1);\n    }\n    // Clean up the master listener if we don't need it anymore.\n    if (!queue.length) {\n      unsubscribe?.();\n    }\n  };\n}\n\n// Create a function that runs before the process exits and guards against running multiple times.\nfunction createExitHook(signal: NodeJS.Signals) {\n  return guardAsync(async () => {\n    debug(`pre-exit (signal: ${signal}, queue length: ${queue.length})`);\n\n    for (const [index, hookAsync] of Object.entries(queue)) {\n      try {\n        await hookAsync(signal);\n      } catch (error: any) {\n        debug(`Error in exit hook: %O (queue: ${index})`, error);\n      }\n    }\n\n    debug(`post-exit (code: ${process.exitCode ?? 0})`);\n\n    process.exit();\n  });\n}\n\nfunction attachMasterListener() {\n  const hooks: [NodeJS.Signals, () => any][] = [];\n  for (const signal of PRE_EXIT_SIGNALS) {\n    const hook = createExitHook(signal);\n    hooks.push([signal, hook]);\n    process.on(signal, hook);\n  }\n  return () => {\n    for (const [signal, hook] of hooks) {\n      process.removeListener(signal, hook);\n    }\n  };\n}\n\n/**\n * Monitor if the current process is exiting before the delay is reached.\n * If there are active resources, the process will be forced to exit after the delay is reached.\n *\n * @see https://nodejs.org/docs/latest-v18.x/api/process.html#processgetactiveresourcesinfo\n */\nexport function ensureProcessExitsAfterDelay(waitUntilExitMs = 10000, startedAtMs = Date.now()) {\n  // Create a list of the expected active resources before exiting.\n  // Note, the order is undeterministic\n  const expectedResources = [\n    process.stdout.isTTY ? 'TTYWrap' : 'PipeWrap',\n    process.stderr.isTTY ? 'TTYWrap' : 'PipeWrap',\n    process.stdin.isTTY ? 'TTYWrap' : 'PipeWrap',\n  ];\n  // Check active resources, besides the TTYWrap/PipeWrap (process.stdin, process.stdout, process.stderr)\n  const activeResources = process.getActiveResourcesInfo() as string[];\n  // Filter the active resource list by subtracting the expected resources, in undeterministic order\n  const unexpectedActiveResources = activeResources.filter((activeResource) => {\n    const index = expectedResources.indexOf(activeResource);\n    if (index >= 0) {\n      expectedResources.splice(index, 1);\n      return false;\n    }\n\n    return true;\n  });\n\n  const canExitProcess = !unexpectedActiveResources.length;\n  if (canExitProcess) {\n    return debug('no active resources detected, process can safely exit');\n  } else {\n    debug(\n      `process is trying to exit, but is stuck on unexpected active resources:`,\n      unexpectedActiveResources\n    );\n  }\n\n  // Check if the process needs to be force-closed\n  const elapsedTime = Date.now() - startedAtMs;\n  if (elapsedTime > waitUntilExitMs) {\n    debug('active handles detected past the exit delay, forcefully exiting:', activeResources);\n    tryWarnActiveProcesses();\n    return process.exit(0);\n  }\n\n  const timeoutId = setTimeout(() => {\n    // Ensure the timeout is cleared before checking the active resources\n    clearTimeout(timeoutId);\n    // Check if the process can exit\n    ensureProcessExitsAfterDelay(waitUntilExitMs, startedAtMs);\n  }, 100);\n}\n\n/**\n * Try to warn the user about unexpected active processes running in the background.\n * This uses the internal `process._getActiveHandles` method, within a try-catch block.\n * If active child processes are detected, the commands of these processes are logged.\n *\n * @example ```bash\n * Done writing bundle output\n * Detected 2 processes preventing Expo from exiting, forcefully exiting now.\n *   - node /Users/<USER>/../node_modules/nativewind/dist/metro/tailwind/v3/child.js\n *   - node /Users/<USER>/../node_modules/nativewind/dist/metro/tailwind/v3/child.js\n * ```\n */\nfunction tryWarnActiveProcesses() {\n  let activeProcesses: string[] = [];\n\n  try {\n    const children: ChildProcess[] = process\n      // @ts-expect-error - This is an internal method, not designed to be exposed. It's also our only way to get this info\n      ._getActiveHandles()\n      .filter((handle: any) => handle instanceof ChildProcess);\n\n    if (children.length) {\n      activeProcesses = children.map((child) => child.spawnargs.join(' '));\n    }\n  } catch (error) {\n    debug('failed to get active process information:', error);\n  }\n\n  if (!activeProcesses.length) {\n    warn('Something prevented Expo from exiting, forcefully exiting now.');\n  } else {\n    const singularOrPlural =\n      activeProcesses.length === 1 ? '1 process' : `${activeProcesses.length} processes`;\n\n    warn(`Detected ${singularOrPlural} preventing Expo from exiting, forcefully exiting now.`);\n    warn('  - ' + activeProcesses.join('\\n  - '));\n  }\n}\n"], "names": ["ensureProcessExitsAfterDelay", "installExitHooks", "debug", "require", "PRE_EXIT_SIGNALS", "queue", "unsubscribe", "asyncExitHook", "length", "attachMasterListener", "push", "index", "indexOf", "splice", "createExitHook", "signal", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Object", "entries", "error", "process", "exitCode", "exit", "hooks", "hook", "on", "removeListener", "waitUntilExitMs", "startedAtMs", "Date", "now", "expectedResources", "stdout", "isTTY", "stderr", "stdin", "activeResources", "getActiveResourcesInfo", "unexpectedActiveResources", "filter", "activeResource", "canExitProcess", "elapsedTime", "tryWarnActiveProcesses", "timeoutId", "setTimeout", "clearTimeout", "activeProcesses", "children", "_getActiveHandles", "handle", "ChildProcess", "map", "child", "spawnargs", "join", "warn", "singularOrPlural"], "mappings": ";;;;;;;;;;;IA+EgBA,4BAA4B;eAA5BA;;IA5DAC,gBAAgB;eAAhBA;;;;yBAnBa;;;;;;;gEACT;;;;;;oBAEO;qBACN;;;;;;AAErB,MAAMC,QAAQC,QAAQ,SAAS;AAI/B,MAAMC,mBAAqC;IAAC;IAAU;IAAU;IAAW;CAAW;AAEtF,0FAA0F;AAC1F,+KAA+K;AAC/K,MAAMC,QAAyB,EAAE;AAEjC,IAAIC,cAAmC;AAGhC,SAASL,iBAAiBM,aAA4B;IAC3D,+EAA+E;IAC/E,IAAI,CAACF,MAAMG,MAAM,EAAE;QACjB,uDAAuD;QACvDF,cAAcG;IAChB;IAEAJ,MAAMK,IAAI,CAACH;IAEX,OAAO;QACL,MAAMI,QAAQN,MAAMO,OAAO,CAACL;QAC5B,IAAII,SAAS,GAAG;YACdN,MAAMQ,MAAM,CAACF,OAAO;QACtB;QACA,4DAA4D;QAC5D,IAAI,CAACN,MAAMG,MAAM,EAAE;YACjBF,+BAAAA;QACF;IACF;AACF;AAEA,kGAAkG;AAClG,SAASQ,eAAeC,MAAsB;IAC5C,OAAOC,IAAAA,cAAU,EAAC;QAChBd,MAAM,CAAC,kBAAkB,EAAEa,OAAO,gBAAgB,EAAEV,MAAMG,MAAM,CAAC,CAAC,CAAC;QAEnE,KAAK,MAAM,CAACG,OAAOM,UAAU,IAAIC,OAAOC,OAAO,CAACd,OAAQ;YACtD,IAAI;gBACF,MAAMY,UAAUF;YAClB,EAAE,OAAOK,OAAY;gBACnBlB,MAAM,CAAC,+BAA+B,EAAES,MAAM,CAAC,CAAC,EAAES;YACpD;QACF;QAEAlB,MAAM,CAAC,iBAAiB,EAAEmB,sBAAO,CAACC,QAAQ,IAAI,EAAE,CAAC,CAAC;QAElDD,sBAAO,CAACE,IAAI;IACd;AACF;AAEA,SAASd;IACP,MAAMe,QAAuC,EAAE;IAC/C,KAAK,MAAMT,UAAUX,iBAAkB;QACrC,MAAMqB,OAAOX,eAAeC;QAC5BS,MAAMd,IAAI,CAAC;YAACK;YAAQU;SAAK;QACzBJ,sBAAO,CAACK,EAAE,CAACX,QAAQU;IACrB;IACA,OAAO;QACL,KAAK,MAAM,CAACV,QAAQU,KAAK,IAAID,MAAO;YAClCH,sBAAO,CAACM,cAAc,CAACZ,QAAQU;QACjC;IACF;AACF;AAQO,SAASzB,6BAA6B4B,kBAAkB,KAAK,EAAEC,cAAcC,KAAKC,GAAG,EAAE;IAC5F,iEAAiE;IACjE,qCAAqC;IACrC,MAAMC,oBAAoB;QACxBX,sBAAO,CAACY,MAAM,CAACC,KAAK,GAAG,YAAY;QACnCb,sBAAO,CAACc,MAAM,CAACD,KAAK,GAAG,YAAY;QACnCb,sBAAO,CAACe,KAAK,CAACF,KAAK,GAAG,YAAY;KACnC;IACD,uGAAuG;IACvG,MAAMG,kBAAkBhB,sBAAO,CAACiB,sBAAsB;IACtD,kGAAkG;IAClG,MAAMC,4BAA4BF,gBAAgBG,MAAM,CAAC,CAACC;QACxD,MAAM9B,QAAQqB,kBAAkBpB,OAAO,CAAC6B;QACxC,IAAI9B,SAAS,GAAG;YACdqB,kBAAkBnB,MAAM,CAACF,OAAO;YAChC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM+B,iBAAiB,CAACH,0BAA0B/B,MAAM;IACxD,IAAIkC,gBAAgB;QAClB,OAAOxC,MAAM;IACf,OAAO;QACLA,MACE,CAAC,uEAAuE,CAAC,EACzEqC;IAEJ;IAEA,gDAAgD;IAChD,MAAMI,cAAcb,KAAKC,GAAG,KAAKF;IACjC,IAAIc,cAAcf,iBAAiB;QACjC1B,MAAM,oEAAoEmC;QAC1EO;QACA,OAAOvB,sBAAO,CAACE,IAAI,CAAC;IACtB;IAEA,MAAMsB,YAAYC,WAAW;QAC3B,qEAAqE;QACrEC,aAAaF;QACb,gCAAgC;QAChC7C,6BAA6B4B,iBAAiBC;IAChD,GAAG;AACL;AAEA;;;;;;;;;;;CAWC,GACD,SAASe;IACP,IAAII,kBAA4B,EAAE;IAElC,IAAI;QACF,MAAMC,WAA2B5B,sBAAO,AACtC,qHAAqH;SACpH6B,iBAAiB,GACjBV,MAAM,CAAC,CAACW,SAAgBA,kBAAkBC,iCAAY;QAEzD,IAAIH,SAASzC,MAAM,EAAE;YACnBwC,kBAAkBC,SAASI,GAAG,CAAC,CAACC,QAAUA,MAAMC,SAAS,CAACC,IAAI,CAAC;QACjE;IACF,EAAE,OAAOpC,OAAO;QACdlB,MAAM,6CAA6CkB;IACrD;IAEA,IAAI,CAAC4B,gBAAgBxC,MAAM,EAAE;QAC3BiD,IAAAA,SAAI,EAAC;IACP,OAAO;QACL,MAAMC,mBACJV,gBAAgBxC,MAAM,KAAK,IAAI,cAAc,GAAGwC,gBAAgBxC,MAAM,CAAC,UAAU,CAAC;QAEpFiD,IAAAA,SAAI,EAAC,CAAC,SAAS,EAAEC,iBAAiB,sDAAsD,CAAC;QACzFD,IAAAA,SAAI,EAAC,SAAST,gBAAgBQ,IAAI,CAAC;IACrC;AACF"}
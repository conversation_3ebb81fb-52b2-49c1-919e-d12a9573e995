{"version": 3, "sources": ["../../../../../../src/start/server/metro/debugging/attachAtlas.ts"], "sourcesContent": ["import type { Server as ConnectServer } from 'connect';\nimport type { ConfigT as MetroConfig } from 'metro-config';\n\nimport { AtlasPrerequisite } from './AtlasPrerequisite';\nimport { env } from '../../../../utils/env';\nimport { type EnsureDependenciesOptions } from '../../../doctor/dependencies/ensureDependenciesAsync';\n\nconst debug = require('debug')('expo:metro:debugging:attachAtlas') as typeof console.log;\n\ntype AttachAtlasOptions = Pick<EnsureDependenciesOptions, 'exp'> & {\n  isExporting: boolean;\n  projectRoot: string;\n  middleware?: ConnectServer;\n  metroConfig: MetroConfig;\n  resetAtlasFile?: boolean;\n};\n\nexport async function attachAtlasAsync(\n  options: AttachAtlasOptions\n): Promise<void | ReturnType<typeof import('expo-atlas/cli').createExpoAtlasMiddleware>> {\n  if (!env.EXPO_ATLAS) {\n    return;\n  }\n\n  debug('Atlas is enabled, initializing for this project...');\n  await new AtlasPrerequisite(options.projectRoot).bootstrapAsync({ exp: options.exp });\n\n  return !options.isExporting\n    ? attachAtlasToDevServer(options)\n    : await attachAtlasToExport(options);\n}\n\n/**\n * Attach Atlas to the Metro bundler for development mode.\n * This includes attaching to Metro's middleware stack to host the Atlas UI.\n */\nfunction attachAtlasToDevServer(\n  options: Pick<AttachAtlasOptions, 'projectRoot' | 'middleware' | 'metroConfig'>\n): void | ReturnType<typeof import('expo-atlas/cli').createExpoAtlasMiddleware> {\n  if (!options.middleware) {\n    throw new Error(\n      'Expected middleware to be provided for Atlas when running in development mode'\n    );\n  }\n\n  const atlas = importAtlasForDev(options.projectRoot);\n  if (!atlas) {\n    return debug('Atlas is not installed in the project, skipping initialization');\n  }\n\n  const instance = atlas.createExpoAtlasMiddleware(options.metroConfig);\n  options.middleware.use('/_expo/atlas', instance.middleware);\n  debug('Attached Atlas middleware for development on: /_expo/atlas');\n  return instance;\n}\n\nfunction importAtlasForDev(projectRoot: string): null | typeof import('expo-atlas/cli') {\n  try {\n    return require(require.resolve('expo-atlas/cli', { paths: [projectRoot] }));\n  } catch (error: any) {\n    debug('Failed to load Atlas from project:', error);\n    return null;\n  }\n}\n\n/**\n * Attach Atlas to the Metro bundler for exporting mode.\n * This only includes attaching the custom serializer to the Metro config.\n */\nasync function attachAtlasToExport(\n  options: Pick<AttachAtlasOptions, 'projectRoot' | 'metroConfig' | 'resetAtlasFile'>\n): Promise<void> {\n  const atlas = importAtlasForExport(options.projectRoot);\n  if (!atlas) {\n    return debug('Atlas is not installed in the project, skipping initialization');\n  }\n\n  if (options.resetAtlasFile) {\n    const filePath = await atlas.resetExpoAtlasFile(options.projectRoot);\n    debug('(Re)created Atlas file at:', filePath);\n  }\n\n  atlas.withExpoAtlas(options.metroConfig);\n  debug('Attached Atlas to Metro config for exporting');\n}\n\nfunction importAtlasForExport(projectRoot: string): null | typeof import('expo-atlas/metro') {\n  try {\n    return require(require.resolve('expo-atlas/metro', { paths: [projectRoot] }));\n  } catch (error: any) {\n    debug('Failed to load Atlas from project:', error);\n    return null;\n  }\n}\n\n/**\n * Wait until the Atlas file has all data written.\n * Note, this is a workaround whenever `process.exit` is required, avoid if possible.\n * @internal\n */\nexport async function waitUntilAtlasExportIsReadyAsync(projectRoot: string) {\n  if (!env.EXPO_ATLAS) return;\n\n  const atlas = importAtlasForExport(projectRoot);\n\n  if (!atlas) {\n    return debug('Atlas is not loaded, cannot wait for export to finish');\n  }\n  if (typeof atlas.waitUntilAtlasFileReady !== 'function') {\n    return debug('Atlas is outdated, cannot wait for export to finish');\n  }\n\n  debug('Waiting for Atlas to finish exporting...');\n  await atlas.waitUntilAtlasFileReady();\n  debug('Atlas export is ready');\n}\n"], "names": ["attachAtlasAsync", "waitUntilAtlasExportIsReadyAsync", "debug", "require", "options", "env", "EXPO_ATLAS", "AtlasPrerequisite", "projectRoot", "bootstrapAsync", "exp", "isExporting", "attachAtlasToDevServer", "attachAtlasToExport", "middleware", "Error", "atlas", "importAtlasForDev", "instance", "createExpoAtlasMiddleware", "metroConfig", "use", "resolve", "paths", "error", "importAtlasForExport", "resetAtlasFile", "filePath", "resetExpoAtlasFile", "withExpoAtlas", "waitUntilAtlasFileReady"], "mappings": ";;;;;;;;;;;IAiBsBA,gBAAgB;eAAhBA;;IAmFAC,gCAAgC;eAAhCA;;;mCAjGY;qBACd;AAGpB,MAAMC,QAAQC,QAAQ,SAAS;AAUxB,eAAeH,iBACpBI,OAA2B;IAE3B,IAAI,CAACC,QAAG,CAACC,UAAU,EAAE;QACnB;IACF;IAEAJ,MAAM;IACN,MAAM,IAAIK,oCAAiB,CAACH,QAAQI,WAAW,EAAEC,cAAc,CAAC;QAAEC,KAAKN,QAAQM,GAAG;IAAC;IAEnF,OAAO,CAACN,QAAQO,WAAW,GACvBC,uBAAuBR,WACvB,MAAMS,oBAAoBT;AAChC;AAEA;;;CAGC,GACD,SAASQ,uBACPR,OAA+E;IAE/E,IAAI,CAACA,QAAQU,UAAU,EAAE;QACvB,MAAM,IAAIC,MACR;IAEJ;IAEA,MAAMC,QAAQC,kBAAkBb,QAAQI,WAAW;IACnD,IAAI,CAACQ,OAAO;QACV,OAAOd,MAAM;IACf;IAEA,MAAMgB,WAAWF,MAAMG,yBAAyB,CAACf,QAAQgB,WAAW;IACpEhB,QAAQU,UAAU,CAACO,GAAG,CAAC,gBAAgBH,SAASJ,UAAU;IAC1DZ,MAAM;IACN,OAAOgB;AACT;AAEA,SAASD,kBAAkBT,WAAmB;IAC5C,IAAI;QACF,OAAOL,QAAQA,QAAQmB,OAAO,CAAC,kBAAkB;YAAEC,OAAO;gBAACf;aAAY;QAAC;IAC1E,EAAE,OAAOgB,OAAY;QACnBtB,MAAM,sCAAsCsB;QAC5C,OAAO;IACT;AACF;AAEA;;;CAGC,GACD,eAAeX,oBACbT,OAAmF;IAEnF,MAAMY,QAAQS,qBAAqBrB,QAAQI,WAAW;IACtD,IAAI,CAACQ,OAAO;QACV,OAAOd,MAAM;IACf;IAEA,IAAIE,QAAQsB,cAAc,EAAE;QAC1B,MAAMC,WAAW,MAAMX,MAAMY,kBAAkB,CAACxB,QAAQI,WAAW;QACnEN,MAAM,8BAA8ByB;IACtC;IAEAX,MAAMa,aAAa,CAACzB,QAAQgB,WAAW;IACvClB,MAAM;AACR;AAEA,SAASuB,qBAAqBjB,WAAmB;IAC/C,IAAI;QACF,OAAOL,QAAQA,QAAQmB,OAAO,CAAC,oBAAoB;YAAEC,OAAO;gBAACf;aAAY;QAAC;IAC5E,EAAE,OAAOgB,OAAY;QACnBtB,MAAM,sCAAsCsB;QAC5C,OAAO;IACT;AACF;AAOO,eAAevB,iCAAiCO,WAAmB;IACxE,IAAI,CAACH,QAAG,CAACC,UAAU,EAAE;IAErB,MAAMU,QAAQS,qBAAqBjB;IAEnC,IAAI,CAACQ,OAAO;QACV,OAAOd,MAAM;IACf;IACA,IAAI,OAAOc,MAAMc,uBAAuB,KAAK,YAAY;QACvD,OAAO5B,MAAM;IACf;IAEAA,MAAM;IACN,MAAMc,MAAMc,uBAAuB;IACnC5B,MAAM;AACR"}
import { useSS<PERSON> } from "@clerk/clerk-expo";
import { useState } from "react"
import { Alert } from "react-native";

export const useSocialAuth = () => {
    const [Loading, setLoading] = useState(false);
    const { startSSOFlow } = useSSO()
    const handleAuth = async (strategy: "oauth_google" | "oauth_apple") => {
        setLoading(true)
        try {
            const { createdSessionId, setActive } = await startSSOFlow({ strategy })
            if (createdSessionId && setActive) {
                await setActive({ session: createdSessionId })
            }
        }
        catch (err) {
            console.log("Error in Auth", err);
            const provider = strategy === "oauth_google" ? "Google" : "Apple";
            Alert.alert("Error ", `Failed to sign in with ${provider}`)


        }
        finally {
            setLoading(false)
        }

    }
    return { Loading, handleAuth }
}
import { useOAuth } from "@clerk/clerk-expo";
import { useRouter } from "expo-router";
import { useState } from "react";
import { Alert } from "react-native";

export const useGoogleAuth = () => {
    const [isLoading, setIsLoading] = useState(false);
    const { startOAuthFlow } = useOAuth({
        strategy: "oauth_google",
        redirectUrl: "mobile://oauth-native-callback"
    });
    const router = useRouter();

    const signInWithGoogle = async () => {
        setIsLoading(true);
        try {
            const { createdSessionId, setActive } = await startOAuthFlow();

            if (createdSessionId) {
                await setActive!({ session: createdSessionId });
                console.log("✅ Google authentication successful");
                // The OAuth callback will handle the redirect
            }
        } catch (err: any) {
            console.error("❌ Google authentication error:", err);

            // Handle specific error cases
            if (err.code === 'session_exists') {
                console.log("Session already exists, redirecting...");
                router.replace("/(tabs)");
                return;
            }

            Alert.alert(
                "Authentication Error",
                err.message || "Failed to sign in with Google. Please try again."
            );
        } finally {
            setIsLoading(false);
        }
    };

    return {
        isLoading,
        signInWithGoogle
    };
};

// Alternative SSO-based approach
export const useGoogleSSO = () => {
    const [isLoading, setIsLoading] = useState(false);
    const { startSSOFlow } = require("@clerk/clerk-expo").useSSO();
    const router = useRouter();

    const signInWithGoogleSSO = async () => {
        setIsLoading(true);
        try {
            const { createdSessionId, setActive } = await startSSOFlow({
                strategy: "oauth_google",
                redirectUrl: "mobile://oauth-native-callback"
            });

            if (createdSessionId && setActive) {
                await setActive({ session: createdSessionId });
                console.log("✅ Google SSO authentication successful");
                router.replace("/(tabs)");
            }
        } catch (err: any) {
            console.error("❌ Google SSO authentication error:", err);

            Alert.alert(
                "Authentication Error",
                err.message || "Failed to sign in with Google. Please try again."
            );
        } finally {
            setIsLoading(false);
        }
    };

    return {
        isLoading,
        signInWithGoogleSSO
    };
};
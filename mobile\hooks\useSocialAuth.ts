import { use<PERSON><PERSON> } from "@clerk/clerk-expo";
import * as AuthSession from 'expo-auth-session';
import { useRouter } from "expo-router";
import * as WebBrowser from 'expo-web-browser';
import { useCallback, useEffect, useState } from "react";
import { Alert } from "react-native";

// Warm up browser for better performance on Android
export const useWarmUpBrowser = () => {
    useEffect(() => {
        // Preloads the browser for Android devices to reduce authentication load time
        void WebBrowser.warmUpAsync()
        return () => {
            // Cleanup: closes browser when component unmounts
            void WebBrowser.coolDownAsync()
        }
    }, [])
}

// Handle any pending authentication sessions
WebBrowser.maybeCompleteAuthSession()

export const useGoogleAuth = () => {
    const [isLoading, setIsLoading] = useState(false);
    const { startSSOFlow } = useSSO();
    const router = useRouter();

    const signInWithGoogle = useCallback(async () => {
        setIsLoading(true);
        try {
            console.log("🚀 Starting Google OAuth flow...");

            // Start the authentication process using Clerk's recommended SSO flow
            const { createdSessionId, setActive, signIn, signUp } = await startSSOFlow({
                strategy: 'oauth_google',
                // For native apps, use AuthSession.makeRedirectUri()
                redirectUrl: AuthSession.makeRedirectUri(),
            });

            console.log("📋 OAuth flow result:", {
                createdSessionId: !!createdSessionId,
                hasSetActive: !!setActive,
                hasSignIn: !!signIn,
                hasSignUp: !!signUp
            });

            // If sign in was successful, set the active session
            if (createdSessionId && setActive) {
                await setActive({ session: createdSessionId });
                console.log("✅ Google authentication successful - session created");
                router.replace("/(tabs)");
            } else {
                // If there is no createdSessionId, there might be missing requirements
                console.log("⚠️ Authentication incomplete - checking requirements...");

                if (signIn) {
                    console.log("📝 SignIn status:", signIn.status);
                    // Handle sign-in specific requirements
                } else if (signUp) {
                    console.log("📝 SignUp status:", signUp.status);
                    // Handle sign-up specific requirements
                }

                Alert.alert(
                    "Authentication Incomplete",
                    "Please complete the authentication process."
                );
            }
        } catch (err: any) {
            console.error("❌ Google authentication error:", err);
            console.error("Error details:", JSON.stringify(err, null, 2));

            Alert.alert(
                "Authentication Error",
                err.message || "Failed to sign in with Google. Please try again."
            );
        } finally {
            setIsLoading(false);
        }
    }, [startSSOFlow, router]);

    return {
        isLoading,
        signInWithGoogle
    };
};
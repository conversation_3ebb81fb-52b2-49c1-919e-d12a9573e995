import { useOAuth } from "@clerk/clerk-expo";
import { useRouter } from "expo-router";
import { useState } from "react";
import { Alert } from "react-native";

export const useGoogleAuth = () => {
    const [isLoading, setIsLoading] = useState(false);
    const { startOAuthFlow } = useOAuth({ strategy: "oauth_google" });
    const router = useRouter();

    const signInWithGoogle = async () => {
        setIsLoading(true);
        try {
            const { createdSessionId, setActive } = await startOAuthFlow();

            if (createdSessionId) {
                await setActive!({ session: createdSessionId });
                console.log("✅ Google authentication successful");
                router.replace("/(tabs)");
            }
        } catch (err: any) {
            console.error("❌ Google authentication error:", err);

            // Handle specific error cases
            if (err.code === 'session_exists') {
                console.log("Session already exists, redirecting...");
                router.replace("/(tabs)");
                return;
            }

            Alert.alert(
                "Authentication Error",
                err.message || "Failed to sign in with Google. Please try again."
            );
        } finally {
            setIsLoading(false);
        }
    };

    return {
        isLoading,
        signInWithGoogle
    };
};
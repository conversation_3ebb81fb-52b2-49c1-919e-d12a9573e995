{"version": 3, "sources": ["../../../../../src/start/server/middleware/CreateFileMiddleware.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport fs from 'fs';\nimport path from 'path';\n\nimport { ExpoMiddleware } from './ExpoMiddleware';\nimport { ServerRequest, ServerResponse } from './server.types';\n\nconst debug = require('debug')('expo:start:server:middleware:createFile') as typeof console.log;\n\nexport type TouchFileBody = {\n  /** @deprecated */\n  path: string;\n  absolutePath?: string;\n  contents: string;\n};\n\n/**\n * Middleware for creating a file given a `POST` request with\n * `{ contents: string, path: string }` in the body.\n */\nexport class CreateFileMiddleware extends ExpoMiddleware {\n  constructor(protected projectRoot: string) {\n    super(projectRoot, ['/_expo/touch']);\n  }\n\n  protected resolvePath(inputPath: string): string {\n    return this.resolveExtension(path.join(this.projectRoot, inputPath));\n  }\n\n  protected resolveExtension(inputPath: string): string {\n    let resolvedPath = inputPath;\n    const extension = path.extname(inputPath);\n    if (extension === '.js') {\n      // Automatically convert JS files to TS files when added to a project\n      // with TypeScript.\n      const tsconfigPath = path.join(this.projectRoot, 'tsconfig.json');\n      if (fs.existsSync(tsconfigPath)) {\n        resolvedPath = resolvedPath.replace(/\\.js$/, '.tsx');\n      }\n    }\n\n    return resolvedPath;\n  }\n\n  protected async parseRawBody(req: ServerRequest): Promise<TouchFileBody> {\n    const rawBody = await new Promise<string>((resolve, reject) => {\n      let body = '';\n      req.on('data', (chunk) => {\n        body += chunk.toString();\n      });\n      req.on('end', () => {\n        resolve(body);\n      });\n      req.on('error', (err) => {\n        reject(err);\n      });\n    });\n\n    const properties = JSON.parse(rawBody);\n    this.assertTouchFileBody(properties);\n\n    return properties;\n  }\n\n  private assertTouchFileBody(body: any): asserts body is TouchFileBody {\n    if (typeof body !== 'object' || body == null) {\n      throw new Error('Expected object');\n    }\n    if (typeof body.path !== 'string') {\n      throw new Error('Expected \"path\" in body to be string');\n    }\n    if (typeof body.contents !== 'string') {\n      throw new Error('Expected \"contents\" in body to be string');\n    }\n  }\n\n  async handleRequestAsync(req: ServerRequest, res: ServerResponse): Promise<void> {\n    if (req.method !== 'POST') {\n      res.statusCode = 405;\n      res.end('Method Not Allowed');\n      return;\n    }\n\n    let properties: TouchFileBody;\n\n    try {\n      properties = await this.parseRawBody(req);\n    } catch (e) {\n      debug('Error parsing request body', e);\n      res.statusCode = 400;\n      res.end('Bad Request');\n      return;\n    }\n\n    debug(`Requested: %O`, properties);\n\n    const resolvedPath = properties.absolutePath\n      ? this.resolveExtension(path.resolve(properties.absolutePath))\n      : this.resolvePath(properties.path);\n\n    if (fs.existsSync(resolvedPath)) {\n      res.statusCode = 409;\n      res.end('File already exists.');\n      return;\n    }\n\n    debug(`Resolved path:`, resolvedPath);\n\n    try {\n      await fs.promises.mkdir(path.dirname(resolvedPath), { recursive: true });\n      await fs.promises.writeFile(resolvedPath, properties.contents, 'utf8');\n    } catch (e) {\n      debug('Error writing file', e);\n      res.statusCode = 500;\n      res.end('Error writing file.');\n      return;\n    }\n\n    debug(`File created`);\n    res.statusCode = 200;\n    res.end('OK');\n  }\n}\n"], "names": ["CreateFileMiddleware", "debug", "require", "ExpoMiddleware", "constructor", "projectRoot", "<PERSON><PERSON><PERSON>", "inputPath", "resolveExtension", "path", "join", "<PERSON><PERSON><PERSON>", "extension", "extname", "tsconfigPath", "fs", "existsSync", "replace", "parseRawBody", "req", "rawBody", "Promise", "resolve", "reject", "body", "on", "chunk", "toString", "err", "properties", "JSON", "parse", "assertTouchFileBody", "Error", "contents", "handleRequestAsync", "res", "method", "statusCode", "end", "e", "absolutePath", "promises", "mkdir", "dirname", "recursive", "writeFile"], "mappings": "AAAA;;;;;CAKC;;;;+BAoBYA;;;eAAAA;;;;gEAnBE;;;;;;;gEACE;;;;;;gCAEc;;;;;;AAG/B,MAAMC,QAAQC,QAAQ,SAAS;AAaxB,MAAMF,6BAA6BG,8BAAc;IACtDC,YAAY,AAAUC,WAAmB,CAAE;QACzC,KAAK,CAACA,aAAa;YAAC;SAAe,QADfA,cAAAA;IAEtB;IAEUC,YAAYC,SAAiB,EAAU;QAC/C,OAAO,IAAI,CAACC,gBAAgB,CAACC,eAAI,CAACC,IAAI,CAAC,IAAI,CAACL,WAAW,EAAEE;IAC3D;IAEUC,iBAAiBD,SAAiB,EAAU;QACpD,IAAII,eAAeJ;QACnB,MAAMK,YAAYH,eAAI,CAACI,OAAO,CAACN;QAC/B,IAAIK,cAAc,OAAO;YACvB,qEAAqE;YACrE,mBAAmB;YACnB,MAAME,eAAeL,eAAI,CAACC,IAAI,CAAC,IAAI,CAACL,WAAW,EAAE;YACjD,IAAIU,aAAE,CAACC,UAAU,CAACF,eAAe;gBAC/BH,eAAeA,aAAaM,OAAO,CAAC,SAAS;YAC/C;QACF;QAEA,OAAON;IACT;IAEA,MAAgBO,aAAaC,GAAkB,EAA0B;QACvE,MAAMC,UAAU,MAAM,IAAIC,QAAgB,CAACC,SAASC;YAClD,IAAIC,OAAO;YACXL,IAAIM,EAAE,CAAC,QAAQ,CAACC;gBACdF,QAAQE,MAAMC,QAAQ;YACxB;YACAR,IAAIM,EAAE,CAAC,OAAO;gBACZH,QAAQE;YACV;YACAL,IAAIM,EAAE,CAAC,SAAS,CAACG;gBACfL,OAAOK;YACT;QACF;QAEA,MAAMC,aAAaC,KAAKC,KAAK,CAACX;QAC9B,IAAI,CAACY,mBAAmB,CAACH;QAEzB,OAAOA;IACT;IAEQG,oBAAoBR,IAAS,EAAiC;QACpE,IAAI,OAAOA,SAAS,YAAYA,QAAQ,MAAM;YAC5C,MAAM,IAAIS,MAAM;QAClB;QACA,IAAI,OAAOT,KAAKf,IAAI,KAAK,UAAU;YACjC,MAAM,IAAIwB,MAAM;QAClB;QACA,IAAI,OAAOT,KAAKU,QAAQ,KAAK,UAAU;YACrC,MAAM,IAAID,MAAM;QAClB;IACF;IAEA,MAAME,mBAAmBhB,GAAkB,EAAEiB,GAAmB,EAAiB;QAC/E,IAAIjB,IAAIkB,MAAM,KAAK,QAAQ;YACzBD,IAAIE,UAAU,GAAG;YACjBF,IAAIG,GAAG,CAAC;YACR;QACF;QAEA,IAAIV;QAEJ,IAAI;YACFA,aAAa,MAAM,IAAI,CAACX,YAAY,CAACC;QACvC,EAAE,OAAOqB,GAAG;YACVvC,MAAM,8BAA8BuC;YACpCJ,IAAIE,UAAU,GAAG;YACjBF,IAAIG,GAAG,CAAC;YACR;QACF;QAEAtC,MAAM,CAAC,aAAa,CAAC,EAAE4B;QAEvB,MAAMlB,eAAekB,WAAWY,YAAY,GACxC,IAAI,CAACjC,gBAAgB,CAACC,eAAI,CAACa,OAAO,CAACO,WAAWY,YAAY,KAC1D,IAAI,CAACnC,WAAW,CAACuB,WAAWpB,IAAI;QAEpC,IAAIM,aAAE,CAACC,UAAU,CAACL,eAAe;YAC/ByB,IAAIE,UAAU,GAAG;YACjBF,IAAIG,GAAG,CAAC;YACR;QACF;QAEAtC,MAAM,CAAC,cAAc,CAAC,EAAEU;QAExB,IAAI;YACF,MAAMI,aAAE,CAAC2B,QAAQ,CAACC,KAAK,CAAClC,eAAI,CAACmC,OAAO,CAACjC,eAAe;gBAAEkC,WAAW;YAAK;YACtE,MAAM9B,aAAE,CAAC2B,QAAQ,CAACI,SAAS,CAACnC,cAAckB,WAAWK,QAAQ,EAAE;QACjE,EAAE,OAAOM,GAAG;YACVvC,MAAM,sBAAsBuC;YAC5BJ,IAAIE,UAAU,GAAG;YACjBF,IAAIG,GAAG,CAAC;YACR;QACF;QAEAtC,MAAM,CAAC,YAAY,CAAC;QACpBmC,IAAIE,UAAU,GAAG;QACjBF,IAAIG,GAAG,CAAC;IACV;AACF"}
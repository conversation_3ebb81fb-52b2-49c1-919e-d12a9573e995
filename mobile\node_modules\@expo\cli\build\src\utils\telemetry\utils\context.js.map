{"version": 3, "sources": ["../../../../../src/utils/telemetry/utils/context.ts"], "sourcesContent": ["import * as ciInfo from 'ci-info';\nimport os from 'os';\n\nimport { groupBy } from '../../array';\n\nexport function createContext() {\n  return {\n    os: { name: os.platform(), version: os.release(), node: process.versions.node },\n    device: { arch: os.arch(), memory: summarizeMemory() },\n    cpu: summarizeCpuInfo(),\n    app: { name: 'expo/cli', version: process.env.__EXPO_VERSION },\n    ci: ciInfo.isCI ? { name: ciInfo.name, isPr: ciInfo.isPR } : undefined,\n  };\n}\n\nfunction summarizeMemory() {\n  const gb = os.totalmem() / 1024 / 1024 / 1024;\n  return Math.round(gb * 100) / 100;\n}\n\nfunction summarizeCpuInfo() {\n  const cpus = groupBy(os.cpus() ?? [], (item) => item.model);\n  const summary = { model: '', speed: 0, count: 0 };\n\n  for (const key in cpus) {\n    if (cpus[key].length > summary.count) {\n      summary.model = key;\n      summary.speed = cpus[key][0].speed;\n      summary.count = cpus[key].length;\n    }\n  }\n\n  return !summary.model || !summary.count ? undefined : summary;\n}\n"], "names": ["createContext", "os", "name", "platform", "version", "release", "node", "process", "versions", "device", "arch", "memory", "summarize<PERSON><PERSON><PERSON>", "cpu", "summarizeCpuInfo", "app", "env", "__EXPO_VERSION", "ci", "ciInfo", "isCI", "isPr", "isPR", "undefined", "gb", "totalmem", "Math", "round", "cpus", "groupBy", "item", "model", "summary", "speed", "count", "key", "length"], "mappings": ";;;;+BAKgBA;;;eAAAA;;;;iEALQ;;;;;;;gEACT;;;;;;uBAES;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEjB,SAASA;IACd,OAAO;QACLC,IAAI;YAAEC,MAAMD,aAAE,CAACE,QAAQ;YAAIC,SAASH,aAAE,CAACI,OAAO;YAAIC,MAAMC,QAAQC,QAAQ,CAACF,IAAI;QAAC;QAC9EG,QAAQ;YAAEC,MAAMT,aAAE,CAACS,IAAI;YAAIC,QAAQC;QAAkB;QACrDC,KAAKC;QACLC,KAAK;YAAEb,MAAM;YAAYE,SAASG,QAAQS,GAAG,CAACC,cAAc;QAAC;QAC7DC,IAAIC,UAAOC,IAAI,GAAG;YAAElB,MAAMiB,UAAOjB,IAAI;YAAEmB,MAAMF,UAAOG,IAAI;QAAC,IAAIC;IAC/D;AACF;AAEA,SAASX;IACP,MAAMY,KAAKvB,aAAE,CAACwB,QAAQ,KAAK,OAAO,OAAO;IACzC,OAAOC,KAAKC,KAAK,CAACH,KAAK,OAAO;AAChC;AAEA,SAASV;IACP,MAAMc,OAAOC,IAAAA,cAAO,EAAC5B,aAAE,CAAC2B,IAAI,MAAM,EAAE,EAAE,CAACE,OAASA,KAAKC,KAAK;IAC1D,MAAMC,UAAU;QAAED,OAAO;QAAIE,OAAO;QAAGC,OAAO;IAAE;IAEhD,IAAK,MAAMC,OAAOP,KAAM;QACtB,IAAIA,IAAI,CAACO,IAAI,CAACC,MAAM,GAAGJ,QAAQE,KAAK,EAAE;YACpCF,QAAQD,KAAK,GAAGI;YAChBH,QAAQC,KAAK,GAAGL,IAAI,CAACO,IAAI,CAAC,EAAE,CAACF,KAAK;YAClCD,QAAQE,KAAK,GAAGN,IAAI,CAACO,IAAI,CAACC,MAAM;QAClC;IACF;IAEA,OAAO,CAACJ,QAAQD,KAAK,IAAI,CAACC,QAAQE,KAAK,GAAGX,YAAYS;AACxD"}
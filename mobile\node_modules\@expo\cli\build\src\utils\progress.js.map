{"version": 3, "sources": ["../../../src/utils/progress.ts"], "sourcesContent": ["import ProgressBar from 'progress';\n\nlet currentProgress: ProgressBar | null = null;\n\nexport function setProgressBar(bar: ProgressBar | null): void {\n  currentProgress = bar;\n}\n\nexport function getProgressBar(): ProgressBar | null {\n  return currentProgress;\n}\n\nexport function createProgressBar(barFormat: string, options: ProgressBar.ProgressBarOptions) {\n  if (process.stderr.clearLine == null) {\n    return null;\n  }\n\n  const bar = new ProgressBar(barFormat, options);\n\n  const logReal = console.log;\n  const infoReal = console.info;\n  const warnReal = console.warn;\n  const errorReal = console.error;\n\n  const wrapNativeLogs = (): void => {\n    // @ts-expect-error\n    console.log = (...args: any) => bar.interrupt(...args);\n    // @ts-expect-error\n    console.info = (...args: any) => bar.interrupt(...args);\n    // @ts-expect-error\n    console.warn = (...args: any) => bar.interrupt(...args);\n    // @ts-expect-error\n    console.error = (...args: any) => bar.interrupt(...args);\n  };\n\n  const resetNativeLogs = (): void => {\n    console.log = logReal;\n    console.info = infoReal;\n    console.warn = warnReal;\n    console.error = errorReal;\n  };\n\n  const originalTerminate = bar.terminate.bind(bar);\n  bar.terminate = () => {\n    resetNativeLogs();\n    setProgressBar(null);\n    originalTerminate();\n  };\n\n  wrapNativeLogs();\n  setProgressBar(bar);\n  return bar;\n}\n"], "names": ["createProgressBar", "getProgressBar", "setProgressBar", "currentProgress", "bar", "barFormat", "options", "process", "stderr", "clearLine", "ProgressBar", "logReal", "console", "log", "infoReal", "info", "warnReal", "warn", "errorReal", "error", "wrapNativeLogs", "args", "interrupt", "resetNativeLogs", "originalTerminate", "terminate", "bind"], "mappings": ";;;;;;;;;;;IAYgBA,iBAAiB;eAAjBA;;IAJAC,cAAc;eAAdA;;IAJAC,cAAc;eAAdA;;;;gEAJQ;;;;;;;;;;;AAExB,IAAIC,kBAAsC;AAEnC,SAASD,eAAeE,GAAuB;IACpDD,kBAAkBC;AACpB;AAEO,SAASH;IACd,OAAOE;AACT;AAEO,SAASH,kBAAkBK,SAAiB,EAAEC,OAAuC;IAC1F,IAAIC,QAAQC,MAAM,CAACC,SAAS,IAAI,MAAM;QACpC,OAAO;IACT;IAEA,MAAML,MAAM,IAAIM,CAAAA,WAAU,SAAC,CAACL,WAAWC;IAEvC,MAAMK,UAAUC,QAAQC,GAAG;IAC3B,MAAMC,WAAWF,QAAQG,IAAI;IAC7B,MAAMC,WAAWJ,QAAQK,IAAI;IAC7B,MAAMC,YAAYN,QAAQO,KAAK;IAE/B,MAAMC,iBAAiB;QACrB,mBAAmB;QACnBR,QAAQC,GAAG,GAAG,CAAC,GAAGQ,OAAcjB,IAAIkB,SAAS,IAAID;QACjD,mBAAmB;QACnBT,QAAQG,IAAI,GAAG,CAAC,GAAGM,OAAcjB,IAAIkB,SAAS,IAAID;QAClD,mBAAmB;QACnBT,QAAQK,IAAI,GAAG,CAAC,GAAGI,OAAcjB,IAAIkB,SAAS,IAAID;QAClD,mBAAmB;QACnBT,QAAQO,KAAK,GAAG,CAAC,GAAGE,OAAcjB,IAAIkB,SAAS,IAAID;IACrD;IAEA,MAAME,kBAAkB;QACtBX,QAAQC,GAAG,GAAGF;QACdC,QAAQG,IAAI,GAAGD;QACfF,QAAQK,IAAI,GAAGD;QACfJ,QAAQO,KAAK,GAAGD;IAClB;IAEA,MAAMM,oBAAoBpB,IAAIqB,SAAS,CAACC,IAAI,CAACtB;IAC7CA,IAAIqB,SAAS,GAAG;QACdF;QACArB,eAAe;QACfsB;IACF;IAEAJ;IACAlB,eAAeE;IACf,OAAOA;AACT"}
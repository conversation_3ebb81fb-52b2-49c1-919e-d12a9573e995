{"version": 3, "sources": ["../../../../../../../src/start/server/metro/dev-server/utils/createSocketBroadcaster.ts"], "sourcesContent": ["import type { SocketId, SocketMap } from './createSocketMap';\n\nconst debug = require('debug')('expo:metro:dev-server:broadcaster') as typeof console.log;\n\nexport function createBroadcaster(sockets: SocketMap) {\n  return function broadcast(senderSocketId: SocketId | null, message: string) {\n    // Ignore if there are no connected sockets\n    if (!sockets.size) return;\n\n    for (const [socketId, socket] of sockets) {\n      if (socketId === senderSocketId) continue;\n\n      try {\n        socket.send(message);\n      } catch (error) {\n        debug(`Failed to broadcast message to socket \"${socketId}\"`, error);\n      }\n    }\n  };\n}\n"], "names": ["createBroadcaster", "debug", "require", "sockets", "broadcast", "senderSocketId", "message", "size", "socketId", "socket", "send", "error"], "mappings": ";;;;+BAIgBA;;;eAAAA;;;AAFhB,MAAMC,QAAQC,QAAQ,SAAS;AAExB,SAASF,kBAAkBG,OAAkB;IAClD,OAAO,SAASC,UAAUC,cAA+B,EAAEC,OAAe;QACxE,2CAA2C;QAC3C,IAAI,CAACH,QAAQI,IAAI,EAAE;QAEnB,KAAK,MAAM,CAACC,UAAUC,OAAO,IAAIN,QAAS;YACxC,IAAIK,aAAaH,gBAAgB;YAEjC,IAAI;gBACFI,OAAOC,IAAI,CAACJ;YACd,EAAE,OAAOK,OAAO;gBACdV,MAAM,CAAC,uCAAuC,EAAEO,SAAS,CAAC,CAAC,EAAEG;YAC/D;QACF;IACF;AACF"}
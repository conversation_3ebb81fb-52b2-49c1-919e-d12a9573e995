{"version": 3, "sources": ["../../../../../../src/run/ios/appleDevice/protocol/AFCProtocol.ts"], "sourcesContent": ["/**\n * Copyright (c) 2021 Expo, Inc.\n * Copyright (c) 2018 Drifty Co.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport Debug from 'debug';\nimport { Socket } from 'net';\n\nimport type { ProtocolReaderCallback, ProtocolWriter } from './AbstractProtocol';\nimport { ProtocolClient, ProtocolReader, ProtocolReaderFactory } from './AbstractProtocol';\nimport { CommandError } from '../../../../utils/errors';\n\nconst debug = Debug('expo:apple-device:protocol:afc');\n\nexport const AFC_MAGIC = 'CFA6LPAA';\nexport const AFC_HEADER_SIZE = 40;\n\nexport interface AFCHeader {\n  magic: typeof AFC_MAGIC;\n  totalLength: number;\n  headerLength: number;\n  requestId: number;\n  operation: AFC_OPS;\n}\n\nexport interface AFCMessage {\n  operation: AFC_OPS;\n  data?: any;\n  payload?: any;\n}\n\nexport interface AFCResponse {\n  operation: AFC_OPS;\n  id: number;\n  data: Buffer;\n}\n\nexport interface AFCStatusResponse {\n  operation: AFC_OPS.STATUS;\n  id: number;\n  data: number;\n}\n\n/**\n * AFC Operations\n */\nexport enum AFC_OPS {\n  /**\n   * Invalid\n   */\n  INVALID = 0x00000000,\n\n  /**\n   * Status\n   */\n  STATUS = 0x00000001,\n\n  /**\n   * Data\n   */\n  DATA = 0x00000002,\n\n  /**\n   * ReadDir\n   */\n  READ_DIR = 0x00000003,\n\n  /**\n   * ReadFile\n   */\n  READ_FILE = 0x00000004,\n\n  /**\n   * WriteFile\n   */\n  WRITE_FILE = 0x00000005,\n\n  /**\n   * WritePart\n   */\n  WRITE_PART = 0x00000006,\n\n  /**\n   * TruncateFile\n   */\n  TRUNCATE = 0x00000007,\n\n  /**\n   * RemovePath\n   */\n  REMOVE_PATH = 0x00000008,\n\n  /**\n   * MakeDir\n   */\n  MAKE_DIR = 0x00000009,\n\n  /**\n   * GetFileInfo\n   */\n  GET_FILE_INFO = 0x0000000a,\n\n  /**\n   * GetDeviceInfo\n   */\n  GET_DEVINFO = 0x0000000b,\n\n  /**\n   * WriteFileAtomic (tmp file+rename)\n   */\n  WRITE_FILE_ATOM = 0x0000000c,\n\n  /**\n   * FileRefOpen\n   */\n  FILE_OPEN = 0x0000000d,\n\n  /**\n   * FileRefOpenResult\n   */\n  FILE_OPEN_RES = 0x0000000e,\n\n  /**\n   * FileRefRead\n   */\n  FILE_READ = 0x0000000f,\n\n  /**\n   * FileRefWrite\n   */\n  FILE_WRITE = 0x00000010,\n\n  /**\n   * FileRefSeek\n   */\n  FILE_SEEK = 0x00000011,\n\n  /**\n   * FileRefTell\n   */\n  FILE_TELL = 0x00000012,\n\n  /**\n   * FileRefTellResult\n   */\n  FILE_TELL_RES = 0x00000013,\n\n  /**\n   * FileRefClose\n   */\n  FILE_CLOSE = 0x00000014,\n\n  /**\n   * FileRefSetFileSize (ftruncate)\n   */\n  FILE_SET_SIZE = 0x00000015,\n\n  /**\n   * GetConnectionInfo\n   */\n  GET_CON_INFO = 0x00000016,\n\n  /**\n   * SetConnectionOptions\n   */\n  SET_CON_OPTIONS = 0x00000017,\n\n  /**\n   * RenamePath\n   */\n  RENAME_PATH = 0x00000018,\n\n  /**\n   * SetFSBlockSize (0x800000)\n   */\n  SET_FS_BS = 0x00000019,\n\n  /**\n   * SetSocketBlockSize (0x800000)\n   */\n  SET_SOCKET_BS = 0x0000001a,\n\n  /**\n   * FileRefLock\n   */\n  FILE_LOCK = 0x0000001b,\n\n  /**\n   * MakeLink\n   */\n  MAKE_LINK = 0x0000001c,\n\n  /**\n   * GetFileHash\n   */\n  GET_FILE_HASH = 0x0000001d,\n\n  /**\n   * SetModTime\n   */\n  SET_FILE_MOD_TIME = 0x0000001e,\n\n  /**\n   * GetFileHashWithRange\n   */\n  GET_FILE_HASH_RANGE = 0x0000001f,\n\n  // iOS 6+\n\n  /**\n   * FileRefSetImmutableHint\n   */\n  FILE_SET_IMMUTABLE_HINT = 0x00000020,\n\n  /**\n   * GetSizeOfPathContents\n   */\n  GET_SIZE_OF_PATH_CONTENTS = 0x00000021,\n\n  /**\n   * RemovePathAndContents\n   */\n  REMOVE_PATH_AND_CONTENTS = 0x00000022,\n\n  /**\n   * DirectoryEnumeratorRefOpen\n   */\n  DIR_OPEN = 0x00000023,\n\n  /**\n   * DirectoryEnumeratorRefOpenResult\n   */\n  DIR_OPEN_RESULT = 0x00000024,\n\n  /**\n   * DirectoryEnumeratorRefRead\n   */\n  DIR_READ = 0x00000025,\n\n  /**\n   * DirectoryEnumeratorRefClose\n   */\n  DIR_CLOSE = 0x00000026,\n\n  // iOS 7+\n\n  /**\n   * FileRefReadWithOffset\n   */\n  FILE_READ_OFFSET = 0x00000027,\n\n  /**\n   * FileRefWriteWithOffset\n   */\n  FILE_WRITE_OFFSET = 0x00000028,\n}\n\n/**\n * Error Codes\n */\nexport enum AFC_STATUS {\n  SUCCESS = 0,\n  UNKNOWN_ERROR = 1,\n  OP_HEADER_INVALID = 2,\n  NO_RESOURCES = 3,\n  READ_ERROR = 4,\n  WRITE_ERROR = 5,\n  UNKNOWN_PACKET_TYPE = 6,\n  INVALID_ARG = 7,\n  OBJECT_NOT_FOUND = 8,\n  OBJECT_IS_DIR = 9,\n  PERM_DENIED = 10,\n  SERVICE_NOT_CONNECTED = 11,\n  OP_TIMEOUT = 12,\n  TOO_MUCH_DATA = 13,\n  END_OF_DATA = 14,\n  OP_NOT_SUPPORTED = 15,\n  OBJECT_EXISTS = 16,\n  OBJECT_BUSY = 17,\n  NO_SPACE_LEFT = 18,\n  OP_WOULD_BLOCK = 19,\n  IO_ERROR = 20,\n  OP_INTERRUPTED = 21,\n  OP_IN_PROGRESS = 22,\n  INTERNAL_ERROR = 23,\n  MUX_ERROR = 30,\n  NO_MEM = 31,\n  NOT_ENOUGH_DATA = 32,\n  DIR_NOT_EMPTY = 33,\n  FORCE_SIGNED_TYPE = -1,\n}\n\nexport enum AFC_FILE_OPEN_FLAGS {\n  /**\n   * r (O_RDONLY)\n   */\n  RDONLY = 0x00000001,\n\n  /**\n   * r+ (O_RDWR | O_CREAT)\n   */\n  RW = 0x00000002,\n\n  /**\n   * w (O_WRONLY | O_CREAT | O_TRUNC)\n   */\n  WRONLY = 0x00000003,\n\n  /**\n   * w+ (O_RDWR | O_CREAT  | O_TRUNC)\n   */\n  WR = 0x00000004,\n\n  /**\n   * a (O_WRONLY | O_APPEND | O_CREAT)\n   */\n  APPEND = 0x00000005,\n\n  /**\n   * a+ (O_RDWR | O_APPEND | O_CREAT)\n   */\n  RDAPPEND = 0x00000006,\n}\n\nfunction isAFCResponse(resp: any): resp is AFCResponse {\n  return AFC_OPS[resp.operation] !== undefined && resp.id !== undefined && resp.data !== undefined;\n}\n\nfunction isStatusResponse(resp: any): resp is AFCStatusResponse {\n  return isAFCResponse(resp) && resp.operation === AFC_OPS.STATUS;\n}\n\nfunction isErrorStatusResponse(resp: AFCResponse): boolean {\n  return isStatusResponse(resp) && resp.data !== AFC_STATUS.SUCCESS;\n}\n\nclass AFCInternalError extends Error {\n  constructor(\n    msg: string,\n    public requestId: number\n  ) {\n    super(msg);\n  }\n}\n\nexport class AFCError extends Error {\n  constructor(\n    msg: string,\n    public status: AFC_STATUS\n  ) {\n    super(msg);\n  }\n}\n\nexport class AFCProtocolClient extends ProtocolClient {\n  private requestId = 0;\n  private requestCallbacks: { [key: number]: ProtocolReaderCallback } = {};\n\n  constructor(socket: Socket) {\n    super(socket, new ProtocolReaderFactory(AFCProtocolReader), new AFCProtocolWriter());\n\n    const reader = this.readerFactory.create((resp, err) => {\n      if (err && err instanceof AFCInternalError) {\n        this.requestCallbacks[err.requestId](resp, err);\n      } else if (isErrorStatusResponse(resp)) {\n        this.requestCallbacks[resp.id](resp, new AFCError(AFC_STATUS[resp.data], resp.data));\n      } else {\n        this.requestCallbacks[resp.id](resp);\n      }\n    });\n    socket.on('data', reader.onData);\n  }\n\n  sendMessage(msg: AFCMessage): Promise<AFCResponse> {\n    return new Promise<AFCResponse>((resolve, reject) => {\n      const requestId = this.requestId++;\n      this.requestCallbacks[requestId] = async (resp: any, err?: Error) => {\n        if (err) {\n          reject(err);\n          return;\n        }\n        if (isAFCResponse(resp)) {\n          resolve(resp);\n        } else {\n          reject(new CommandError('APPLE_DEVICE_AFC', 'Malformed AFC response'));\n        }\n      };\n      this.writer.write(this.socket, { ...msg, requestId });\n    });\n  }\n}\n\nexport class AFCProtocolReader extends ProtocolReader {\n  private header!: AFCHeader; // TODO: ! -> ?\n\n  constructor(callback: ProtocolReaderCallback) {\n    super(AFC_HEADER_SIZE, callback);\n  }\n\n  parseHeader(data: Buffer) {\n    const magic = data.slice(0, 8).toString('ascii');\n    if (magic !== AFC_MAGIC) {\n      throw new AFCInternalError(\n        `Invalid AFC packet received (magic != ${AFC_MAGIC})`,\n        data.readUInt32LE(24)\n      );\n    }\n    // technically these are uint64\n    this.header = {\n      magic,\n      totalLength: data.readUInt32LE(8),\n      headerLength: data.readUInt32LE(16),\n      requestId: data.readUInt32LE(24),\n      operation: data.readUInt32LE(32),\n    };\n\n    debug(`parse header: ${JSON.stringify(this.header)}`);\n    if (this.header.headerLength < AFC_HEADER_SIZE) {\n      throw new AFCInternalError('Invalid AFC header', this.header.requestId);\n    }\n    return this.header.totalLength - AFC_HEADER_SIZE;\n  }\n\n  parseBody(data: Buffer): AFCResponse | AFCStatusResponse {\n    const body: any = {\n      operation: this.header.operation,\n      id: this.header.requestId,\n      data,\n    };\n    if (isStatusResponse(body)) {\n      const status = data.readUInt32LE(0);\n      debug(`${AFC_OPS[this.header.operation]} response: ${AFC_STATUS[status]}`);\n      body.data = status;\n    } else if (data.length <= 8) {\n      debug(`${AFC_OPS[this.header.operation]} response: ${Array.prototype.toString.call(body)}`);\n    } else {\n      debug(`${AFC_OPS[this.header.operation]} response length: ${data.length} bytes`);\n    }\n    return body;\n  }\n}\n\nexport class AFCProtocolWriter implements ProtocolWriter {\n  write(socket: Socket, msg: AFCMessage & { requestId: number }) {\n    const { data, payload, operation, requestId } = msg;\n\n    const dataLength = data ? data.length : 0;\n    const payloadLength = payload ? payload.length : 0;\n\n    const header = Buffer.alloc(AFC_HEADER_SIZE);\n    const magic = Buffer.from(AFC_MAGIC);\n    magic.copy(header);\n    header.writeUInt32LE(AFC_HEADER_SIZE + dataLength + payloadLength, 8);\n    header.writeUInt32LE(AFC_HEADER_SIZE + dataLength, 16);\n    header.writeUInt32LE(requestId, 24);\n    header.writeUInt32LE(operation, 32);\n    socket.write(header);\n    socket.write(data);\n    if (data.length <= 8) {\n      debug(\n        `socket write, header: { requestId: ${requestId}, operation: ${\n          AFC_OPS[operation]\n        }}, body: ${Array.prototype.toString.call(data)}`\n      );\n    } else {\n      debug(\n        `socket write, header: { requestId: ${requestId}, operation: ${AFC_OPS[operation]}}, body: ${data.length} bytes`\n      );\n    }\n\n    debug(`socket write, bytes written ${header.length} (header), ${data.length} (body)`);\n    if (payload) {\n      socket.write(payload);\n    }\n  }\n}\n"], "names": ["AFCError", "AFCProtocolClient", "AFCProtocolReader", "AFCProtocolWriter", "AFC_FILE_OPEN_FLAGS", "AFC_HEADER_SIZE", "AFC_MAGIC", "AFC_OPS", "AFC_STATUS", "debug", "Debug", "isAFCResponse", "resp", "operation", "undefined", "id", "data", "isStatusResponse", "isErrorStatusResponse", "AFCInternalError", "Error", "constructor", "msg", "requestId", "status", "ProtocolClient", "socket", "ProtocolReaderFactory", "requestCallbacks", "reader", "readerFactory", "create", "err", "on", "onData", "sendMessage", "Promise", "resolve", "reject", "CommandError", "writer", "write", "ProtocolReader", "callback", "parse<PERSON><PERSON><PERSON>", "magic", "slice", "toString", "readUInt32LE", "header", "totalLength", "<PERSON><PERSON><PERSON><PERSON>", "JSON", "stringify", "parseBody", "body", "length", "Array", "prototype", "call", "payload", "dataLength", "payloadLength", "<PERSON><PERSON><PERSON>", "alloc", "from", "copy", "writeUInt32LE"], "mappings": "AAAA;;;;;;CAMC;;;;;;;;;;;IAqVYA,QAAQ;eAARA;;IASAC,iBAAiB;eAAjBA;;IAsCAC,iBAAiB;eAAjBA;;IAkDAC,iBAAiB;eAAjBA;;IAtJDC,mBAAmB;eAAnBA;;IArRCC,eAAe;eAAfA;;IADAC,SAAS;eAATA;;IAgCDC,OAAO;eAAPA;;IAsNAC,UAAU;eAAVA;;;;gEA/PM;;;;;;kCAIoD;wBACzC;;;;;;AAE7B,MAAMC,QAAQC,IAAAA,gBAAK,EAAC;AAEb,MAAMJ,YAAY;AAClB,MAAMD,kBAAkB;AA+BxB,IAAA,AAAKE,iCAAAA;IACV;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD,SAAS;IAET;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD,SAAS;IAET;;GAEC;IAGD;;GAEC;WA/MSA;;AAsNL,IAAA,AAAKC,oCAAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;WAAAA;;AAgCL,IAAA,AAAKJ,6CAAAA;IACV;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;WA5BSA;;AAgCZ,SAASO,cAAcC,IAAS;IAC9B,OAAOL,OAAO,CAACK,KAAKC,SAAS,CAAC,KAAKC,aAAaF,KAAKG,EAAE,KAAKD,aAAaF,KAAKI,IAAI,KAAKF;AACzF;AAEA,SAASG,iBAAiBL,IAAS;IACjC,OAAOD,cAAcC,SAASA,KAAKC,SAAS;AAC9C;AAEA,SAASK,sBAAsBN,IAAiB;IAC9C,OAAOK,iBAAiBL,SAASA,KAAKI,IAAI;AAC5C;AAEA,MAAMG,yBAAyBC;IAC7BC,YACEC,GAAW,EACX,AAAOC,SAAiB,CACxB;QACA,KAAK,CAACD,WAFCC,YAAAA;IAGT;AACF;AAEO,MAAMvB,iBAAiBoB;IAC5BC,YACEC,GAAW,EACX,AAAOE,MAAkB,CACzB;QACA,KAAK,CAACF,WAFCE,SAAAA;IAGT;AACF;AAEO,MAAMvB,0BAA0BwB,gCAAc;IAInDJ,YAAYK,MAAc,CAAE;QAC1B,KAAK,CAACA,QAAQ,IAAIC,uCAAqB,CAACzB,oBAAoB,IAAIC,2BAJ1DoB,YAAY,QACZK,mBAA8D,CAAC;QAKrE,MAAMC,SAAS,IAAI,CAACC,aAAa,CAACC,MAAM,CAAC,CAACnB,MAAMoB;YAC9C,IAAIA,OAAOA,eAAeb,kBAAkB;gBAC1C,IAAI,CAACS,gBAAgB,CAACI,IAAIT,SAAS,CAAC,CAACX,MAAMoB;YAC7C,OAAO,IAAId,sBAAsBN,OAAO;gBACtC,IAAI,CAACgB,gBAAgB,CAAChB,KAAKG,EAAE,CAAC,CAACH,MAAM,IAAIZ,SAASQ,UAAU,CAACI,KAAKI,IAAI,CAAC,EAAEJ,KAAKI,IAAI;YACpF,OAAO;gBACL,IAAI,CAACY,gBAAgB,CAAChB,KAAKG,EAAE,CAAC,CAACH;YACjC;QACF;QACAc,OAAOO,EAAE,CAAC,QAAQJ,OAAOK,MAAM;IACjC;IAEAC,YAAYb,GAAe,EAAwB;QACjD,OAAO,IAAIc,QAAqB,CAACC,SAASC;YACxC,MAAMf,YAAY,IAAI,CAACA,SAAS;YAChC,IAAI,CAACK,gBAAgB,CAACL,UAAU,GAAG,OAAOX,MAAWoB;gBACnD,IAAIA,KAAK;oBACPM,OAAON;oBACP;gBACF;gBACA,IAAIrB,cAAcC,OAAO;oBACvByB,QAAQzB;gBACV,OAAO;oBACL0B,OAAO,IAAIC,oBAAY,CAAC,oBAAoB;gBAC9C;YACF;YACA,IAAI,CAACC,MAAM,CAACC,KAAK,CAAC,IAAI,CAACf,MAAM,EAAE;gBAAE,GAAGJ,GAAG;gBAAEC;YAAU;QACrD;IACF;AACF;AAEO,MAAMrB,0BAA0BwC,gCAAc;IAGnDrB,YAAYsB,QAAgC,CAAE;QAC5C,KAAK,CAACtC,iBAAiBsC;IACzB;IAEAC,YAAY5B,IAAY,EAAE;QACxB,MAAM6B,QAAQ7B,KAAK8B,KAAK,CAAC,GAAG,GAAGC,QAAQ,CAAC;QACxC,IAAIF,UAAUvC,WAAW;YACvB,MAAM,IAAIa,iBACR,CAAC,sCAAsC,EAAEb,UAAU,CAAC,CAAC,EACrDU,KAAKgC,YAAY,CAAC;QAEtB;QACA,+BAA+B;QAC/B,IAAI,CAACC,MAAM,GAAG;YACZJ;YACAK,aAAalC,KAAKgC,YAAY,CAAC;YAC/BG,cAAcnC,KAAKgC,YAAY,CAAC;YAChCzB,WAAWP,KAAKgC,YAAY,CAAC;YAC7BnC,WAAWG,KAAKgC,YAAY,CAAC;QAC/B;QAEAvC,MAAM,CAAC,cAAc,EAAE2C,KAAKC,SAAS,CAAC,IAAI,CAACJ,MAAM,GAAG;QACpD,IAAI,IAAI,CAACA,MAAM,CAACE,YAAY,GAAG9C,iBAAiB;YAC9C,MAAM,IAAIc,iBAAiB,sBAAsB,IAAI,CAAC8B,MAAM,CAAC1B,SAAS;QACxE;QACA,OAAO,IAAI,CAAC0B,MAAM,CAACC,WAAW,GAAG7C;IACnC;IAEAiD,UAAUtC,IAAY,EAAmC;QACvD,MAAMuC,OAAY;YAChB1C,WAAW,IAAI,CAACoC,MAAM,CAACpC,SAAS;YAChCE,IAAI,IAAI,CAACkC,MAAM,CAAC1B,SAAS;YACzBP;QACF;QACA,IAAIC,iBAAiBsC,OAAO;YAC1B,MAAM/B,SAASR,KAAKgC,YAAY,CAAC;YACjCvC,MAAM,GAAGF,OAAO,CAAC,IAAI,CAAC0C,MAAM,CAACpC,SAAS,CAAC,CAAC,WAAW,EAAEL,UAAU,CAACgB,OAAO,EAAE;YACzE+B,KAAKvC,IAAI,GAAGQ;QACd,OAAO,IAAIR,KAAKwC,MAAM,IAAI,GAAG;YAC3B/C,MAAM,GAAGF,OAAO,CAAC,IAAI,CAAC0C,MAAM,CAACpC,SAAS,CAAC,CAAC,WAAW,EAAE4C,MAAMC,SAAS,CAACX,QAAQ,CAACY,IAAI,CAACJ,OAAO;QAC5F,OAAO;YACL9C,MAAM,GAAGF,OAAO,CAAC,IAAI,CAAC0C,MAAM,CAACpC,SAAS,CAAC,CAAC,kBAAkB,EAAEG,KAAKwC,MAAM,CAAC,MAAM,CAAC;QACjF;QACA,OAAOD;IACT;AACF;AAEO,MAAMpD;IACXsC,MAAMf,MAAc,EAAEJ,GAAuC,EAAE;QAC7D,MAAM,EAAEN,IAAI,EAAE4C,OAAO,EAAE/C,SAAS,EAAEU,SAAS,EAAE,GAAGD;QAEhD,MAAMuC,aAAa7C,OAAOA,KAAKwC,MAAM,GAAG;QACxC,MAAMM,gBAAgBF,UAAUA,QAAQJ,MAAM,GAAG;QAEjD,MAAMP,SAASc,OAAOC,KAAK,CAAC3D;QAC5B,MAAMwC,QAAQkB,OAAOE,IAAI,CAAC3D;QAC1BuC,MAAMqB,IAAI,CAACjB;QACXA,OAAOkB,aAAa,CAAC9D,kBAAkBwD,aAAaC,eAAe;QACnEb,OAAOkB,aAAa,CAAC9D,kBAAkBwD,YAAY;QACnDZ,OAAOkB,aAAa,CAAC5C,WAAW;QAChC0B,OAAOkB,aAAa,CAACtD,WAAW;QAChCa,OAAOe,KAAK,CAACQ;QACbvB,OAAOe,KAAK,CAACzB;QACb,IAAIA,KAAKwC,MAAM,IAAI,GAAG;YACpB/C,MACE,CAAC,mCAAmC,EAAEc,UAAU,aAAa,EAC3DhB,OAAO,CAACM,UAAU,CACnB,SAAS,EAAE4C,MAAMC,SAAS,CAACX,QAAQ,CAACY,IAAI,CAAC3C,OAAO;QAErD,OAAO;YACLP,MACE,CAAC,mCAAmC,EAAEc,UAAU,aAAa,EAAEhB,OAAO,CAACM,UAAU,CAAC,SAAS,EAAEG,KAAKwC,MAAM,CAAC,MAAM,CAAC;QAEpH;QAEA/C,MAAM,CAAC,4BAA4B,EAAEwC,OAAOO,MAAM,CAAC,WAAW,EAAExC,KAAKwC,MAAM,CAAC,OAAO,CAAC;QACpF,IAAII,SAAS;YACXlC,OAAOe,KAAK,CAACmB;QACf;IACF;AACF"}
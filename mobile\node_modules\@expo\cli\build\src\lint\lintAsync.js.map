{"version": 3, "sources": ["../../../src/lint/lintAsync.ts"], "sourcesContent": ["import { createForProject } from '@expo/package-manager';\nimport fs from 'node:fs';\nimport path from 'node:path';\nimport semver from 'semver';\n\nimport { ESLintProjectPrerequisite } from './ESlintPrerequisite';\nimport type { Options } from './resolveOptions';\nimport { CommandError } from '../utils/errors';\nimport { findUpProjectRootOrAssert } from '../utils/findUp';\nimport { setNodeEnv } from '../utils/nodeEnv';\n\nconst debug = require('debug')('expo:lint');\n\nconst DEFAULT_INPUTS = ['src', 'app', 'components'];\n\nexport const lintAsync = async (\n  inputs: string[],\n  options: Options & { projectRoot?: string },\n  eslintArguments: string[] = []\n) => {\n  setNodeEnv('development');\n  // Locate the project root based on the process current working directory.\n  // This enables users to run `npx expo install` from a subdirectory of the project.\n  const projectRoot = options?.projectRoot ?? findUpProjectRootOrAssert(process.cwd());\n  require('@expo/env').load(projectRoot);\n\n  // TODO: Perhaps we should assert that TypeScript is required.\n\n  const prerequisite = new ESLintProjectPrerequisite(projectRoot);\n  if (!(await prerequisite.assertAsync())) {\n    await prerequisite.bootstrapAsync();\n  }\n\n  const { loadESLint } = require('eslint');\n\n  const mod = await import('eslint');\n\n  let ESLint: typeof import('eslint').ESLint;\n  // loadESLint is >= 8.57.0 (https://github.com/eslint/eslint/releases/tag/v8.57.0) https://github.com/eslint/eslint/pull/18098\n  if ('loadESLint' in mod) {\n    ESLint = await loadESLint({ cwd: options.projectRoot });\n  } else {\n    throw new CommandError(\n      'npx expo lint requires ESLint version 8.57.0 or greater. Upgrade eslint or use npx eslint directly.'\n    );\n  }\n\n  const version = ESLint?.version;\n\n  if (!version || semver.lt(version, '8.57.0')) {\n    throw new CommandError(\n      'npx expo lint requires ESLint version 8.57.0 or greater. Upgrade eslint or use npx eslint directly.'\n    );\n  }\n\n  if (!inputs.length) {\n    DEFAULT_INPUTS.map((input) => {\n      const abs = path.join(projectRoot, input);\n      if (fs.existsSync(abs)) {\n        inputs.push(abs);\n      }\n    });\n  }\n\n  const eslintArgs: string[] = [];\n  inputs.forEach((input) => {\n    eslintArgs.push(input);\n  });\n  options.ext.forEach((ext) => {\n    eslintArgs.push('--ext', ext);\n  });\n\n  eslintArgs.push(`--fix=${options.fix}`);\n  eslintArgs.push(`--cache=${options.cache}`);\n\n  if (options.config) {\n    eslintArgs.push(`--config`, options.config);\n  }\n  if (!options.ignore) {\n    eslintArgs.push('--no-ignore');\n  }\n  options.ignorePattern.forEach((pattern) => {\n    eslintArgs.push(`--ignore-pattern=${pattern}`);\n  });\n\n  eslintArgs.push(...options.fixType.map((type) => `--fix-type=${type}`));\n\n  if (options.quiet) {\n    eslintArgs.push('--quiet');\n  }\n\n  if (options.maxWarnings != null && options.maxWarnings >= 0) {\n    eslintArgs.push(`--max-warnings=${options.maxWarnings.toString()}`);\n  }\n\n  const cacheDir = path.join(projectRoot, '.expo', 'cache', 'eslint/');\n  // Add other defaults\n  eslintArgs.push(`--cache-location=${cacheDir}`);\n\n  // Add passthrough arguments\n  eslintArguments.forEach((arg) => {\n    eslintArgs.push(arg);\n  });\n\n  debug('Running ESLint with args: %O', eslintArgs);\n\n  const manager = createForProject(projectRoot, { silent: true });\n\n  try {\n    // TODO: Custom logger\n    // - Use relative paths\n    // - When react-hooks/exhaustive-deps is hit, notify about enabling React Compiler.\n    // - Green check when no issues are found.\n    await manager.runBinAsync(['eslint', ...eslintArgs], {\n      stdio: 'inherit',\n    });\n  } catch (error: any) {\n    process.exit(error.status);\n  }\n};\n"], "names": ["lintAsync", "debug", "require", "DEFAULT_INPUTS", "inputs", "options", "eslintArguments", "setNodeEnv", "projectRoot", "findUpProjectRootOrAssert", "process", "cwd", "load", "prerequisite", "ESLintProjectPrerequisite", "assertAsync", "bootstrapAsync", "loadESLint", "mod", "ESLint", "CommandError", "version", "semver", "lt", "length", "map", "input", "abs", "path", "join", "fs", "existsSync", "push", "eslint<PERSON><PERSON><PERSON>", "for<PERSON>ach", "ext", "fix", "cache", "config", "ignore", "ignorePattern", "pattern", "fixType", "type", "quiet", "maxWarnings", "toString", "cacheDir", "arg", "manager", "createForProject", "silent", "runBinAsync", "stdio", "error", "exit", "status"], "mappings": ";;;;+BAeaA;;;eAAAA;;;;yBAfoB;;;;;;;gEAClB;;;;;;;gEACE;;;;;;;gEACE;;;;;;oCAEuB;wBAEb;wBACa;yBACf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3B,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,MAAMC,iBAAiB;IAAC;IAAO;IAAO;CAAa;AAE5C,MAAMH,YAAY,OACvBI,QACAC,SACAC,kBAA4B,EAAE;IAE9BC,IAAAA,mBAAU,EAAC;IACX,0EAA0E;IAC1E,mFAAmF;IACnF,MAAMC,cAAcH,CAAAA,2BAAAA,QAASG,WAAW,KAAIC,IAAAA,iCAAyB,EAACC,QAAQC,GAAG;IACjFT,QAAQ,aAAaU,IAAI,CAACJ;IAE1B,8DAA8D;IAE9D,MAAMK,eAAe,IAAIC,6CAAyB,CAACN;IACnD,IAAI,CAAE,MAAMK,aAAaE,WAAW,IAAK;QACvC,MAAMF,aAAaG,cAAc;IACnC;IAEA,MAAM,EAAEC,UAAU,EAAE,GAAGf,QAAQ;IAE/B,MAAMgB,MAAM,MAAM,mEAAA,QAAO;IAEzB,IAAIC;IACJ,8HAA8H;IAC9H,IAAI,gBAAgBD,KAAK;QACvBC,SAAS,MAAMF,WAAW;YAAEN,KAAKN,QAAQG,WAAW;QAAC;IACvD,OAAO;QACL,MAAM,IAAIY,oBAAY,CACpB;IAEJ;IAEA,MAAMC,UAAUF,0BAAAA,OAAQE,OAAO;IAE/B,IAAI,CAACA,WAAWC,iBAAM,CAACC,EAAE,CAACF,SAAS,WAAW;QAC5C,MAAM,IAAID,oBAAY,CACpB;IAEJ;IAEA,IAAI,CAAChB,OAAOoB,MAAM,EAAE;QAClBrB,eAAesB,GAAG,CAAC,CAACC;YAClB,MAAMC,MAAMC,mBAAI,CAACC,IAAI,CAACrB,aAAakB;YACnC,IAAII,iBAAE,CAACC,UAAU,CAACJ,MAAM;gBACtBvB,OAAO4B,IAAI,CAACL;YACd;QACF;IACF;IAEA,MAAMM,aAAuB,EAAE;IAC/B7B,OAAO8B,OAAO,CAAC,CAACR;QACdO,WAAWD,IAAI,CAACN;IAClB;IACArB,QAAQ8B,GAAG,CAACD,OAAO,CAAC,CAACC;QACnBF,WAAWD,IAAI,CAAC,SAASG;IAC3B;IAEAF,WAAWD,IAAI,CAAC,CAAC,MAAM,EAAE3B,QAAQ+B,GAAG,EAAE;IACtCH,WAAWD,IAAI,CAAC,CAAC,QAAQ,EAAE3B,QAAQgC,KAAK,EAAE;IAE1C,IAAIhC,QAAQiC,MAAM,EAAE;QAClBL,WAAWD,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE3B,QAAQiC,MAAM;IAC5C;IACA,IAAI,CAACjC,QAAQkC,MAAM,EAAE;QACnBN,WAAWD,IAAI,CAAC;IAClB;IACA3B,QAAQmC,aAAa,CAACN,OAAO,CAAC,CAACO;QAC7BR,WAAWD,IAAI,CAAC,CAAC,iBAAiB,EAAES,SAAS;IAC/C;IAEAR,WAAWD,IAAI,IAAI3B,QAAQqC,OAAO,CAACjB,GAAG,CAAC,CAACkB,OAAS,CAAC,WAAW,EAAEA,MAAM;IAErE,IAAItC,QAAQuC,KAAK,EAAE;QACjBX,WAAWD,IAAI,CAAC;IAClB;IAEA,IAAI3B,QAAQwC,WAAW,IAAI,QAAQxC,QAAQwC,WAAW,IAAI,GAAG;QAC3DZ,WAAWD,IAAI,CAAC,CAAC,eAAe,EAAE3B,QAAQwC,WAAW,CAACC,QAAQ,IAAI;IACpE;IAEA,MAAMC,WAAWnB,mBAAI,CAACC,IAAI,CAACrB,aAAa,SAAS,SAAS;IAC1D,qBAAqB;IACrByB,WAAWD,IAAI,CAAC,CAAC,iBAAiB,EAAEe,UAAU;IAE9C,4BAA4B;IAC5BzC,gBAAgB4B,OAAO,CAAC,CAACc;QACvBf,WAAWD,IAAI,CAACgB;IAClB;IAEA/C,MAAM,gCAAgCgC;IAEtC,MAAMgB,UAAUC,IAAAA,kCAAgB,EAAC1C,aAAa;QAAE2C,QAAQ;IAAK;IAE7D,IAAI;QACF,sBAAsB;QACtB,uBAAuB;QACvB,mFAAmF;QACnF,0CAA0C;QAC1C,MAAMF,QAAQG,WAAW,CAAC;YAAC;eAAanB;SAAW,EAAE;YACnDoB,OAAO;QACT;IACF,EAAE,OAAOC,OAAY;QACnB5C,QAAQ6C,IAAI,CAACD,MAAME,MAAM;IAC3B;AACF"}
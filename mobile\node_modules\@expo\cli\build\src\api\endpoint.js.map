{"version": 3, "sources": ["../../../src/api/endpoint.ts"], "sourcesContent": ["import { env } from '../utils/env';\n\n/** Get the URL for the expo.dev API. */\nexport function getExpoApiBaseUrl(): string {\n  if (env.EXPO_STAGING) {\n    return `https://staging-api.expo.dev`;\n  } else if (env.EXPO_LOCAL) {\n    return `http://127.0.0.1:3000`;\n  } else {\n    return `https://api.expo.dev`;\n  }\n}\n\n/** Get the URL for the expo.dev website. */\nexport function getExpoWebsiteBaseUrl(): string {\n  if (env.EXPO_STAGING) {\n    return `https://staging.expo.dev`;\n  } else if (env.EXPO_LOCAL) {\n    return `http://127.0.0.1:3001`;\n  } else {\n    return `https://expo.dev`;\n  }\n}\n"], "names": ["getExpoApiBaseUrl", "getExpoWebsiteBaseUrl", "env", "EXPO_STAGING", "EXPO_LOCAL"], "mappings": ";;;;;;;;;;;IAGgBA,iBAAiB;eAAjBA;;IAWAC,qBAAqB;eAArBA;;;qBAdI;AAGb,SAASD;IACd,IAAIE,QAAG,CAACC,YAAY,EAAE;QACpB,OAAO,CAAC,4BAA4B,CAAC;IACvC,OAAO,IAAID,QAAG,CAACE,UAAU,EAAE;QACzB,OAAO,CAAC,qBAAqB,CAAC;IAChC,OAAO;QACL,OAAO,CAAC,oBAAoB,CAAC;IAC/B;AACF;AAGO,SAASH;IACd,IAAIC,QAAG,CAACC,YAAY,EAAE;QACpB,OAAO,CAAC,wBAAwB,CAAC;IACnC,OAAO,IAAID,QAAG,CAACE,UAAU,EAAE;QACzB,OAAO,CAAC,qBAAqB,CAAC;IAChC,OAAO;QACL,OAAO,CAAC,gBAAgB,CAAC;IAC3B;AACF"}
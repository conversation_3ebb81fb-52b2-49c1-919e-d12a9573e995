{"version": 3, "sources": ["../../../src/utils/fetch.ts"], "sourcesContent": ["import type { FetchLike } from '../api/rest/client.types';\n\n/**\n * The Node's built-in fetch API, but polyfilled from `undici` if necessary.\n * @todo(cedric): remove this once we min-support a Node version where fetch can't be disabled\n */\nexport const fetch: FetchLike =\n  typeof globalThis.fetch !== 'undefined' ? globalThis.fetch : require('undici').fetch;\n\n/**\n * Node's built-in fetch Headers class, or the polyfilled Headers from `undici` when unavailable.\n * @todo(cedric): remove this once we min-support a Node version where fetch can't be disabled\n */\nexport const Headers: typeof import('undici').Headers =\n  typeof globalThis.Headers !== 'undefined' ? globalThis.Headers : require('undici').Headers;\n"], "names": ["Headers", "fetch", "globalThis", "require"], "mappings": ";;;;;;;;;;;IAaaA,OAAO;eAAPA;;IAPAC,KAAK;eAALA;;;AAAN,MAAMA,QACX,OAAOC,WAAWD,KAAK,KAAK,cAAcC,WAAWD,KAAK,GAAGE,QAAQ,UAAUF,KAAK;AAM/E,MAAMD,UACX,OAAOE,WAAWF,OAAO,KAAK,cAAcE,WAAWF,OAAO,GAAGG,QAAQ,UAAUH,OAAO"}
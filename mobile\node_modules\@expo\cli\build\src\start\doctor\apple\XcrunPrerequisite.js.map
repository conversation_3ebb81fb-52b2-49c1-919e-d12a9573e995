{"version": 3, "sources": ["../../../../../src/start/doctor/apple/XcrunPrerequisite.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport chalk from 'chalk';\nimport { execSync } from 'child_process';\n\nimport { delayAsync } from '../../../utils/delay';\nimport { AbortCommandError } from '../../../utils/errors';\nimport { confirmAsync } from '../../../utils/prompts';\nimport { Prerequisite } from '../Prerequisite';\n\nasync function isXcrunInstalledAsync() {\n  try {\n    execSync('xcrun --version', { stdio: 'ignore' });\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nexport class XcrunPrerequisite extends Prerequisite {\n  static instance = new XcrunPrerequisite();\n\n  /**\n   * Ensure Xcode CLI is installed.\n   */\n  async assertImplementation(): Promise<void> {\n    if (await isXcrunInstalledAsync()) {\n      // Run this second to ensure the Xcode version check is run.\n      return;\n    }\n\n    async function pendingAsync(): Promise<void> {\n      if (!(await isXcrunInstalledAsync())) {\n        await delayAsync(100);\n        return await pendingAsync();\n      }\n    }\n\n    // This prompt serves no purpose accept informing the user what to do next, we could just open the App Store but it could be confusing if they don't know what's going on.\n    const confirm = await confirmAsync({\n      initial: true,\n      message: chalk`Xcode {bold Command Line Tools} needs to be installed (requires {bold sudo}), continue?`,\n    });\n\n    if (confirm) {\n      try {\n        await spawnAsync('sudo', [\n          'xcode-select',\n          '--install',\n          // TODO: Is there any harm in skipping this?\n          // '--switch', '/Applications/Xcode.app'\n        ]);\n        // Most likely the user will cancel the process, but if they don't this will continue checking until the CLI is available.\n        return await pendingAsync();\n      } catch {\n        // TODO: Figure out why this might get called (cancel early, network issues, server problems)\n        // TODO: Handle me\n      }\n    }\n\n    throw new AbortCommandError();\n  }\n}\n"], "names": ["XcrunPrerequisite", "isXcrunInstalledAsync", "execSync", "stdio", "Prerequisite", "instance", "assertImplementation", "pendingAsync", "delayAsync", "confirm", "<PERSON><PERSON><PERSON>", "initial", "message", "chalk", "spawnAsync", "AbortCommandError"], "mappings": ";;;;+BAkBaA;;;eAAAA;;;;gEAlBU;;;;;;;gEACL;;;;;;;yBACO;;;;;;uBAEE;wBACO;yBACL;8BACA;;;;;;AAE7B,eAAeC;IACb,IAAI;QACFC,IAAAA,yBAAQ,EAAC,mBAAmB;YAAEC,OAAO;QAAS;QAC9C,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,MAAMH,0BAA0BI,0BAAY;qBAC1CC,WAAW,IAAIL;IAEtB;;GAEC,GACD,MAAMM,uBAAsC;QAC1C,IAAI,MAAML,yBAAyB;YACjC,4DAA4D;YAC5D;QACF;QAEA,eAAeM;YACb,IAAI,CAAE,MAAMN,yBAA0B;gBACpC,MAAMO,IAAAA,iBAAU,EAAC;gBACjB,OAAO,MAAMD;YACf;QACF;QAEA,0KAA0K;QAC1K,MAAME,UAAU,MAAMC,IAAAA,qBAAY,EAAC;YACjCC,SAAS;YACTC,SAASC,IAAAA,gBAAK,CAAA,CAAC,uFAAuF,CAAC;QACzG;QAEA,IAAIJ,SAAS;YACX,IAAI;gBACF,MAAMK,IAAAA,qBAAU,EAAC,QAAQ;oBACvB;oBACA;iBAGD;gBACD,0HAA0H;gBAC1H,OAAO,MAAMP;YACf,EAAE,OAAM;YACN,6FAA6F;YAC7F,kBAAkB;YACpB;QACF;QAEA,MAAM,IAAIQ,yBAAiB;IAC7B;AACF"}
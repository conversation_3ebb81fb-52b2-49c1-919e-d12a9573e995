{"version": 3, "sources": ["../../../src/serve/serveAsync.ts"], "sourcesContent": ["import { create<PERSON>e<PERSON><PERSON>and<PERSON> } from '@expo/server/build/vendor/http';\nimport chalk from 'chalk';\nimport connect from 'connect';\nimport http from 'http';\nimport path from 'path';\nimport send from 'send';\n\nimport * as Log from '../log';\nimport { directoryExistsAsync, fileExistsAsync } from '../utils/dir';\nimport { CommandError } from '../utils/errors';\nimport { findUpProjectRootOrAssert } from '../utils/findUp';\nimport { setNodeEnv } from '../utils/nodeEnv';\nimport { resolvePortAsync } from '../utils/port';\n\ntype Options = {\n  port?: number;\n  isDefaultDirectory: boolean;\n};\n\nconst debug = require('debug')('expo:serve') as typeof console.log;\n\n// Start a basic http server\nexport async function serveAsync(inputDir: string, options: Options) {\n  const projectRoot = findUpProjectRootOrAssert(inputDir);\n\n  setNodeEnv('production');\n  require('@expo/env').load(projectRoot);\n\n  const port = await resolvePortAsync(projectRoot, {\n    defaultPort: options.port,\n    fallbackPort: 8081,\n  });\n\n  if (port == null) {\n    throw new CommandError('Could not start server. Port is not available.');\n  }\n  options.port = port;\n\n  const serverDist = options.isDefaultDirectory ? path.join(inputDir, 'dist') : inputDir;\n  //  TODO: `.expo/server/ios`, `.expo/server/android`, etc.\n\n  if (!(await directoryExistsAsync(serverDist))) {\n    throw new CommandError(\n      `The server directory ${serverDist} does not exist. Run \\`npx expo export\\` first.`\n    );\n  }\n\n  const isStatic = await isStaticExportAsync(serverDist);\n\n  Log.log(chalk.dim(`Starting ${isStatic ? 'static ' : ''}server in ${serverDist}`));\n\n  if (isStatic) {\n    await startStaticServerAsync(serverDist, options);\n  } else {\n    await startDynamicServerAsync(serverDist, options);\n  }\n  Log.log(`Server running at http://localhost:${options.port}`);\n  // Detect the type of server we need to setup:\n}\n\nasync function startStaticServerAsync(dist: string, options: Options) {\n  const server = http.createServer((req, res) => {\n    // Remove query strings and decode URI\n    const filePath = decodeURI(req.url?.split('?')[0] ?? '');\n\n    send(req, filePath, {\n      root: dist,\n      index: 'index.html',\n    })\n      .on('error', (err: any) => {\n        if (err.status === 404) {\n          res.statusCode = 404;\n          res.end('Not Found');\n          return;\n        }\n        res.statusCode = err.status || 500;\n        res.end('Internal Server Error');\n      })\n      .pipe(res);\n  });\n\n  server.listen(options.port!);\n}\n\nasync function startDynamicServerAsync(dist: string, options: Options) {\n  const middleware = connect();\n\n  const staticDirectory = path.join(dist, 'client');\n  const serverDirectory = path.join(dist, 'server');\n\n  const serverHandler = createRequestHandler({ build: serverDirectory });\n\n  // DOM component CORS support\n  middleware.use((req, res, next) => {\n    // TODO: Only when origin is `file://` (iOS), and Android equivalent.\n\n    // Required for DOM components security in release builds.\n\n    res.setHeader('Access-Control-Allow-Origin', '*');\n    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');\n    res.setHeader(\n      'Access-Control-Allow-Headers',\n      'Origin, X-Requested-With, Content-Type, Accept, expo-platform'\n    );\n\n    // Handle OPTIONS preflight requests\n    if (req.method === 'OPTIONS') {\n      res.statusCode = 200;\n      res.end();\n      return;\n    }\n    next();\n  });\n\n  middleware.use((req, res, next) => {\n    if (!req?.url || (req.method !== 'GET' && req.method !== 'HEAD')) {\n      return next();\n    }\n\n    const pathname = canParseURL(req.url) ? new URL(req.url).pathname : req.url;\n    if (!pathname) {\n      return next();\n    }\n\n    debug(`Maybe serve static:`, pathname);\n\n    const stream = send(req, pathname, {\n      root: staticDirectory,\n      extensions: ['html'],\n    });\n\n    // add file listener for fallthrough\n    let forwardError = false;\n    stream.on('file', function onFile() {\n      // once file is determined, always forward error\n      forwardError = true;\n    });\n\n    // forward errors\n    stream.on('error', function error(err: any) {\n      if (forwardError || !(err.statusCode < 500)) {\n        next(err);\n        return;\n      }\n\n      next();\n    });\n\n    // pipe\n    stream.pipe(res);\n  });\n\n  middleware.use(serverHandler);\n\n  middleware.listen(options.port!);\n}\n\nfunction canParseURL(url: string): boolean {\n  try {\n    // eslint-disable-next-line no-new\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nasync function isStaticExportAsync(dist: string): Promise<boolean> {\n  const routesFile = path.join(dist, `server/_expo/routes.json`);\n  return !(await fileExistsAsync(routesFile));\n}\n"], "names": ["serveAsync", "debug", "require", "inputDir", "options", "projectRoot", "findUpProjectRootOrAssert", "setNodeEnv", "load", "port", "resolvePortAsync", "defaultPort", "fallback<PERSON>ort", "CommandError", "serverDist", "isDefaultDirectory", "path", "join", "directoryExistsAsync", "isStatic", "isStaticExportAsync", "Log", "log", "chalk", "dim", "startStaticServerAsync", "startDynamicServerAsync", "dist", "server", "http", "createServer", "req", "res", "filePath", "decodeURI", "url", "split", "send", "root", "index", "on", "err", "status", "statusCode", "end", "pipe", "listen", "middleware", "connect", "staticDirectory", "serverDirectory", "serverHandler", "createRequestHandler", "build", "use", "next", "<PERSON><PERSON><PERSON><PERSON>", "method", "pathname", "canParseURL", "URL", "stream", "extensions", "forward<PERSON><PERSON>r", "onFile", "error", "routesFile", "fileExistsAsync"], "mappings": ";;;;+BAsBsBA;;;eAAAA;;;;yBAtBe;;;;;;;gEACnB;;;;;;;gEACE;;;;;;;gEACH;;;;;;;gEACA;;;;;;;gEACA;;;;;;6DAEI;qBACiC;wBACzB;wBACa;yBACf;sBACM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjC,MAAMC,QAAQC,QAAQ,SAAS;AAGxB,eAAeF,WAAWG,QAAgB,EAAEC,OAAgB;IACjE,MAAMC,cAAcC,IAAAA,iCAAyB,EAACH;IAE9CI,IAAAA,mBAAU,EAAC;IACXL,QAAQ,aAAaM,IAAI,CAACH;IAE1B,MAAMI,OAAO,MAAMC,IAAAA,sBAAgB,EAACL,aAAa;QAC/CM,aAAaP,QAAQK,IAAI;QACzBG,cAAc;IAChB;IAEA,IAAIH,QAAQ,MAAM;QAChB,MAAM,IAAII,oBAAY,CAAC;IACzB;IACAT,QAAQK,IAAI,GAAGA;IAEf,MAAMK,aAAaV,QAAQW,kBAAkB,GAAGC,eAAI,CAACC,IAAI,CAACd,UAAU,UAAUA;IAC9E,0DAA0D;IAE1D,IAAI,CAAE,MAAMe,IAAAA,yBAAoB,EAACJ,aAAc;QAC7C,MAAM,IAAID,oBAAY,CACpB,CAAC,qBAAqB,EAAEC,WAAW,+CAA+C,CAAC;IAEvF;IAEA,MAAMK,WAAW,MAAMC,oBAAoBN;IAE3CO,KAAIC,GAAG,CAACC,gBAAK,CAACC,GAAG,CAAC,CAAC,SAAS,EAAEL,WAAW,YAAY,GAAG,UAAU,EAAEL,YAAY;IAEhF,IAAIK,UAAU;QACZ,MAAMM,uBAAuBX,YAAYV;IAC3C,OAAO;QACL,MAAMsB,wBAAwBZ,YAAYV;IAC5C;IACAiB,KAAIC,GAAG,CAAC,CAAC,mCAAmC,EAAElB,QAAQK,IAAI,EAAE;AAC5D,8CAA8C;AAChD;AAEA,eAAegB,uBAAuBE,IAAY,EAAEvB,OAAgB;IAClE,MAAMwB,SAASC,gBAAI,CAACC,YAAY,CAAC,CAACC,KAAKC;YAEVD;QAD3B,sCAAsC;QACtC,MAAME,WAAWC,UAAUH,EAAAA,WAAAA,IAAII,GAAG,qBAAPJ,SAASK,KAAK,CAAC,IAAI,CAAC,EAAE,KAAI;QAErDC,IAAAA,eAAI,EAACN,KAAKE,UAAU;YAClBK,MAAMX;YACNY,OAAO;QACT,GACGC,EAAE,CAAC,SAAS,CAACC;YACZ,IAAIA,IAAIC,MAAM,KAAK,KAAK;gBACtBV,IAAIW,UAAU,GAAG;gBACjBX,IAAIY,GAAG,CAAC;gBACR;YACF;YACAZ,IAAIW,UAAU,GAAGF,IAAIC,MAAM,IAAI;YAC/BV,IAAIY,GAAG,CAAC;QACV,GACCC,IAAI,CAACb;IACV;IAEAJ,OAAOkB,MAAM,CAAC1C,QAAQK,IAAI;AAC5B;AAEA,eAAeiB,wBAAwBC,IAAY,EAAEvB,OAAgB;IACnE,MAAM2C,aAAaC,IAAAA,kBAAO;IAE1B,MAAMC,kBAAkBjC,eAAI,CAACC,IAAI,CAACU,MAAM;IACxC,MAAMuB,kBAAkBlC,eAAI,CAACC,IAAI,CAACU,MAAM;IAExC,MAAMwB,gBAAgBC,IAAAA,4BAAoB,EAAC;QAAEC,OAAOH;IAAgB;IAEpE,6BAA6B;IAC7BH,WAAWO,GAAG,CAAC,CAACvB,KAAKC,KAAKuB;QACxB,qEAAqE;QAErE,0DAA0D;QAE1DvB,IAAIwB,SAAS,CAAC,+BAA+B;QAC7CxB,IAAIwB,SAAS,CAAC,gCAAgC;QAC9CxB,IAAIwB,SAAS,CACX,gCACA;QAGF,oCAAoC;QACpC,IAAIzB,IAAI0B,MAAM,KAAK,WAAW;YAC5BzB,IAAIW,UAAU,GAAG;YACjBX,IAAIY,GAAG;YACP;QACF;QACAW;IACF;IAEAR,WAAWO,GAAG,CAAC,CAACvB,KAAKC,KAAKuB;QACxB,IAAI,EAACxB,uBAAAA,IAAKI,GAAG,KAAKJ,IAAI0B,MAAM,KAAK,SAAS1B,IAAI0B,MAAM,KAAK,QAAS;YAChE,OAAOF;QACT;QAEA,MAAMG,WAAWC,YAAY5B,IAAII,GAAG,IAAI,IAAIyB,IAAI7B,IAAII,GAAG,EAAEuB,QAAQ,GAAG3B,IAAII,GAAG;QAC3E,IAAI,CAACuB,UAAU;YACb,OAAOH;QACT;QAEAtD,MAAM,CAAC,mBAAmB,CAAC,EAAEyD;QAE7B,MAAMG,SAASxB,IAAAA,eAAI,EAACN,KAAK2B,UAAU;YACjCpB,MAAMW;YACNa,YAAY;gBAAC;aAAO;QACtB;QAEA,oCAAoC;QACpC,IAAIC,eAAe;QACnBF,OAAOrB,EAAE,CAAC,QAAQ,SAASwB;YACzB,gDAAgD;YAChDD,eAAe;QACjB;QAEA,iBAAiB;QACjBF,OAAOrB,EAAE,CAAC,SAAS,SAASyB,MAAMxB,GAAQ;YACxC,IAAIsB,gBAAgB,CAAEtB,CAAAA,IAAIE,UAAU,GAAG,GAAE,GAAI;gBAC3CY,KAAKd;gBACL;YACF;YAEAc;QACF;QAEA,OAAO;QACPM,OAAOhB,IAAI,CAACb;IACd;IAEAe,WAAWO,GAAG,CAACH;IAEfJ,WAAWD,MAAM,CAAC1C,QAAQK,IAAI;AAChC;AAEA,SAASkD,YAAYxB,GAAW;IAC9B,IAAI;QACF,kCAAkC;QAClC,IAAIyB,IAAIzB;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,eAAef,oBAAoBO,IAAY;IAC7C,MAAMuC,aAAalD,eAAI,CAACC,IAAI,CAACU,MAAM,CAAC,wBAAwB,CAAC;IAC7D,OAAO,CAAE,MAAMwC,IAAAA,oBAAe,EAACD;AACjC"}
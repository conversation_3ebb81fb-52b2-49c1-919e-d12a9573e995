{"version": 3, "sources": ["../../../../../src/start/doctor/apple/SimulatorAppPrerequisite.ts"], "sourcesContent": ["import { execAsync } from '@expo/osascript';\nimport spawnAsync from '@expo/spawn-async';\n\nimport * as Log from '../../../log';\nimport { Prerequisite, PrerequisiteCommandError } from '../Prerequisite';\n\nconst debug = require('debug')('expo:doctor:apple:simulatorApp') as typeof console.log;\n\nasync function getSimulatorAppIdAsync(): Promise<string | null> {\n  try {\n    return (await execAsync('id of app \"Simulator\"')).trim();\n  } catch {\n    // This error may occur in CI where the users intends to install just the simulators but no Xcode.\n  }\n  return null;\n}\n\nexport class SimulatorAppPrerequisite extends Prerequisite {\n  static instance = new SimulatorAppPrerequisite();\n\n  async assertImplementation(): Promise<void> {\n    const result = await getSimulatorAppIdAsync();\n    if (!result) {\n      // This error may occur in CI where the users intends to install just the simulators but no Xcode.\n      throw new PrerequisiteCommandError(\n        'SIMULATOR_APP',\n        \"Can't determine id of Simulator app; the Simulator is most likely not installed on this machine. Run `sudo xcode-select -s /Applications/Xcode.app`\"\n      );\n    }\n    if (\n      result !== 'com.apple.iphonesimulator' &&\n      result !== 'com.apple.CoreSimulator.SimulatorTrampoline'\n    ) {\n      throw new PrerequisiteCommandError(\n        'SIMULATOR_APP',\n        \"Simulator is installed but is identified as '\" + result + \"'; don't know what that is.\"\n      );\n    }\n    debug(`Simulator app id: ${result}`);\n\n    try {\n      // make sure we can run simctl\n      await spawnAsync('xcrun', ['simctl', 'help']);\n    } catch (error: any) {\n      Log.warn(`Unable to run simctl:\\n${error.toString()}`);\n      throw new PrerequisiteCommandError(\n        'SIMCTL',\n        'xcrun is not configured correctly. Ensure `sudo xcode-select --reset` works before running this command again.'\n      );\n    }\n  }\n}\n"], "names": ["SimulatorAppPrerequisite", "debug", "require", "getSimulatorAppIdAsync", "execAsync", "trim", "Prerequisite", "instance", "assertImplementation", "result", "PrerequisiteCommandError", "spawnAsync", "error", "Log", "warn", "toString"], "mappings": ";;;;+BAiBaA;;;eAAAA;;;;yBAjBa;;;;;;;gEACH;;;;;;6DAEF;8BACkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEvD,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,eAAeC;IACb,IAAI;QACF,OAAO,AAAC,CAAA,MAAMC,IAAAA,sBAAS,EAAC,wBAAuB,EAAGC,IAAI;IACxD,EAAE,OAAM;IACN,kGAAkG;IACpG;IACA,OAAO;AACT;AAEO,MAAML,iCAAiCM,0BAAY;qBACjDC,WAAW,IAAIP;IAEtB,MAAMQ,uBAAsC;QAC1C,MAAMC,SAAS,MAAMN;QACrB,IAAI,CAACM,QAAQ;YACX,kGAAkG;YAClG,MAAM,IAAIC,sCAAwB,CAChC,iBACA;QAEJ;QACA,IACED,WAAW,+BACXA,WAAW,+CACX;YACA,MAAM,IAAIC,sCAAwB,CAChC,iBACA,kDAAkDD,SAAS;QAE/D;QACAR,MAAM,CAAC,kBAAkB,EAAEQ,QAAQ;QAEnC,IAAI;YACF,8BAA8B;YAC9B,MAAME,IAAAA,qBAAU,EAAC,SAAS;gBAAC;gBAAU;aAAO;QAC9C,EAAE,OAAOC,OAAY;YACnBC,KAAIC,IAAI,CAAC,CAAC,uBAAuB,EAAEF,MAAMG,QAAQ,IAAI;YACrD,MAAM,IAAIL,sCAAwB,CAChC,UACA;QAEJ;IACF;AACF"}
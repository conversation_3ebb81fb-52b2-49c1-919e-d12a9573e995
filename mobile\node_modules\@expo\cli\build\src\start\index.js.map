{"version": 3, "sources": ["../../../src/start/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport chalk from 'chalk';\n\nimport { Command } from '../../bin/cli';\nimport { assertArgs, getProjectRoot, printHelp } from '../utils/args';\nimport { logCmdError } from '../utils/errors';\n\nexport const expoStart: Command = async (argv) => {\n  const args = assertArgs(\n    {\n      // Types\n      '--help': Boolean,\n      '--clear': Boolean,\n      '--max-workers': Number,\n      '--no-dev': Boolean,\n      '--minify': <PERSON><PERSON><PERSON>,\n      '--https': Boolean,\n      '--private-key-path': String,\n      '--port': Number,\n      '--dev-client': Boolean,\n      '--scheme': String,\n      '--android': Boolean,\n      '--ios': Boolean,\n      '--web': Boolean,\n      '--host': String,\n      '--tunnel': Boolean,\n      '--lan': <PERSON>olean,\n      '--localhost': <PERSON><PERSON><PERSON>,\n      '--offline': <PERSON><PERSON><PERSON>,\n      '--go': <PERSON><PERSON><PERSON>,\n      // Aliases\n      '-h': '--help',\n      '-c': '--clear',\n      '-p': '--port',\n      '-a': '--android',\n      '-i': '--ios',\n      '-w': '--web',\n      '-m': '--host',\n      '-d': '--dev-client',\n      '-g': '--go',\n      // Alias for adding interop with the Metro docs and RedBox errors.\n      '--reset-cache': '--clear',\n    },\n    argv\n  );\n\n  if (args['--help']) {\n    printHelp(\n      `Start a local dev server for the app`,\n      chalk`npx expo start {dim <dir>}`,\n      [\n        chalk`<dir>                           Directory of the Expo project. {dim Default: Current working directory}`,\n        `-a, --android                   Open on a connected Android device`,\n        `-i, --ios                       Open in an iOS simulator`,\n        `-w, --web                       Open in a web browser`,\n        ``,\n        chalk`-d, --dev-client                Launch in a custom native app`,\n        chalk`-g, --go                        Launch in Expo Go`,\n        ``,\n        `-c, --clear                     Clear the bundler cache`,\n        `--max-workers <number>          Maximum number of tasks to allow Metro to spawn`,\n        `--no-dev                        Bundle in production mode`,\n        `--minify                        Minify JavaScript`,\n        ``,\n        chalk`-m, --host <string>             Dev server hosting type. {dim Default: lan}`,\n        chalk`                                {bold lan}: Use the local network`,\n        chalk`                                {bold tunnel}: Use any network by tunnel through ngrok`,\n        chalk`                                {bold localhost}: Connect to the dev server over localhost`,\n        `--tunnel                        Same as --host tunnel`,\n        `--lan                           Same as --host lan`,\n        `--localhost                     Same as --host localhost`,\n        ``,\n        `--offline                       Skip network requests and use anonymous manifest signatures`,\n        chalk`--https                         Start the dev server with https protocol. {bold Deprecated in favor of --tunnel}`,\n        `--scheme <scheme>               Custom URI protocol to use when launching an app`,\n        chalk`-p, --port <number>             Port to start the dev server on (does not apply to web or tunnel). {dim Default: 8081}`,\n        ``,\n        chalk`--private-key-path <path>       Path to private key for code signing. {dim Default: \"private-key.pem\" in the same directory as the certificate specified by the expo-updates configuration in app.json.}`,\n        `-h, --help                      Usage info`,\n      ].join('\\n')\n    );\n  }\n\n  const projectRoot = getProjectRoot(args);\n\n  // NOTE(cedric): `./resolveOptions` loads the expo config when using dev clients, this needs to be initialized before that\n  const { setNodeEnv, loadEnvFiles } = await import('../utils/nodeEnv.js');\n  setNodeEnv(!args['--no-dev'] ? 'development' : 'production');\n  loadEnvFiles(projectRoot);\n\n  const { resolveOptionsAsync } = await import('./resolveOptions.js');\n  const options = await resolveOptionsAsync(projectRoot, args).catch(logCmdError);\n\n  if (options.offline) {\n    const { disableNetwork } = await import('../api/settings.js');\n    disableNetwork();\n  }\n\n  const { startAsync } = await import('./startAsync.js');\n  return startAsync(projectRoot, options, { webOnly: false }).catch(logCmdError);\n};\n"], "names": ["expoStart", "argv", "args", "assertArgs", "Boolean", "Number", "String", "printHelp", "chalk", "join", "projectRoot", "getProjectRoot", "setNodeEnv", "loadEnvFiles", "resolveOptionsAsync", "options", "catch", "logCmdError", "offline", "disableNetwork", "startAsync", "webOnly"], "mappings": ";;;;;+BAOaA;;;eAAAA;;;;gEANK;;;;;;sBAGoC;wBAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMA,YAAqB,OAAOC;IACvC,MAAMC,OAAOC,IAAAA,gBAAU,EACrB;QACE,QAAQ;QACR,UAAUC;QACV,WAAWA;QACX,iBAAiBC;QACjB,YAAYD;QACZ,YAAYA;QACZ,WAAWA;QACX,sBAAsBE;QACtB,UAAUD;QACV,gBAAgBD;QAChB,YAAYE;QACZ,aAAaF;QACb,SAASA;QACT,SAASA;QACT,UAAUE;QACV,YAAYF;QACZ,SAASA;QACT,eAAeA;QACf,aAAaA;QACb,QAAQA;QACR,UAAU;QACV,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,kEAAkE;QAClE,iBAAiB;IACnB,GACAH;IAGF,IAAIC,IAAI,CAAC,SAAS,EAAE;QAClBK,IAAAA,eAAS,EACP,CAAC,oCAAoC,CAAC,EACtCC,IAAAA,gBAAK,CAAA,CAAC,0BAA0B,CAAC,EACjC;YACEA,IAAAA,gBAAK,CAAA,CAAC,uGAAuG,CAAC;YAC9G,CAAC,kEAAkE,CAAC;YACpE,CAAC,wDAAwD,CAAC;YAC1D,CAAC,qDAAqD,CAAC;YACvD,EAAE;YACFA,IAAAA,gBAAK,CAAA,CAAC,6DAA6D,CAAC;YACpEA,IAAAA,gBAAK,CAAA,CAAC,iDAAiD,CAAC;YACxD,EAAE;YACF,CAAC,uDAAuD,CAAC;YACzD,CAAC,+EAA+E,CAAC;YACjF,CAAC,yDAAyD,CAAC;YAC3D,CAAC,iDAAiD,CAAC;YACnD,EAAE;YACFA,IAAAA,gBAAK,CAAA,CAAC,2EAA2E,CAAC;YAClFA,IAAAA,gBAAK,CAAA,CAAC,iEAAiE,CAAC;YACxEA,IAAAA,gBAAK,CAAA,CAAC,sFAAsF,CAAC;YAC7FA,IAAAA,gBAAK,CAAA,CAAC,0FAA0F,CAAC;YACjG,CAAC,qDAAqD,CAAC;YACvD,CAAC,kDAAkD,CAAC;YACpD,CAAC,wDAAwD,CAAC;YAC1D,EAAE;YACF,CAAC,2FAA2F,CAAC;YAC7FA,IAAAA,gBAAK,CAAA,CAAC,gHAAgH,CAAC;YACvH,CAAC,gFAAgF,CAAC;YAClFA,IAAAA,gBAAK,CAAA,CAAC,sHAAsH,CAAC;YAC7H,EAAE;YACFA,IAAAA,gBAAK,CAAA,CAAC,wMAAwM,CAAC;YAC/M,CAAC,0CAA0C,CAAC;SAC7C,CAACC,IAAI,CAAC;IAEX;IAEA,MAAMC,cAAcC,IAAAA,oBAAc,EAACT;IAEnC,0HAA0H;IAC1H,MAAM,EAAEU,UAAU,EAAEC,YAAY,EAAE,GAAG,MAAM,mEAAA,QAAO;IAClDD,WAAW,CAACV,IAAI,CAAC,WAAW,GAAG,gBAAgB;IAC/CW,aAAaH;IAEb,MAAM,EAAEI,mBAAmB,EAAE,GAAG,MAAM,mEAAA,QAAO;IAC7C,MAAMC,UAAU,MAAMD,oBAAoBJ,aAAaR,MAAMc,KAAK,CAACC,mBAAW;IAE9E,IAAIF,QAAQG,OAAO,EAAE;QACnB,MAAM,EAAEC,cAAc,EAAE,GAAG,MAAM,mEAAA,QAAO;QACxCA;IACF;IAEA,MAAM,EAAEC,UAAU,EAAE,GAAG,MAAM,mEAAA,QAAO;IACpC,OAAOA,WAAWV,aAAaK,SAAS;QAAEM,SAAS;IAAM,GAAGL,KAAK,CAACC,mBAAW;AAC/E"}
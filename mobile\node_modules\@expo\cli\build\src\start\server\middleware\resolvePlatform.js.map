{"version": 3, "sources": ["../../../../../src/start/server/middleware/resolvePlatform.ts"], "sourcesContent": ["import { parse } from 'url';\n\nimport { ServerRequest } from './server.types';\nimport { CommandError } from '../../../utils/errors';\n\nconst debug = require('debug')(\n  'expo:start:server:middleware:resolvePlatform'\n) as typeof console.log;\n\n/** Supported platforms */\nexport type RuntimePlatform = 'ios' | 'android';\n\n/**\n * Extract the runtime platform from the server request.\n * 1. Query param `platform`: `?platform=ios`\n * 2. Header `expo-platform`: `'expo-platform': ios`\n * 3. Legacy header `exponent-platform`: `'exponent-platform': ios`\n *\n * Returns first item in the case of an array.\n */\nexport function parsePlatformHeader(req: ServerRequest): string | null {\n  const url = parse(req.url!, /* parseQueryString */ true);\n  const platform =\n    url.query?.platform || req.headers['expo-platform'] || req.headers['exponent-platform'];\n  return (Array.isArray(platform) ? platform[0] : platform) ?? null;\n}\n\n/** Guess the platform from the user-agent header. */\nexport function resolvePlatformFromUserAgentHeader(req: ServerRequest): string | null {\n  let platform = null;\n  const userAgent = req.headers['user-agent'];\n  if (userAgent?.match(/Android/i)) {\n    platform = 'android';\n  }\n  if (userAgent?.match(/iPhone|iPad/i)) {\n    platform = 'ios';\n  }\n  debug(`Resolved platform ${platform} from user-agent header: ${userAgent}`);\n  return platform;\n}\n\n/** Assert if the runtime platform is not included. */\nexport function assertMissingRuntimePlatform(platform?: any): asserts platform {\n  if (!platform) {\n    throw new CommandError(\n      'PLATFORM_HEADER',\n      `Must specify \"expo-platform\" header or \"platform\" query parameter`\n    );\n  }\n}\n\n/** Assert if the runtime platform is not correct. */\nexport function assertRuntimePlatform(platform: string): asserts platform is RuntimePlatform {\n  const stringifiedPlatform = String(platform);\n  if (!['android', 'ios', 'web'].includes(stringifiedPlatform)) {\n    throw new CommandError(\n      'PLATFORM_HEADER',\n      `platform must be \"android\", \"ios\", or \"web\". Received: \"${platform}\"`\n    );\n  }\n}\n"], "names": ["assertMissingRuntimePlatform", "assertRuntimePlatform", "parsePlatformHeader", "resolvePlatformFromUserAgentHeader", "debug", "require", "req", "url", "parse", "platform", "query", "headers", "Array", "isArray", "userAgent", "match", "CommandError", "stringifiedPlatform", "String", "includes"], "mappings": ";;;;;;;;;;;IA0CgBA,4BAA4B;eAA5BA;;IAUAC,qBAAqB;eAArBA;;IAhCAC,mBAAmB;eAAnBA;;IAQAC,kCAAkC;eAAlCA;;;;yBA5BM;;;;;;wBAGO;AAE7B,MAAMC,QAAQC,QAAQ,SACpB;AAcK,SAASH,oBAAoBI,GAAkB;QAGlDC;IAFF,MAAMA,MAAMC,IAAAA,YAAK,EAACF,IAAIC,GAAG,EAAG,oBAAoB,GAAG;IACnD,MAAME,WACJF,EAAAA,aAAAA,IAAIG,KAAK,qBAATH,WAAWE,QAAQ,KAAIH,IAAIK,OAAO,CAAC,gBAAgB,IAAIL,IAAIK,OAAO,CAAC,oBAAoB;IACzF,OAAO,AAACC,CAAAA,MAAMC,OAAO,CAACJ,YAAYA,QAAQ,CAAC,EAAE,GAAGA,QAAO,KAAM;AAC/D;AAGO,SAASN,mCAAmCG,GAAkB;IACnE,IAAIG,WAAW;IACf,MAAMK,YAAYR,IAAIK,OAAO,CAAC,aAAa;IAC3C,IAAIG,6BAAAA,UAAWC,KAAK,CAAC,aAAa;QAChCN,WAAW;IACb;IACA,IAAIK,6BAAAA,UAAWC,KAAK,CAAC,iBAAiB;QACpCN,WAAW;IACb;IACAL,MAAM,CAAC,kBAAkB,EAAEK,SAAS,yBAAyB,EAAEK,WAAW;IAC1E,OAAOL;AACT;AAGO,SAAST,6BAA6BS,QAAc;IACzD,IAAI,CAACA,UAAU;QACb,MAAM,IAAIO,oBAAY,CACpB,mBACA,CAAC,iEAAiE,CAAC;IAEvE;AACF;AAGO,SAASf,sBAAsBQ,QAAgB;IACpD,MAAMQ,sBAAsBC,OAAOT;IACnC,IAAI,CAAC;QAAC;QAAW;QAAO;KAAM,CAACU,QAAQ,CAACF,sBAAsB;QAC5D,MAAM,IAAID,oBAAY,CACpB,mBACA,CAAC,wDAAwD,EAAEP,SAAS,CAAC,CAAC;IAE1E;AACF"}
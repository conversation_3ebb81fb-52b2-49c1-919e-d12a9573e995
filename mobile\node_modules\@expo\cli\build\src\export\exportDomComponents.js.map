{"version": 3, "sources": ["../../../src/export/exportDomComponents.ts"], "sourcesContent": ["import type { ExpoConfig } from '@expo/config';\nimport assert from 'assert';\nimport crypto from 'crypto';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\nimport url from 'url';\n\nimport { type PlatformMetadata } from './createMetadataJson';\nimport { type BundleOutput, type ExportAssetMap, getFilesFromSerialAssets } from './saveAssets';\nimport { type MetroBundlerDevServer } from '../start/server/metro/MetroBundlerDevServer';\nimport { serializeHtmlWithAssets } from '../start/server/metro/serializeHtml';\nimport {\n  getDomComponentHtml,\n  DOM_COMPONENTS_BUNDLE_DIR,\n} from '../start/server/middleware/DomComponentsMiddleware';\nimport { env } from '../utils/env';\nimport { resolveRealEntryFilePath, toPosixPath } from '../utils/filePath';\n\nconst debug = require('debug')('expo:export:exportDomComponents') as typeof console.log;\n\n// TODO(EvanBacon): determine how to support DOM Components with hosting.\nexport async function exportDomComponentAsync({\n  filePath,\n  projectRoot,\n  dev,\n  devServer,\n  isHermes,\n  includeSourceMaps,\n  exp,\n  files,\n  useMd5Filename = false,\n}: {\n  filePath: string;\n  projectRoot: string;\n  dev: boolean;\n  devServer: MetroBundlerDevServer;\n  isHermes: boolean;\n  includeSourceMaps: boolean;\n  exp: ExpoConfig;\n  files: ExportAssetMap;\n  useMd5Filename?: boolean;\n}): Promise<{\n  bundle: BundleOutput;\n  htmlOutputName: string;\n}> {\n  const virtualEntry = toPosixPath(resolveFrom(projectRoot, 'expo/dom/entry.js'));\n  debug('Bundle DOM Component:', filePath);\n  // MUST MATCH THE BABEL PLUGIN!\n  const hash = crypto.createHash('md5').update(filePath).digest('hex');\n  const outputName = `${DOM_COMPONENTS_BUNDLE_DIR}/${hash}.html`;\n  const generatedEntryPath = toPosixPath(\n    filePath.startsWith('file://') ? url.fileURLToPath(filePath) : filePath\n  );\n  const baseUrl = `/${DOM_COMPONENTS_BUNDLE_DIR}`;\n  // The relative import path will be used like URI so it must be POSIX.\n  const relativeImport = './' + path.posix.relative(path.dirname(virtualEntry), generatedEntryPath);\n  // Run metro bundler and create the JS bundles/source maps.\n  const bundle = await devServer.legacySinglePageExportBundleAsync({\n    platform: 'web',\n    domRoot: encodeURI(relativeImport),\n    splitChunks: !env.EXPO_NO_BUNDLE_SPLITTING,\n    mainModuleName: resolveRealEntryFilePath(projectRoot, virtualEntry),\n    mode: dev ? 'development' : 'production',\n    engine: isHermes ? 'hermes' : undefined,\n    serializerIncludeMaps: includeSourceMaps,\n    bytecode: false,\n    reactCompiler: !!exp.experiments?.reactCompiler,\n    baseUrl: './',\n    useMd5Filename,\n    // Minify may be false because it's skipped on native when Hermes is enabled, default to true.\n    minify: true,\n  });\n\n  if (useMd5Filename) {\n    for (const artifact of bundle.artifacts) {\n      const md5 = crypto.createHash('md5').update(artifact.source).digest('hex');\n      artifact.filename = `${md5}.${artifact.type}`;\n    }\n  }\n\n  const html = await serializeHtmlWithAssets({\n    isExporting: true,\n    resources: bundle.artifacts,\n    template: getDomComponentHtml(),\n    baseUrl: './',\n  });\n\n  const serialAssets = bundle.artifacts.map((a) => {\n    return {\n      ...a,\n      filename: path.join(baseUrl, a.filename),\n    };\n  });\n\n  getFilesFromSerialAssets(serialAssets, {\n    includeSourceMaps,\n    files,\n    platform: 'web',\n  });\n\n  files.set(outputName, {\n    contents: html,\n  });\n\n  return {\n    bundle,\n    htmlOutputName: outputName,\n  };\n}\n\n//#region `npx export` for updates\n\n/**\n * Add the DOM component bundle to the metadata for updates.\n */\nexport function addDomBundleToMetadataAsync(bundle: BundleOutput): PlatformMetadata['assets'] {\n  const assetsMetadata: PlatformMetadata['assets'] = [];\n  for (const artifact of bundle.artifacts) {\n    if (artifact.type === 'map') {\n      continue;\n    }\n    assetsMetadata.push({\n      path: `${DOM_COMPONENTS_BUNDLE_DIR}/${artifact.filename}`,\n      ext: artifact.type,\n    });\n  }\n  return assetsMetadata;\n}\n\n/**\n * Transform the DOM component entry (*.html) to use MD5 filename by its content.\n */\nexport function transformDomEntryForMd5Filename({\n  files,\n  htmlOutputName,\n}: {\n  files: ExportAssetMap;\n  htmlOutputName: string;\n}): PlatformMetadata['assets'] {\n  const htmlContent = files.get(htmlOutputName);\n  assert(htmlContent);\n  const htmlMd5 = crypto.createHash('md5').update(htmlContent.contents.toString()).digest('hex');\n  const htmlMd5Filename = `${DOM_COMPONENTS_BUNDLE_DIR}/${htmlMd5}.html`;\n  files.set(htmlMd5Filename, htmlContent);\n  files.delete(htmlOutputName);\n  return [\n    {\n      path: htmlMd5Filename,\n      ext: 'html',\n    },\n  ];\n}\n\n/**\n * Post-transform the native bundle to use MD5 filename based on DOM component entry content.\n */\nexport function transformNativeBundleForMd5Filename({\n  domComponentReference,\n  nativeBundle,\n  files,\n  htmlOutputName,\n}: {\n  domComponentReference: string;\n  nativeBundle: BundleOutput;\n  files: ExportAssetMap;\n  htmlOutputName: string;\n}) {\n  const htmlContent = files.get(htmlOutputName);\n  assert(htmlContent);\n  const htmlMd5 = crypto.createHash('md5').update(htmlContent.contents.toString()).digest('hex');\n  const hash = crypto.createHash('md5').update(domComponentReference).digest('hex');\n  for (const artifact of nativeBundle.artifacts) {\n    if (artifact.type !== 'js') {\n      continue;\n    }\n    const assetEntity = files.get(artifact.filename);\n    assert(assetEntity);\n    if (Buffer.isBuffer(assetEntity.contents)) {\n      const searchBuffer = Buffer.from(`${hash}.html`, 'utf8');\n      const replaceBuffer = Buffer.from(`${htmlMd5}.html`, 'utf8');\n      assert(searchBuffer.length === replaceBuffer.length);\n      let index = assetEntity.contents.indexOf(searchBuffer, 0);\n      while (index !== -1) {\n        replaceBuffer.copy(assetEntity.contents, index);\n        index = assetEntity.contents.indexOf(searchBuffer, index + searchBuffer.length);\n      }\n    } else {\n      const search = `${hash}.html`;\n      const replace = `${htmlMd5}.html`;\n      assert(search.length === replace.length);\n      assetEntity.contents = assetEntity.contents.toString().replaceAll(search, replace);\n    }\n  }\n}\n\n//#endregion `npx export` for updates\n"], "names": ["addDomBundleToMetadataAsync", "exportDomComponentAsync", "transformDomEntryForMd5Filename", "transformNativeBundleForMd5Filename", "debug", "require", "filePath", "projectRoot", "dev", "devServer", "isHermes", "includeSourceMaps", "exp", "files", "useMd5Filename", "virtualEntry", "toPosixPath", "resolveFrom", "hash", "crypto", "createHash", "update", "digest", "outputName", "DOM_COMPONENTS_BUNDLE_DIR", "generatedEntryPath", "startsWith", "url", "fileURLToPath", "baseUrl", "relativeImport", "path", "posix", "relative", "dirname", "bundle", "legacySinglePageExportBundleAsync", "platform", "domRoot", "encodeURI", "splitChunks", "env", "EXPO_NO_BUNDLE_SPLITTING", "mainModuleName", "resolveRealEntryFilePath", "mode", "engine", "undefined", "serializerIncludeMaps", "bytecode", "reactCompiler", "experiments", "minify", "artifact", "artifacts", "md5", "source", "filename", "type", "html", "serializeHtmlWithAssets", "isExporting", "resources", "template", "getDomComponentHtml", "serialAssets", "map", "a", "join", "getFilesFromSerialAssets", "set", "contents", "htmlOutputName", "assetsMetadata", "push", "ext", "htmlContent", "get", "assert", "htmlMd5", "toString", "htmlMd5Filename", "delete", "domComponentReference", "nativeBundle", "assetEntity", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "searchBuffer", "from", "<PERSON><PERSON><PERSON><PERSON>", "length", "index", "indexOf", "copy", "search", "replace", "replaceAll"], "mappings": ";;;;;;;;;;;IAmHgBA,2BAA2B;eAA3BA;;IA9FMC,uBAAuB;eAAvBA;;IA+GNC,+BAA+B;eAA/BA;;IAwBAC,mCAAmC;eAAnCA;;;;gEA3JG;;;;;;;gEACA;;;;;;;gEACF;;;;;;;gEACO;;;;;;;gEACR;;;;;;4BAGiE;+BAEzC;yCAIjC;qBACa;0BACkC;;;;;;AAEtD,MAAMC,QAAQC,QAAQ,SAAS;AAGxB,eAAeJ,wBAAwB,EAC5CK,QAAQ,EACRC,WAAW,EACXC,GAAG,EACHC,SAAS,EACTC,QAAQ,EACRC,iBAAiB,EACjBC,GAAG,EACHC,KAAK,EACLC,iBAAiB,KAAK,EAWvB;QAyBoBF;IArBnB,MAAMG,eAAeC,IAAAA,qBAAW,EAACC,IAAAA,sBAAW,EAACV,aAAa;IAC1DH,MAAM,yBAAyBE;IAC/B,+BAA+B;IAC/B,MAAMY,OAAOC,iBAAM,CAACC,UAAU,CAAC,OAAOC,MAAM,CAACf,UAAUgB,MAAM,CAAC;IAC9D,MAAMC,aAAa,GAAGC,kDAAyB,CAAC,CAAC,EAAEN,KAAK,KAAK,CAAC;IAC9D,MAAMO,qBAAqBT,IAAAA,qBAAW,EACpCV,SAASoB,UAAU,CAAC,aAAaC,cAAG,CAACC,aAAa,CAACtB,YAAYA;IAEjE,MAAMuB,UAAU,CAAC,CAAC,EAAEL,kDAAyB,EAAE;IAC/C,sEAAsE;IACtE,MAAMM,iBAAiB,OAAOC,eAAI,CAACC,KAAK,CAACC,QAAQ,CAACF,eAAI,CAACG,OAAO,CAACnB,eAAeU;IAC9E,2DAA2D;IAC3D,MAAMU,SAAS,MAAM1B,UAAU2B,iCAAiC,CAAC;QAC/DC,UAAU;QACVC,SAASC,UAAUT;QACnBU,aAAa,CAACC,QAAG,CAACC,wBAAwB;QAC1CC,gBAAgBC,IAAAA,kCAAwB,EAACrC,aAAaQ;QACtD8B,MAAMrC,MAAM,gBAAgB;QAC5BsC,QAAQpC,WAAW,WAAWqC;QAC9BC,uBAAuBrC;QACvBsC,UAAU;QACVC,eAAe,CAAC,GAACtC,mBAAAA,IAAIuC,WAAW,qBAAfvC,iBAAiBsC,aAAa;QAC/CrB,SAAS;QACTf;QACA,8FAA8F;QAC9FsC,QAAQ;IACV;IAEA,IAAItC,gBAAgB;QAClB,KAAK,MAAMuC,YAAYlB,OAAOmB,SAAS,CAAE;YACvC,MAAMC,MAAMpC,iBAAM,CAACC,UAAU,CAAC,OAAOC,MAAM,CAACgC,SAASG,MAAM,EAAElC,MAAM,CAAC;YACpE+B,SAASI,QAAQ,GAAG,GAAGF,IAAI,CAAC,EAAEF,SAASK,IAAI,EAAE;QAC/C;IACF;IAEA,MAAMC,OAAO,MAAMC,IAAAA,sCAAuB,EAAC;QACzCC,aAAa;QACbC,WAAW3B,OAAOmB,SAAS;QAC3BS,UAAUC,IAAAA,4CAAmB;QAC7BnC,SAAS;IACX;IAEA,MAAMoC,eAAe9B,OAAOmB,SAAS,CAACY,GAAG,CAAC,CAACC;QACzC,OAAO;YACL,GAAGA,CAAC;YACJV,UAAU1B,eAAI,CAACqC,IAAI,CAACvC,SAASsC,EAAEV,QAAQ;QACzC;IACF;IAEAY,IAAAA,oCAAwB,EAACJ,cAAc;QACrCtD;QACAE;QACAwB,UAAU;IACZ;IAEAxB,MAAMyD,GAAG,CAAC/C,YAAY;QACpBgD,UAAUZ;IACZ;IAEA,OAAO;QACLxB;QACAqC,gBAAgBjD;IAClB;AACF;AAOO,SAASvB,4BAA4BmC,MAAoB;IAC9D,MAAMsC,iBAA6C,EAAE;IACrD,KAAK,MAAMpB,YAAYlB,OAAOmB,SAAS,CAAE;QACvC,IAAID,SAASK,IAAI,KAAK,OAAO;YAC3B;QACF;QACAe,eAAeC,IAAI,CAAC;YAClB3C,MAAM,GAAGP,kDAAyB,CAAC,CAAC,EAAE6B,SAASI,QAAQ,EAAE;YACzDkB,KAAKtB,SAASK,IAAI;QACpB;IACF;IACA,OAAOe;AACT;AAKO,SAASvE,gCAAgC,EAC9CW,KAAK,EACL2D,cAAc,EAIf;IACC,MAAMI,cAAc/D,MAAMgE,GAAG,CAACL;IAC9BM,IAAAA,iBAAM,EAACF;IACP,MAAMG,UAAU5D,iBAAM,CAACC,UAAU,CAAC,OAAOC,MAAM,CAACuD,YAAYL,QAAQ,CAACS,QAAQ,IAAI1D,MAAM,CAAC;IACxF,MAAM2D,kBAAkB,GAAGzD,kDAAyB,CAAC,CAAC,EAAEuD,QAAQ,KAAK,CAAC;IACtElE,MAAMyD,GAAG,CAACW,iBAAiBL;IAC3B/D,MAAMqE,MAAM,CAACV;IACb,OAAO;QACL;YACEzC,MAAMkD;YACNN,KAAK;QACP;KACD;AACH;AAKO,SAASxE,oCAAoC,EAClDgF,qBAAqB,EACrBC,YAAY,EACZvE,KAAK,EACL2D,cAAc,EAMf;IACC,MAAMI,cAAc/D,MAAMgE,GAAG,CAACL;IAC9BM,IAAAA,iBAAM,EAACF;IACP,MAAMG,UAAU5D,iBAAM,CAACC,UAAU,CAAC,OAAOC,MAAM,CAACuD,YAAYL,QAAQ,CAACS,QAAQ,IAAI1D,MAAM,CAAC;IACxF,MAAMJ,OAAOC,iBAAM,CAACC,UAAU,CAAC,OAAOC,MAAM,CAAC8D,uBAAuB7D,MAAM,CAAC;IAC3E,KAAK,MAAM+B,YAAY+B,aAAa9B,SAAS,CAAE;QAC7C,IAAID,SAASK,IAAI,KAAK,MAAM;YAC1B;QACF;QACA,MAAM2B,cAAcxE,MAAMgE,GAAG,CAACxB,SAASI,QAAQ;QAC/CqB,IAAAA,iBAAM,EAACO;QACP,IAAIC,OAAOC,QAAQ,CAACF,YAAYd,QAAQ,GAAG;YACzC,MAAMiB,eAAeF,OAAOG,IAAI,CAAC,GAAGvE,KAAK,KAAK,CAAC,EAAE;YACjD,MAAMwE,gBAAgBJ,OAAOG,IAAI,CAAC,GAAGV,QAAQ,KAAK,CAAC,EAAE;YACrDD,IAAAA,iBAAM,EAACU,aAAaG,MAAM,KAAKD,cAAcC,MAAM;YACnD,IAAIC,QAAQP,YAAYd,QAAQ,CAACsB,OAAO,CAACL,cAAc;YACvD,MAAOI,UAAU,CAAC,EAAG;gBACnBF,cAAcI,IAAI,CAACT,YAAYd,QAAQ,EAAEqB;gBACzCA,QAAQP,YAAYd,QAAQ,CAACsB,OAAO,CAACL,cAAcI,QAAQJ,aAAaG,MAAM;YAChF;QACF,OAAO;YACL,MAAMI,SAAS,GAAG7E,KAAK,KAAK,CAAC;YAC7B,MAAM8E,UAAU,GAAGjB,QAAQ,KAAK,CAAC;YACjCD,IAAAA,iBAAM,EAACiB,OAAOJ,MAAM,KAAKK,QAAQL,MAAM;YACvCN,YAAYd,QAAQ,GAAGc,YAAYd,QAAQ,CAACS,QAAQ,GAAGiB,UAAU,CAACF,QAAQC;QAC5E;IACF;AACF,EAEA,qCAAqC"}
{"version": 3, "sources": ["../../../../src/start/server/platformBundlers.ts"], "sourcesContent": ["import { ExpoConfig, Platform } from '@expo/config';\nimport resolveFrom from 'resolve-from';\n\n/** Which bundler each platform should use. */\nexport type PlatformBundlers = Record<Platform, 'metro' | 'webpack'>;\n\n/** Get the platform bundlers mapping. */\nexport function getPlatformBundlers(\n  projectRoot: string,\n  exp: Partial<ExpoConfig>\n): PlatformBundlers {\n  /**\n   * SDK 50+: The web bundler is dynamic based upon the presence of the `@expo/webpack-config` package.\n   */\n  let web = exp.web?.bundler;\n  if (!web) {\n    const resolved = resolveFrom.silent(projectRoot, '@expo/webpack-config/package.json');\n    web = resolved ? 'webpack' : 'metro';\n  }\n\n  return {\n    // @ts-expect-error: not on type yet\n    ios: exp.ios?.bundler ?? 'metro',\n    // @ts-expect-error: not on type yet\n    android: exp.android?.bundler ?? 'metro',\n    web,\n  };\n}\n"], "names": ["getPlatformBundlers", "projectRoot", "exp", "web", "bundler", "resolved", "resolveFrom", "silent", "ios", "android"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;;gEANQ;;;;;;;;;;;AAMjB,SAASA,oBACdC,WAAmB,EACnBC,GAAwB;QAKdA,UAQHA,UAEIA;IAbX;;GAEC,GACD,IAAIC,OAAMD,WAAAA,IAAIC,GAAG,qBAAPD,SAASE,OAAO;IAC1B,IAAI,CAACD,KAAK;QACR,MAAME,WAAWC,sBAAW,CAACC,MAAM,CAACN,aAAa;QACjDE,MAAME,WAAW,YAAY;IAC/B;IAEA,OAAO;QACL,oCAAoC;QACpCG,KAAKN,EAAAA,WAAAA,IAAIM,GAAG,qBAAPN,SAASE,OAAO,KAAI;QACzB,oCAAoC;QACpCK,SAASP,EAAAA,eAAAA,IAAIO,OAAO,qBAAXP,aAAaE,OAAO,KAAI;QACjCD;IACF;AACF"}
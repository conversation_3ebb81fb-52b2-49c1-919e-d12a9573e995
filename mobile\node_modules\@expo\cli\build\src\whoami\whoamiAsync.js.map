{"version": 3, "sources": ["../../../src/whoami/whoamiAsync.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport { getActorDisplayName, getUserAsync } from '../api/user/user';\nimport * as Log from '../log';\n\nexport async function whoamiAsync() {\n  const user = await getUserAsync();\n  if (user) {\n    Log.exit(chalk.green(getActorDisplayName(user)), 0);\n  } else {\n    Log.exit('Not logged in');\n  }\n}\n"], "names": ["<PERSON>ami<PERSON><PERSON>", "user", "getUserAsync", "Log", "exit", "chalk", "green", "getActorDisplayName"], "mappings": ";;;;+BAKsBA;;;eAAAA;;;;gEALJ;;;;;;sBAEgC;6DAC7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEd,eAAeA;IACpB,MAAMC,OAAO,MAAMC,IAAAA,kBAAY;IAC/B,IAAID,MAAM;QACRE,KAAIC,IAAI,CAACC,gBAAK,CAACC,KAAK,CAACC,IAAAA,yBAAmB,EAACN,QAAQ;IACnD,OAAO;QACLE,KAAIC,IAAI,CAAC;IACX;AACF"}
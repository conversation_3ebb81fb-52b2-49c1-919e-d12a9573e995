{"version": 3, "sources": ["../../../src/customize/templates.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport prompt, { ExpoChoice } from '../utils/prompts';\n\nconst debug = require('debug')('expo:customize:templates');\n\nexport type DestinationResolutionProps = {\n  /** Web 'public' folder path (defaults to `/web`). This technically can be changed but shouldn't be. */\n  webStaticPath: string;\n  /** The Expo Router app directory. */\n  appDirPath: string;\n};\n\nfunction importFromExpoWebpackConfig(projectRoot: string, folder: string, moduleId: string) {\n  try {\n    const filePath = resolveFrom(projectRoot, `@expo/webpack-config/${folder}/${moduleId}`);\n    debug(`Using @expo/webpack-config template for \"${moduleId}\": ${filePath}`);\n    return filePath;\n  } catch {\n    debug(`@expo/webpack-config template for \"${moduleId}\" not found, falling back on @expo/cli`);\n  }\n  return importFromVendor(projectRoot, moduleId);\n}\n\nfunction importFromVendor(projectRoot: string, moduleId: string) {\n  try {\n    const filePath = resolveFrom(projectRoot, '@expo/cli/static/template/' + moduleId);\n    debug(`Using @expo/cli template for \"${moduleId}\": ${filePath}`);\n    return filePath;\n  } catch {\n    // For dev mode, testing and other cases where @expo/cli is not installed\n    const filePath = require.resolve(`@expo/cli/static/template/${moduleId}`);\n    debug(\n      `Local @expo/cli template for \"${moduleId}\" not found, falling back on template relative to @expo/cli: ${filePath}`\n    );\n\n    return filePath;\n  }\n}\n\nexport const TEMPLATES: {\n  /** Unique ID for easily indexing. */\n  id: string;\n  /** Template file path to copy into the project. */\n  file: (projectRoot: string) => string;\n  /** Output location for the file in the user project. */\n  destination: (props: DestinationResolutionProps) => string;\n  /** List of dependencies to install in the project. These are used inside of the template file. */\n  dependencies: string[];\n\n  /** Custom step for configuring the file. Return true to exit early. */\n  configureAsync?: (projectRoot: string) => Promise<boolean>;\n}[] = [\n  {\n    id: 'babel.config.js',\n    file: (projectRoot) => importFromVendor(projectRoot, 'babel.config.js'),\n    destination: () => 'babel.config.js',\n    dependencies: [\n      // Even though this is installed in `expo`, we should add it for now.\n      'babel-preset-expo',\n    ],\n  },\n  {\n    id: 'metro.config.js',\n    dependencies: ['@expo/metro-config'],\n    destination: () => 'metro.config.js',\n    file: (projectRoot) => importFromVendor(projectRoot, 'metro.config.js'),\n  },\n  {\n    // `tsconfig.json` is special-cased and doesn't follow the template.\n    id: 'tsconfig.json',\n    dependencies: [],\n    destination: () => 'tsconfig.json',\n    file: () => '',\n    configureAsync: async (projectRoot) => {\n      const { typescript } = require('./typescript') as typeof import('./typescript');\n      await typescript(projectRoot);\n      return true;\n    },\n  },\n  {\n    id: '.eslintrc.js',\n    dependencies: [],\n    destination: () => '.eslintrc.js (deprecated)',\n    file: (projectRoot) => importFromVendor(projectRoot, '.eslintrc.js'),\n    configureAsync: async (projectRoot) => {\n      const { ESLintProjectPrerequisite } =\n        require('../lint/ESlintPrerequisite') as typeof import('../lint/ESlintPrerequisite.js');\n      const prerequisite = new ESLintProjectPrerequisite(projectRoot);\n      if (!(await prerequisite.assertAsync())) {\n        await prerequisite.bootstrapAsync();\n      }\n      return false;\n    },\n  },\n  {\n    id: 'eslint.config.js',\n    dependencies: [],\n    destination: () => 'eslint.config.js',\n    file: (projectRoot) => importFromVendor(projectRoot, 'eslint.config.js'),\n    configureAsync: async (projectRoot) => {\n      const { ESLintProjectPrerequisite } =\n        require('../lint/ESlintPrerequisite') as typeof import('../lint/ESlintPrerequisite.js');\n      const prerequisite = new ESLintProjectPrerequisite(projectRoot);\n      if (!(await prerequisite.assertAsync())) {\n        await prerequisite.bootstrapAsync();\n      }\n      return false;\n    },\n  },\n  {\n    id: 'index.html',\n    file: (projectRoot) => importFromExpoWebpackConfig(projectRoot, 'web-default', 'index.html'),\n    // web/index.html\n    destination: ({ webStaticPath }) => webStaticPath + '/index.html',\n    dependencies: [],\n  },\n  {\n    id: 'webpack.config.js',\n    file: (projectRoot) =>\n      importFromExpoWebpackConfig(projectRoot, 'template', 'webpack.config.js'),\n    destination: () => 'webpack.config.js',\n    dependencies: ['@expo/webpack-config'],\n  },\n  {\n    id: '+html.tsx',\n    file: (projectRoot) => importFromVendor(projectRoot, '+html.tsx'),\n    destination: ({ appDirPath }) => path.join(appDirPath, '+html.tsx'),\n    dependencies: [],\n  },\n  {\n    id: '+native-intent.ts',\n    file: (projectRoot) => importFromVendor(projectRoot, '+native-intent.ts'),\n    destination: ({ appDirPath }) => path.join(appDirPath, '+native-intent.ts'),\n    dependencies: [],\n  },\n];\n\n/** Generate the prompt choices. */\nfunction createChoices(\n  projectRoot: string,\n  props: DestinationResolutionProps\n): ExpoChoice<number>[] {\n  return TEMPLATES.map((template, index) => {\n    const destination = template.destination(props);\n    const localProjectFile = path.resolve(projectRoot, destination);\n    const exists = fs.existsSync(localProjectFile);\n\n    return {\n      title: destination,\n      value: index,\n      description: exists ? chalk.red('This will overwrite the existing file') : undefined,\n    };\n  });\n}\n\n/** Prompt to select templates to add. */\nexport async function selectTemplatesAsync(projectRoot: string, props: DestinationResolutionProps) {\n  const options = createChoices(projectRoot, props);\n\n  const { answer } = await prompt({\n    type: 'multiselect',\n    name: 'answer',\n    message: 'Which files would you like to generate?',\n    hint: '- Space to select. Return to submit',\n    warn: 'File already exists.',\n    limit: options.length,\n    instructions: '',\n    choices: options,\n  });\n  return answer;\n}\n"], "names": ["TEMPLATES", "selectTemplatesAsync", "debug", "require", "importFromExpoWebpackConfig", "projectRoot", "folder", "moduleId", "filePath", "resolveFrom", "importFromVendor", "resolve", "id", "file", "destination", "dependencies", "configure<PERSON><PERSON>", "typescript", "ESLintProjectPrerequisite", "prerequisite", "assertAsync", "bootstrapAsync", "webStaticPath", "appDirPath", "path", "join", "createChoices", "props", "map", "template", "index", "localProjectFile", "exists", "fs", "existsSync", "title", "value", "description", "chalk", "red", "undefined", "options", "answer", "prompt", "type", "name", "message", "hint", "warn", "limit", "length", "instructions", "choices"], "mappings": ";;;;;;;;;;;IA2CaA,SAAS;eAATA;;IAqHSC,oBAAoB;eAApBA;;;;gEAhKJ;;;;;;;gEACH;;;;;;;gEACE;;;;;;;gEACO;;;;;;gEAEW;;;;;;AAEnC,MAAMC,QAAQC,QAAQ,SAAS;AAS/B,SAASC,4BAA4BC,WAAmB,EAAEC,MAAc,EAAEC,QAAgB;IACxF,IAAI;QACF,MAAMC,WAAWC,IAAAA,sBAAW,EAACJ,aAAa,CAAC,qBAAqB,EAAEC,OAAO,CAAC,EAAEC,UAAU;QACtFL,MAAM,CAAC,yCAAyC,EAAEK,SAAS,GAAG,EAAEC,UAAU;QAC1E,OAAOA;IACT,EAAE,OAAM;QACNN,MAAM,CAAC,mCAAmC,EAAEK,SAAS,sCAAsC,CAAC;IAC9F;IACA,OAAOG,iBAAiBL,aAAaE;AACvC;AAEA,SAASG,iBAAiBL,WAAmB,EAAEE,QAAgB;IAC7D,IAAI;QACF,MAAMC,WAAWC,IAAAA,sBAAW,EAACJ,aAAa,+BAA+BE;QACzEL,MAAM,CAAC,8BAA8B,EAAEK,SAAS,GAAG,EAAEC,UAAU;QAC/D,OAAOA;IACT,EAAE,OAAM;QACN,yEAAyE;QACzE,MAAMA,WAAWL,QAAQQ,OAAO,CAAC,CAAC,0BAA0B,EAAEJ,UAAU;QACxEL,MACE,CAAC,8BAA8B,EAAEK,SAAS,6DAA6D,EAAEC,UAAU;QAGrH,OAAOA;IACT;AACF;AAEO,MAAMR,YAYP;IACJ;QACEY,IAAI;QACJC,MAAM,CAACR,cAAgBK,iBAAiBL,aAAa;QACrDS,aAAa,IAAM;QACnBC,cAAc;YACZ,qEAAqE;YACrE;SACD;IACH;IACA;QACEH,IAAI;QACJG,cAAc;YAAC;SAAqB;QACpCD,aAAa,IAAM;QACnBD,MAAM,CAACR,cAAgBK,iBAAiBL,aAAa;IACvD;IACA;QACE,oEAAoE;QACpEO,IAAI;QACJG,cAAc,EAAE;QAChBD,aAAa,IAAM;QACnBD,MAAM,IAAM;QACZG,gBAAgB,OAAOX;YACrB,MAAM,EAAEY,UAAU,EAAE,GAAGd,QAAQ;YAC/B,MAAMc,WAAWZ;YACjB,OAAO;QACT;IACF;IACA;QACEO,IAAI;QACJG,cAAc,EAAE;QAChBD,aAAa,IAAM;QACnBD,MAAM,CAACR,cAAgBK,iBAAiBL,aAAa;QACrDW,gBAAgB,OAAOX;YACrB,MAAM,EAAEa,yBAAyB,EAAE,GACjCf,QAAQ;YACV,MAAMgB,eAAe,IAAID,0BAA0Bb;YACnD,IAAI,CAAE,MAAMc,aAAaC,WAAW,IAAK;gBACvC,MAAMD,aAAaE,cAAc;YACnC;YACA,OAAO;QACT;IACF;IACA;QACET,IAAI;QACJG,cAAc,EAAE;QAChBD,aAAa,IAAM;QACnBD,MAAM,CAACR,cAAgBK,iBAAiBL,aAAa;QACrDW,gBAAgB,OAAOX;YACrB,MAAM,EAAEa,yBAAyB,EAAE,GACjCf,QAAQ;YACV,MAAMgB,eAAe,IAAID,0BAA0Bb;YACnD,IAAI,CAAE,MAAMc,aAAaC,WAAW,IAAK;gBACvC,MAAMD,aAAaE,cAAc;YACnC;YACA,OAAO;QACT;IACF;IACA;QACET,IAAI;QACJC,MAAM,CAACR,cAAgBD,4BAA4BC,aAAa,eAAe;QAC/E,iBAAiB;QACjBS,aAAa,CAAC,EAAEQ,aAAa,EAAE,GAAKA,gBAAgB;QACpDP,cAAc,EAAE;IAClB;IACA;QACEH,IAAI;QACJC,MAAM,CAACR,cACLD,4BAA4BC,aAAa,YAAY;QACvDS,aAAa,IAAM;QACnBC,cAAc;YAAC;SAAuB;IACxC;IACA;QACEH,IAAI;QACJC,MAAM,CAACR,cAAgBK,iBAAiBL,aAAa;QACrDS,aAAa,CAAC,EAAES,UAAU,EAAE,GAAKC,eAAI,CAACC,IAAI,CAACF,YAAY;QACvDR,cAAc,EAAE;IAClB;IACA;QACEH,IAAI;QACJC,MAAM,CAACR,cAAgBK,iBAAiBL,aAAa;QACrDS,aAAa,CAAC,EAAES,UAAU,EAAE,GAAKC,eAAI,CAACC,IAAI,CAACF,YAAY;QACvDR,cAAc,EAAE;IAClB;CACD;AAED,iCAAiC,GACjC,SAASW,cACPrB,WAAmB,EACnBsB,KAAiC;IAEjC,OAAO3B,UAAU4B,GAAG,CAAC,CAACC,UAAUC;QAC9B,MAAMhB,cAAce,SAASf,WAAW,CAACa;QACzC,MAAMI,mBAAmBP,eAAI,CAACb,OAAO,CAACN,aAAaS;QACnD,MAAMkB,SAASC,aAAE,CAACC,UAAU,CAACH;QAE7B,OAAO;YACLI,OAAOrB;YACPsB,OAAON;YACPO,aAAaL,SAASM,gBAAK,CAACC,GAAG,CAAC,2CAA2CC;QAC7E;IACF;AACF;AAGO,eAAevC,qBAAqBI,WAAmB,EAAEsB,KAAiC;IAC/F,MAAMc,UAAUf,cAAcrB,aAAasB;IAE3C,MAAM,EAAEe,MAAM,EAAE,GAAG,MAAMC,IAAAA,gBAAM,EAAC;QAC9BC,MAAM;QACNC,MAAM;QACNC,SAAS;QACTC,MAAM;QACNC,MAAM;QACNC,OAAOR,QAAQS,MAAM;QACrBC,cAAc;QACdC,SAASX;IACX;IACA,OAAOC;AACT"}
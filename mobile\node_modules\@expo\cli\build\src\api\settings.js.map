{"version": 3, "sources": ["../../../src/api/settings.ts"], "sourcesContent": ["// This file represents temporary globals for the CLI when using the API.\n// Settings should be as minimal as possible since they are globals.\nimport chalk from 'chalk';\n\nimport { Log } from '../log';\nimport { env } from '../utils/env';\n\nexport function disableNetwork() {\n  if (env.EXPO_OFFLINE) return;\n  process.env.EXPO_OFFLINE = '1';\n  Log.log(chalk.gray('Networking has been disabled'));\n}\n"], "names": ["disableNetwork", "env", "EXPO_OFFLINE", "process", "Log", "log", "chalk", "gray"], "mappings": "AAAA,yEAAyE;AACzE,oEAAoE;;;;;+BAMpDA;;;eAAAA;;;;gEALE;;;;;;qBAEE;qBACA;;;;;;AAEb,SAASA;IACd,IAAIC,QAAG,CAACC,YAAY,EAAE;IACtBC,QAAQF,GAAG,CAACC,YAAY,GAAG;IAC3BE,QAAG,CAACC,GAAG,CAACC,gBAAK,CAACC,IAAI,CAAC;AACrB"}
{"version": 3, "sources": ["../../../../../src/start/platforms/ios/AppleAppIdResolver.ts"], "sourcesContent": ["import { IOSConfig } from '@expo/config-plugins';\nimport plist from '@expo/plist';\nimport fs from 'fs';\n\nimport { AppIdResolver } from '../AppIdResolver';\n\nconst debug = require('debug')('expo:start:platforms:ios:AppleAppIdResolver') as typeof console.log;\n\n/** Resolves the iOS bundle identifier from the Expo config or native files. */\nexport class AppleAppIdResolver extends AppIdResolver {\n  constructor(projectRoot: string) {\n    super(projectRoot, 'ios', 'ios.bundleIdentifier');\n  }\n\n  /** @return `true` if the app has valid `*.pbxproj` file */\n  async hasNativeProjectAsync(): Promise<boolean> {\n    try {\n      // Never returns nullish values.\n      return !!IOSConfig.Paths.getAllPBXProjectPaths(this.projectRoot).length;\n    } catch (error: any) {\n      debug('Expected error checking for native project:', error.message);\n      return false;\n    }\n  }\n\n  async resolveAppIdFromNativeAsync(): Promise<string | null> {\n    // Check xcode project\n    try {\n      const bundleId = IOSConfig.BundleIdentifier.getBundleIdentifierFromPbxproj(this.projectRoot);\n      if (bundleId) {\n        return bundleId;\n      }\n    } catch (error: any) {\n      debug('Expected error resolving the bundle identifier from the pbxproj:', error);\n    }\n\n    // Check Info.plist\n    try {\n      const infoPlistPath = IOSConfig.Paths.getInfoPlistPath(this.projectRoot);\n      const data = await plist.parse(fs.readFileSync(infoPlistPath, 'utf8'));\n      if (data.CFBundleIdentifier && !data.CFBundleIdentifier.startsWith('$(')) {\n        return data.CFBundleIdentifier;\n      }\n    } catch (error) {\n      debug('Expected error resolving the bundle identifier from the project Info.plist:', error);\n    }\n\n    return null;\n  }\n}\n"], "names": ["AppleAppIdResolver", "debug", "require", "AppIdResolver", "constructor", "projectRoot", "hasNativeProjectAsync", "IOSConfig", "Paths", "getAllPBXProjectPaths", "length", "error", "message", "resolveAppIdFromNativeAsync", "bundleId", "BundleIdentifier", "getBundleIdentifierFromPbxproj", "infoPlistPath", "getInfoPlistPath", "data", "plist", "parse", "fs", "readFileSync", "CFBundleIdentifier", "startsWith"], "mappings": ";;;;+BASaA;;;eAAAA;;;;yBATa;;;;;;;gEACR;;;;;;;gEACH;;;;;;+BAEe;;;;;;AAE9B,MAAMC,QAAQC,QAAQ,SAAS;AAGxB,MAAMF,2BAA2BG,4BAAa;IACnDC,YAAYC,WAAmB,CAAE;QAC/B,KAAK,CAACA,aAAa,OAAO;IAC5B;IAEA,yDAAyD,GACzD,MAAMC,wBAA0C;QAC9C,IAAI;YACF,gCAAgC;YAChC,OAAO,CAAC,CAACC,0BAAS,CAACC,KAAK,CAACC,qBAAqB,CAAC,IAAI,CAACJ,WAAW,EAAEK,MAAM;QACzE,EAAE,OAAOC,OAAY;YACnBV,MAAM,+CAA+CU,MAAMC,OAAO;YAClE,OAAO;QACT;IACF;IAEA,MAAMC,8BAAsD;QAC1D,sBAAsB;QACtB,IAAI;YACF,MAAMC,WAAWP,0BAAS,CAACQ,gBAAgB,CAACC,8BAA8B,CAAC,IAAI,CAACX,WAAW;YAC3F,IAAIS,UAAU;gBACZ,OAAOA;YACT;QACF,EAAE,OAAOH,OAAY;YACnBV,MAAM,oEAAoEU;QAC5E;QAEA,mBAAmB;QACnB,IAAI;YACF,MAAMM,gBAAgBV,0BAAS,CAACC,KAAK,CAACU,gBAAgB,CAAC,IAAI,CAACb,WAAW;YACvE,MAAMc,OAAO,MAAMC,gBAAK,CAACC,KAAK,CAACC,aAAE,CAACC,YAAY,CAACN,eAAe;YAC9D,IAAIE,KAAKK,kBAAkB,IAAI,CAACL,KAAKK,kBAAkB,CAACC,UAAU,CAAC,OAAO;gBACxE,OAAON,KAAKK,kBAAkB;YAChC;QACF,EAAE,OAAOb,OAAO;YACdV,MAAM,+EAA+EU;QACvF;QAEA,OAAO;IACT;AACF"}
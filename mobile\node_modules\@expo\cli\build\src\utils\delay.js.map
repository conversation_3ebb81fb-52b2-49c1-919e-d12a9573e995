{"version": 3, "sources": ["../../../src/utils/delay.ts"], "sourcesContent": ["import { CommandError } from './errors';\n\n/** Await for a given duration of milliseconds. */\nexport function delayAsync(timeout: number): Promise<void> {\n  return new Promise((resolve) => setTimeout(resolve, timeout));\n}\n\n/** Wait for a given action to return a truthy value. */\nexport async function waitForActionAsync<T>({\n  action,\n  interval = 100,\n  maxWaitTime = 20000,\n}: {\n  action: () => T | Promise<T>;\n  interval?: number;\n  maxWaitTime?: number;\n}): Promise<T> {\n  let complete: T;\n  const start = Date.now();\n  do {\n    const actionStartTime = Date.now();\n    complete = await action();\n\n    const actionTimeElapsed = Date.now() - actionStartTime;\n    const remainingDelayInterval = interval - actionTimeElapsed;\n    if (remainingDelayInterval > 0) {\n      await delayAsync(remainingDelayInterval);\n    }\n    if (Date.now() - start > maxWaitTime) {\n      break;\n    }\n  } while (!complete);\n\n  return complete;\n}\n\n/** Resolves a given function or rejects if the provided timeout is passed. */\nexport function resolveWithTimeout<T>(\n  action: () => Promise<T>,\n  {\n    timeout,\n    errorMessage,\n  }: {\n    /** Duration in milliseconds to wait before asserting a timeout. */\n    timeout: number;\n    /** Optional error message to use in the assertion. */\n    errorMessage?: string;\n  }\n): Promise<T> {\n  return new Promise((resolve, reject) => {\n    setTimeout(() => {\n      reject(new CommandError('TIMEOUT', errorMessage));\n    }, timeout);\n    action().then(resolve, reject);\n  });\n}\n"], "names": ["delayAsync", "resolveWithTimeout", "waitForActionAsync", "timeout", "Promise", "resolve", "setTimeout", "action", "interval", "maxWaitTime", "complete", "start", "Date", "now", "actionStartTime", "actionTimeElapsed", "remainingDelayInterval", "errorMessage", "reject", "CommandError", "then"], "mappings": ";;;;;;;;;;;IAGgBA,UAAU;eAAVA;;IAkCAC,kBAAkB;eAAlBA;;IA7BMC,kBAAkB;eAAlBA;;;wBARO;AAGtB,SAASF,WAAWG,OAAe;IACxC,OAAO,IAAIC,QAAQ,CAACC,UAAYC,WAAWD,SAASF;AACtD;AAGO,eAAeD,mBAAsB,EAC1CK,MAAM,EACNC,WAAW,GAAG,EACdC,cAAc,KAAK,EAKpB;IACC,IAAIC;IACJ,MAAMC,QAAQC,KAAKC,GAAG;IACtB,GAAG;QACD,MAAMC,kBAAkBF,KAAKC,GAAG;QAChCH,WAAW,MAAMH;QAEjB,MAAMQ,oBAAoBH,KAAKC,GAAG,KAAKC;QACvC,MAAME,yBAAyBR,WAAWO;QAC1C,IAAIC,yBAAyB,GAAG;YAC9B,MAAMhB,WAAWgB;QACnB;QACA,IAAIJ,KAAKC,GAAG,KAAKF,QAAQF,aAAa;YACpC;QACF;IACF,QAAS,CAACC,UAAU;IAEpB,OAAOA;AACT;AAGO,SAAST,mBACdM,MAAwB,EACxB,EACEJ,OAAO,EACPc,YAAY,EAMb;IAED,OAAO,IAAIb,QAAQ,CAACC,SAASa;QAC3BZ,WAAW;YACTY,OAAO,IAAIC,oBAAY,CAAC,WAAWF;QACrC,GAAGd;QACHI,SAASa,IAAI,CAACf,SAASa;IACzB;AACF"}
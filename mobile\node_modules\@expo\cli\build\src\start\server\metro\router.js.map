{"version": 3, "sources": ["../../../../../src/start/server/metro/router.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport chalk from 'chalk';\nimport { sync as globSync } from 'glob';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { Log } from '../../../log';\nimport { directoryExistsSync } from '../../../utils/dir';\nimport { toPosixPath } from '../../../utils/filePath';\nimport { learnMore } from '../../../utils/link';\n\nconst debug = require('debug')('expo:start:server:metro:router') as typeof console.log;\n\n/**\n * Get the relative path for requiring the `/app` folder relative to the `expo-router/entry` file.\n * This mechanism does require the server to restart after the `expo-router` package is installed.\n */\nexport function getAppRouterRelativeEntryPath(\n  projectRoot: string,\n  routerDirectory: string = getRouterDirectory(projectRoot)\n): string | undefined {\n  // Auto pick App entry\n  const routerEntry =\n    resolveFrom.silent(projectRoot, 'expo-router/entry') ?? getFallbackEntryRoot(projectRoot);\n  if (!routerEntry) {\n    return undefined;\n  }\n  // It doesn't matter if the app folder exists.\n  const appFolder = path.join(projectRoot, routerDirectory);\n  const appRoot = path.relative(path.dirname(routerEntry), appFolder);\n  debug('expo-router entry', routerEntry, appFolder, appRoot);\n  return appRoot;\n}\n\n/** If the `expo-router` package is not installed, then use the `expo` package to determine where the node modules are relative to the project. */\nfunction getFallbackEntryRoot(projectRoot: string): string {\n  const expoRoot = resolveFrom.silent(projectRoot, 'expo/package.json');\n  if (expoRoot) {\n    return path.join(path.dirname(path.dirname(expoRoot)), 'expo-router/entry');\n  }\n  return path.join(projectRoot, 'node_modules/expo-router/entry');\n}\n\nexport function getRouterDirectoryModuleIdWithManifest(\n  projectRoot: string,\n  exp: ExpoConfig\n): string {\n  return toPosixPath(exp.extra?.router?.root ?? getRouterDirectory(projectRoot));\n}\n\nlet hasWarnedAboutSrcDir = false;\nconst logSrcDir = () => {\n  if (hasWarnedAboutSrcDir) return;\n  hasWarnedAboutSrcDir = true;\n  Log.log(chalk.gray('Using src/app as the root directory for Expo Router.'));\n};\n\nexport function getRouterDirectory(projectRoot: string): string {\n  // more specific directories first\n  if (directoryExistsSync(path.join(projectRoot, 'src', 'app'))) {\n    logSrcDir();\n    return path.join('src', 'app');\n  }\n\n  debug('Using app as the root directory for Expo Router.');\n  return 'app';\n}\n\nexport function isApiRouteConvention(name: string): boolean {\n  return /\\+api\\.[tj]sx?$/.test(name);\n}\n\nexport function getApiRoutesForDirectory(cwd: string) {\n  return globSync('**/*+api.@(ts|tsx|js|jsx)', {\n    cwd,\n    absolute: true,\n    dot: true,\n  });\n}\n\n// Used to emulate a context module, but way faster. TODO: May need to adjust the extensions to stay in sync with Metro.\nexport function getRoutePaths(cwd: string) {\n  return globSync('**/*.@(ts|tsx|js|jsx)', {\n    cwd,\n    dot: true,\n  }).map((p) => './' + normalizePaths(p));\n}\n\nfunction normalizePaths(p: string) {\n  return p.replace(/\\\\/g, '/');\n}\n\nlet hasWarnedAboutApiRouteOutput = false;\n\nexport function hasWarnedAboutApiRoutes() {\n  return hasWarnedAboutApiRouteOutput;\n}\n\nexport function warnInvalidWebOutput() {\n  if (!hasWarnedAboutApiRouteOutput) {\n    Log.warn(\n      chalk.yellow`Using API routes requires the {bold web.output} to be set to {bold \"server\"} in the project {bold app.json}. ${learnMore(\n        'https://docs.expo.dev/router/reference/api-routes/'\n      )}`\n    );\n  }\n\n  hasWarnedAboutApiRouteOutput = true;\n}\n"], "names": ["getApiRoutesForDirectory", "getAppRouterRelativeEntryPath", "getRoutePaths", "getRouterDirectory", "getRouterDirectoryModuleIdWithManifest", "hasWarnedAboutApiRoutes", "isApiRouteConvention", "warnInvalidWebOutput", "debug", "require", "projectRoot", "routerDirectory", "routerEntry", "resolveFrom", "silent", "getFallbackEntryRoot", "undefined", "appFolder", "path", "join", "appRoot", "relative", "dirname", "expoRoot", "exp", "toPosixPath", "extra", "router", "root", "hasWarnedAboutSrcDir", "logSrcDir", "Log", "log", "chalk", "gray", "directoryExistsSync", "name", "test", "cwd", "globSync", "absolute", "dot", "map", "p", "normalizePaths", "replace", "hasWarnedAboutApiRouteOutput", "warn", "yellow", "learnMore"], "mappings": ";;;;;;;;;;;IAwEgBA,wBAAwB;eAAxBA;;IAvDAC,6BAA6B;eAA7BA;;IAgEAC,aAAa;eAAbA;;IAxBAC,kBAAkB;eAAlBA;;IAdAC,sCAAsC;eAAtCA;;IAmDAC,uBAAuB;eAAvBA;;IA1BAC,oBAAoB;eAApBA;;IA8BAC,oBAAoB;eAApBA;;;;gEAjGE;;;;;;;yBACe;;;;;;;gEAChB;;;;;;;gEACO;;;;;;qBAEJ;qBACgB;0BACR;sBACF;;;;;;AAE1B,MAAMC,QAAQC,QAAQ,SAAS;AAMxB,SAASR,8BACdS,WAAmB,EACnBC,kBAA0BR,mBAAmBO,YAAY;IAEzD,sBAAsB;IACtB,MAAME,cACJC,sBAAW,CAACC,MAAM,CAACJ,aAAa,wBAAwBK,qBAAqBL;IAC/E,IAAI,CAACE,aAAa;QAChB,OAAOI;IACT;IACA,8CAA8C;IAC9C,MAAMC,YAAYC,eAAI,CAACC,IAAI,CAACT,aAAaC;IACzC,MAAMS,UAAUF,eAAI,CAACG,QAAQ,CAACH,eAAI,CAACI,OAAO,CAACV,cAAcK;IACzDT,MAAM,qBAAqBI,aAAaK,WAAWG;IACnD,OAAOA;AACT;AAEA,gJAAgJ,GAChJ,SAASL,qBAAqBL,WAAmB;IAC/C,MAAMa,WAAWV,sBAAW,CAACC,MAAM,CAACJ,aAAa;IACjD,IAAIa,UAAU;QACZ,OAAOL,eAAI,CAACC,IAAI,CAACD,eAAI,CAACI,OAAO,CAACJ,eAAI,CAACI,OAAO,CAACC,YAAY;IACzD;IACA,OAAOL,eAAI,CAACC,IAAI,CAACT,aAAa;AAChC;AAEO,SAASN,uCACdM,WAAmB,EACnBc,GAAe;QAEIA,mBAAAA;IAAnB,OAAOC,IAAAA,qBAAW,EAACD,EAAAA,aAAAA,IAAIE,KAAK,sBAATF,oBAAAA,WAAWG,MAAM,qBAAjBH,kBAAmBI,IAAI,KAAIzB,mBAAmBO;AACnE;AAEA,IAAImB,uBAAuB;AAC3B,MAAMC,YAAY;IAChB,IAAID,sBAAsB;IAC1BA,uBAAuB;IACvBE,QAAG,CAACC,GAAG,CAACC,gBAAK,CAACC,IAAI,CAAC;AACrB;AAEO,SAAS/B,mBAAmBO,WAAmB;IACpD,kCAAkC;IAClC,IAAIyB,IAAAA,wBAAmB,EAACjB,eAAI,CAACC,IAAI,CAACT,aAAa,OAAO,SAAS;QAC7DoB;QACA,OAAOZ,eAAI,CAACC,IAAI,CAAC,OAAO;IAC1B;IAEAX,MAAM;IACN,OAAO;AACT;AAEO,SAASF,qBAAqB8B,IAAY;IAC/C,OAAO,kBAAkBC,IAAI,CAACD;AAChC;AAEO,SAASpC,yBAAyBsC,GAAW;IAClD,OAAOC,IAAAA,YAAQ,EAAC,6BAA6B;QAC3CD;QACAE,UAAU;QACVC,KAAK;IACP;AACF;AAGO,SAASvC,cAAcoC,GAAW;IACvC,OAAOC,IAAAA,YAAQ,EAAC,yBAAyB;QACvCD;QACAG,KAAK;IACP,GAAGC,GAAG,CAAC,CAACC,IAAM,OAAOC,eAAeD;AACtC;AAEA,SAASC,eAAeD,CAAS;IAC/B,OAAOA,EAAEE,OAAO,CAAC,OAAO;AAC1B;AAEA,IAAIC,+BAA+B;AAE5B,SAASzC;IACd,OAAOyC;AACT;AAEO,SAASvC;IACd,IAAI,CAACuC,8BAA8B;QACjCf,QAAG,CAACgB,IAAI,CACNd,gBAAK,CAACe,MAAM,CAAC,6GAA6G,EAAEC,IAAAA,eAAS,EACnI,sDACA,CAAC;IAEP;IAEAH,+BAA+B;AACjC"}
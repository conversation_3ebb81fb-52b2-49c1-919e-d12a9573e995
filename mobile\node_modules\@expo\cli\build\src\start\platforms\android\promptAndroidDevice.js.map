{"version": 3, "sources": ["../../../../../src/start/platforms/android/promptAndroidDevice.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport { Device, logUnauthorized } from './adb';\nimport { AbortCommandError } from '../../../utils/errors';\nimport { createSelectionFilter, promptAsync } from '../../../utils/prompts';\n\nexport async function promptForDeviceAsync(devices: Device[]): Promise<Device> {\n  // TODO: provide an option to add or download more simulators\n\n  const { value } = await promptAsync({\n    type: 'autocomplete',\n    name: 'value',\n    limit: 11,\n    message: 'Select a device/emulator',\n    choices: devices.map((item) => formatDeviceChoice(item)),\n    suggest: createSelectionFilter(),\n  });\n\n  const device = devices.find(({ name }) => name === value);\n\n  if (device?.isAuthorized === false) {\n    logUnauthorized(device);\n    throw new AbortCommandError();\n  }\n\n  return device!;\n}\n\n/**\n * Format the device for prompt list.\n * @internal - Exposed for testing.\n */\nexport function formatDeviceChoice(device: Device): { title: string; value: string } {\n  const symbol = getDeviceChoiceSymbol(device);\n  const name = getDeviceChoiceName(device);\n  const type = chalk.dim(device.isAuthorized ? device.type : 'unauthorized');\n\n  return {\n    value: device.name,\n    title: `${symbol}${name} (${type})`,\n  };\n}\n\n/** Get the styled symbol of the device, based on ADB connection type (usb vs network) */\nfunction getDeviceChoiceSymbol(device: Device) {\n  if (device.type === 'device' && device.connectionType === 'Network') {\n    return '🌐 ';\n  }\n\n  if (device.type === 'device') {\n    return '🔌 ';\n  }\n\n  return '';\n}\n\n/** Get the styled name of the device, based on device state */\nfunction getDeviceChoiceName(device: Device) {\n  // Use no style changes for a disconnected device that is available to be opened.\n  if (!device.isBooted) {\n    return device.name;\n  }\n\n  // A device that is connected and ready to be used should be bolded to match iOS.\n  if (device.isAuthorized) {\n    return chalk.bold(device.name);\n  }\n\n  // Devices that are unauthorized and connected cannot be used, but they are connected so gray them out.\n  return chalk.bold(chalk.gray(device.name));\n}\n"], "names": ["formatDeviceChoice", "promptForDeviceAsync", "devices", "value", "promptAsync", "type", "name", "limit", "message", "choices", "map", "item", "suggest", "createSelectionFilter", "device", "find", "isAuthorized", "logUnauthorized", "AbortCommandError", "symbol", "getDeviceChoiceSymbol", "getDeviceChoiceName", "chalk", "dim", "title", "connectionType", "isBooted", "bold", "gray"], "mappings": ";;;;;;;;;;;IAgCgBA,kBAAkB;eAAlBA;;IA1BMC,oBAAoB;eAApBA;;;;gEANJ;;;;;;qBAEsB;wBACN;yBACiB;;;;;;AAE5C,eAAeA,qBAAqBC,OAAiB;IAC1D,6DAA6D;IAE7D,MAAM,EAAEC,KAAK,EAAE,GAAG,MAAMC,IAAAA,oBAAW,EAAC;QAClCC,MAAM;QACNC,MAAM;QACNC,OAAO;QACPC,SAAS;QACTC,SAASP,QAAQQ,GAAG,CAAC,CAACC,OAASX,mBAAmBW;QAClDC,SAASC,IAAAA,8BAAqB;IAChC;IAEA,MAAMC,SAASZ,QAAQa,IAAI,CAAC,CAAC,EAAET,IAAI,EAAE,GAAKA,SAASH;IAEnD,IAAIW,CAAAA,0BAAAA,OAAQE,YAAY,MAAK,OAAO;QAClCC,IAAAA,oBAAe,EAACH;QAChB,MAAM,IAAII,yBAAiB;IAC7B;IAEA,OAAOJ;AACT;AAMO,SAASd,mBAAmBc,MAAc;IAC/C,MAAMK,SAASC,sBAAsBN;IACrC,MAAMR,OAAOe,oBAAoBP;IACjC,MAAMT,OAAOiB,gBAAK,CAACC,GAAG,CAACT,OAAOE,YAAY,GAAGF,OAAOT,IAAI,GAAG;IAE3D,OAAO;QACLF,OAAOW,OAAOR,IAAI;QAClBkB,OAAO,GAAGL,SAASb,KAAK,EAAE,EAAED,KAAK,CAAC,CAAC;IACrC;AACF;AAEA,uFAAuF,GACvF,SAASe,sBAAsBN,MAAc;IAC3C,IAAIA,OAAOT,IAAI,KAAK,YAAYS,OAAOW,cAAc,KAAK,WAAW;QACnE,OAAO;IACT;IAEA,IAAIX,OAAOT,IAAI,KAAK,UAAU;QAC5B,OAAO;IACT;IAEA,OAAO;AACT;AAEA,6DAA6D,GAC7D,SAASgB,oBAAoBP,MAAc;IACzC,iFAAiF;IACjF,IAAI,CAACA,OAAOY,QAAQ,EAAE;QACpB,OAAOZ,OAAOR,IAAI;IACpB;IAEA,iFAAiF;IACjF,IAAIQ,OAAOE,YAAY,EAAE;QACvB,OAAOM,gBAAK,CAACK,IAAI,CAACb,OAAOR,IAAI;IAC/B;IAEA,uGAAuG;IACvG,OAAOgB,gBAAK,CAACK,IAAI,CAACL,gBAAK,CAACM,IAAI,CAACd,OAAOR,IAAI;AAC1C"}
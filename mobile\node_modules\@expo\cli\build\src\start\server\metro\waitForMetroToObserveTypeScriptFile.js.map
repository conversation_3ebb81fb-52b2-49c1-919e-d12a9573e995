{"version": 3, "sources": ["../../../../../src/start/server/metro/waitForMetroToObserveTypeScriptFile.ts"], "sourcesContent": ["import path from 'path';\n\nimport type { ServerLike } from '../BundlerDevServer';\n\nconst debug = require('debug')('expo:start:server:metro:waitForTypescript') as typeof console.log;\n\nexport type FileChangeEvent = {\n  filePath: string;\n  metadata?: {\n    type: 'f' | 'd' | 'l'; // Regular file / Directory / Symlink\n  } | null;\n  type: string;\n};\n\n/**\n * Use the native file watcher / Metro ruleset to detect if a\n * TypeScript file is added to the project during development.\n */\nexport function waitForMetroToObserveTypeScriptFile(\n  projectRoot: string,\n  runner: {\n    metro: import('metro').Server;\n    server: ServerLike;\n  },\n  callback: () => Promise<void>\n): () => void {\n  const watcher = runner.metro.getBundler().getBundler().getWatcher();\n\n  const tsconfigPath = path.join(projectRoot, 'tsconfig.json');\n\n  const listener = ({ eventsQueue }: { eventsQueue: FileChangeEvent[] }) => {\n    for (const event of eventsQueue) {\n      if (\n        event.type === 'add' &&\n        event.metadata?.type !== 'd' &&\n        // We need to ignore node_modules because Metro will add all of the files in node_modules to the watcher.\n        !/node_modules/.test(event.filePath)\n      ) {\n        const { filePath } = event;\n        // Is TypeScript?\n        if (\n          // If the user adds a TypeScript file to the observable files in their project.\n          /\\.tsx?$/.test(filePath) ||\n          // Or if the user adds a tsconfig.json file to the project root.\n          filePath === tsconfigPath\n        ) {\n          debug('Detected TypeScript file added to the project: ', filePath);\n          callback();\n          off();\n          return;\n        }\n      }\n    }\n  };\n\n  debug('Waiting for TypeScript files to be added to the project...');\n  watcher.addListener('change', listener);\n\n  const off = () => {\n    watcher.removeListener('change', listener);\n  };\n\n  runner.server.addListener?.('close', off);\n  return off;\n}\n\nexport function observeFileChanges(\n  runner: {\n    metro: import('metro').Server;\n    server: ServerLike;\n  },\n  files: string[],\n  callback: () => void | Promise<void>\n): () => void {\n  const watcher = runner.metro.getBundler().getBundler().getWatcher();\n\n  const listener = ({\n    eventsQueue,\n  }: {\n    eventsQueue: {\n      filePath: string;\n      metadata?: {\n        type: 'f' | 'd' | 'l'; // Regular file / Directory / Symlink\n      } | null;\n      type: string;\n    }[];\n  }) => {\n    for (const event of eventsQueue) {\n      if (\n        // event.type === 'add' &&\n        event.metadata?.type !== 'd' &&\n        // We need to ignore node_modules because Metro will add all of the files in node_modules to the watcher.\n        !/node_modules/.test(event.filePath)\n      ) {\n        const { filePath } = event;\n        // Is TypeScript?\n        if (files.includes(filePath)) {\n          debug('Observed change:', filePath);\n          callback();\n          return;\n        }\n      }\n    }\n  };\n\n  debug('Watching file changes:', files);\n  watcher.addListener('change', listener);\n\n  const off = () => {\n    watcher.removeListener('change', listener);\n  };\n\n  runner.server.addListener?.('close', off);\n  return off;\n}\n\nexport function observeAnyFileChanges(\n  runner: {\n    metro: import('metro').Server;\n    server: ServerLike;\n  },\n  callback: (events: FileChangeEvent[]) => void | Promise<void>\n): () => void {\n  const watcher = runner.metro.getBundler().getBundler().getWatcher();\n\n  const listener = ({ eventsQueue }: { eventsQueue: FileChangeEvent[] }) => {\n    callback(eventsQueue);\n  };\n\n  watcher.addListener('change', listener);\n\n  const off = () => {\n    watcher.removeListener('change', listener);\n  };\n\n  runner.server.addListener?.('close', off);\n  return off;\n}\n"], "names": ["observeAnyFileChanges", "observeFileChanges", "waitForMetroToObserveTypeScriptFile", "debug", "require", "projectRoot", "runner", "callback", "watcher", "metro", "getBundler", "getW<PERSON>er", "tsconfigPath", "path", "join", "listener", "eventsQueue", "event", "type", "metadata", "test", "filePath", "off", "addListener", "removeListener", "server", "files", "includes"], "mappings": ";;;;;;;;;;;IAoHgBA,qBAAqB;eAArBA;;IAlDAC,kBAAkB;eAAlBA;;IAhDAC,mCAAmC;eAAnCA;;;;gEAlBC;;;;;;;;;;;AAIjB,MAAMC,QAAQC,QAAQ,SAAS;AAcxB,SAASF,oCACdG,WAAmB,EACnBC,MAGC,EACDC,QAA6B;IAE7B,MAAMC,UAAUF,OAAOG,KAAK,CAACC,UAAU,GAAGA,UAAU,GAAGC,UAAU;IAEjE,MAAMC,eAAeC,eAAI,CAACC,IAAI,CAACT,aAAa;IAE5C,MAAMU,WAAW,CAAC,EAAEC,WAAW,EAAsC;QACnE,KAAK,MAAMC,SAASD,YAAa;gBAG7BC;YAFF,IACEA,MAAMC,IAAI,KAAK,SACfD,EAAAA,kBAAAA,MAAME,QAAQ,qBAAdF,gBAAgBC,IAAI,MAAK,OACzB,yGAAyG;YACzG,CAAC,eAAeE,IAAI,CAACH,MAAMI,QAAQ,GACnC;gBACA,MAAM,EAAEA,QAAQ,EAAE,GAAGJ;gBACrB,iBAAiB;gBACjB,IACE,+EAA+E;gBAC/E,UAAUG,IAAI,CAACC,aACf,gEAAgE;gBAChEA,aAAaT,cACb;oBACAT,MAAM,mDAAmDkB;oBACzDd;oBACAe;oBACA;gBACF;YACF;QACF;IACF;IAEAnB,MAAM;IACNK,QAAQe,WAAW,CAAC,UAAUR;IAE9B,MAAMO,MAAM;QACVd,QAAQgB,cAAc,CAAC,UAAUT;IACnC;IAEAT,OAAOmB,MAAM,CAACF,WAAW,oBAAzBjB,OAAOmB,MAAM,CAACF,WAAW,MAAzBjB,OAAOmB,MAAM,EAAe,SAASH;IACrC,OAAOA;AACT;AAEO,SAASrB,mBACdK,MAGC,EACDoB,KAAe,EACfnB,QAAoC;IAEpC,MAAMC,UAAUF,OAAOG,KAAK,CAACC,UAAU,GAAGA,UAAU,GAAGC,UAAU;IAEjE,MAAMI,WAAW,CAAC,EAChBC,WAAW,EASZ;QACC,KAAK,MAAMC,SAASD,YAAa;gBAE7B,0BAA0B;YAC1BC;YAFF,IAEEA,EAAAA,kBAAAA,MAAME,QAAQ,qBAAdF,gBAAgBC,IAAI,MAAK,OACzB,yGAAyG;YACzG,CAAC,eAAeE,IAAI,CAACH,MAAMI,QAAQ,GACnC;gBACA,MAAM,EAAEA,QAAQ,EAAE,GAAGJ;gBACrB,iBAAiB;gBACjB,IAAIS,MAAMC,QAAQ,CAACN,WAAW;oBAC5BlB,MAAM,oBAAoBkB;oBAC1Bd;oBACA;gBACF;YACF;QACF;IACF;IAEAJ,MAAM,0BAA0BuB;IAChClB,QAAQe,WAAW,CAAC,UAAUR;IAE9B,MAAMO,MAAM;QACVd,QAAQgB,cAAc,CAAC,UAAUT;IACnC;IAEAT,OAAOmB,MAAM,CAACF,WAAW,oBAAzBjB,OAAOmB,MAAM,CAACF,WAAW,MAAzBjB,OAAOmB,MAAM,EAAe,SAASH;IACrC,OAAOA;AACT;AAEO,SAAStB,sBACdM,MAGC,EACDC,QAA6D;IAE7D,MAAMC,UAAUF,OAAOG,KAAK,CAACC,UAAU,GAAGA,UAAU,GAAGC,UAAU;IAEjE,MAAMI,WAAW,CAAC,EAAEC,WAAW,EAAsC;QACnET,SAASS;IACX;IAEAR,QAAQe,WAAW,CAAC,UAAUR;IAE9B,MAAMO,MAAM;QACVd,QAAQgB,cAAc,CAAC,UAAUT;IACnC;IAEAT,OAAOmB,MAAM,CAACF,WAAW,oBAAzBjB,OAAOmB,MAAM,CAACF,WAAW,MAAzBjB,OAAOmB,MAAM,EAAe,SAASH;IACrC,OAAOA;AACT"}
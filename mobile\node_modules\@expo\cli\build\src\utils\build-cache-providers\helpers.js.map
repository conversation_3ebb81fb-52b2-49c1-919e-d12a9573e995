{"version": 3, "sources": ["../../../../src/utils/build-cache-providers/helpers.ts"], "sourcesContent": ["import fs from 'fs';\n\nexport function moduleNameIsDirectFileReference(name: string): boolean {\n  // Check if path is a file. Matches lines starting with: . / ~/\n  if (name.match(/^(\\.|~\\/|\\/)/g)) {\n    return true;\n  }\n\n  const slashCount = name.split('/')?.length;\n  // Orgs (like @expo/config ) should have more than one slash to be a direct file.\n  if (name.startsWith('@')) {\n    return slashCount > 2;\n  }\n\n  // Regular packages should be considered direct reference if they have more than one slash.\n  return slashCount > 1;\n}\n\nexport function moduleNameIsPackageReference(name: string): boolean {\n  const slashCount = name.split('/')?.length;\n  return name.startsWith('@') ? slashCount === 2 : slashCount === 1;\n}\n\nexport function fileExists(file: string): boolean {\n  try {\n    return fs.statSync(file).isFile();\n  } catch {\n    return false;\n  }\n}\n"], "names": ["fileExists", "moduleNameIsDirectFileReference", "moduleNameIsPackageReference", "name", "match", "slashCount", "split", "length", "startsWith", "file", "fs", "statSync", "isFile"], "mappings": ";;;;;;;;;;;IAuBgBA,UAAU;eAAVA;;IArBAC,+BAA+B;eAA/BA;;IAgBAC,4BAA4B;eAA5BA;;;;gEAlBD;;;;;;;;;;;AAER,SAASD,gCAAgCE,IAAY;QAMvCA;IALnB,+DAA+D;IAC/D,IAAIA,KAAKC,KAAK,CAAC,kBAAkB;QAC/B,OAAO;IACT;IAEA,MAAMC,cAAaF,cAAAA,KAAKG,KAAK,CAAC,yBAAXH,YAAiBI,MAAM;IAC1C,iFAAiF;IACjF,IAAIJ,KAAKK,UAAU,CAAC,MAAM;QACxB,OAAOH,aAAa;IACtB;IAEA,2FAA2F;IAC3F,OAAOA,aAAa;AACtB;AAEO,SAASH,6BAA6BC,IAAY;QACpCA;IAAnB,MAAME,cAAaF,cAAAA,KAAKG,KAAK,CAAC,yBAAXH,YAAiBI,MAAM;IAC1C,OAAOJ,KAAKK,UAAU,CAAC,OAAOH,eAAe,IAAIA,eAAe;AAClE;AAEO,SAASL,WAAWS,IAAY;IACrC,IAAI;QACF,OAAOC,aAAE,CAACC,QAAQ,CAACF,MAAMG,MAAM;IACjC,EAAE,OAAM;QACN,OAAO;IACT;AACF"}
{"version": 3, "sources": ["../../../../../src/start/server/webpack/resolveFromProject.ts"], "sourcesContent": ["import resolveFrom from 'resolve-from';\n\nimport { CommandError } from '../../../utils/errors';\n\n// These resolvers enable us to test the CLI in older projects.\n// We may be able to get rid of this in the future.\n// TODO: Maybe combine with AsyncResolver?\nclass WebpackImportError extends CommandError {\n  constructor(projectRoot: string, moduleId: string) {\n    super(\n      'WEBPACK_IMPORT',\n      `Missing package \"${moduleId}\" in the project. Try running the command again. (cwd: ${projectRoot})`\n    );\n  }\n}\n\nfunction resolveFromProject(projectRoot: string, moduleId: string) {\n  const resolvedPath = resolveFrom.silent(projectRoot, moduleId);\n  if (!resolvedPath) {\n    throw new WebpackImportError(projectRoot, moduleId);\n  }\n  return resolvedPath;\n}\n\nfunction importFromProject(projectRoot: string, moduleId: string) {\n  return require(resolveFromProject(projectRoot, moduleId));\n}\n\n/** Import `webpack` from the project. */\nexport function importWebpackFromProject(projectRoot: string): typeof import('webpack') {\n  return importFromProject(projectRoot, 'webpack');\n}\n\n/** Import `@expo/webpack-config` from the project. */\nexport function importExpoWebpackConfigFromProject(projectRoot: string) {\n  return importFromProject(projectRoot, '@expo/webpack-config');\n}\n\n/** Import `webpack-dev-server` from the project. */\nexport function importWebpackDevServerFromProject(\n  projectRoot: string\n): typeof import('webpack-dev-server') {\n  return importFromProject(projectRoot, 'webpack-dev-server');\n}\n"], "names": ["importExpoWebpackConfigFromProject", "importWebpackDevServerFromProject", "importWebpackFromProject", "WebpackImportError", "CommandError", "constructor", "projectRoot", "moduleId", "resolveFromProject", "<PERSON><PERSON><PERSON>", "resolveFrom", "silent", "importFromProject", "require"], "mappings": ";;;;;;;;;;;IAkCgBA,kCAAkC;eAAlCA;;IAKAC,iCAAiC;eAAjCA;;IAVAC,wBAAwB;eAAxBA;;;;gEA7BQ;;;;;;wBAEK;;;;;;AAE7B,+DAA+D;AAC/D,mDAAmD;AACnD,0CAA0C;AAC1C,MAAMC,2BAA2BC,oBAAY;IAC3CC,YAAYC,WAAmB,EAAEC,QAAgB,CAAE;QACjD,KAAK,CACH,kBACA,CAAC,iBAAiB,EAAEA,SAAS,uDAAuD,EAAED,YAAY,CAAC,CAAC;IAExG;AACF;AAEA,SAASE,mBAAmBF,WAAmB,EAAEC,QAAgB;IAC/D,MAAME,eAAeC,sBAAW,CAACC,MAAM,CAACL,aAAaC;IACrD,IAAI,CAACE,cAAc;QACjB,MAAM,IAAIN,mBAAmBG,aAAaC;IAC5C;IACA,OAAOE;AACT;AAEA,SAASG,kBAAkBN,WAAmB,EAAEC,QAAgB;IAC9D,OAAOM,QAAQL,mBAAmBF,aAAaC;AACjD;AAGO,SAASL,yBAAyBI,WAAmB;IAC1D,OAAOM,kBAAkBN,aAAa;AACxC;AAGO,SAASN,mCAAmCM,WAAmB;IACpE,OAAOM,kBAAkBN,aAAa;AACxC;AAGO,SAASL,kCACdK,WAAmB;IAEnB,OAAOM,kBAAkBN,aAAa;AACxC"}
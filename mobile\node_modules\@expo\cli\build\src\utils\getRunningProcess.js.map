{"version": 3, "sources": ["../../../src/utils/getRunningProcess.ts"], "sourcesContent": ["import { execFileSync, execSync, ExecSyncOptionsWithStringEncoding } from 'child_process';\nimport * as path from 'path';\n\nconst debug = require('debug')('expo:utils:getRunningProcess') as typeof console.log;\n\nconst defaultOptions: ExecSyncOptionsWithStringEncoding = {\n  encoding: 'utf8',\n  stdio: ['pipe', 'pipe', 'ignore'],\n};\n\n/** Returns a pid value for a running port like `63828` or null if nothing is running on the given port. */\nexport function getPID(port: number): number | null {\n  try {\n    const results = execFileSync('lsof', [`-i:${port}`, '-P', '-t', '-sTCP:LISTEN'], defaultOptions)\n      .split('\\n')[0]\n      .trim();\n    const pid = Number(results);\n    debug(`pid: ${pid} for port: ${port}`);\n    return pid;\n  } catch (error: any) {\n    debug(`No pid found for port: ${port}. Error: ${error}`);\n    return null;\n  }\n}\n\n/** Get `package.json` `name` field for a given directory. Returns `null` if none exist. */\nfunction getPackageName(packageRoot: string): string | null {\n  const packageJson = path.join(packageRoot, 'package.json');\n  try {\n    return require(packageJson).name || null;\n  } catch {\n    return null;\n  }\n}\n\n/** Returns a command like `node /Users/<USER>/.../bin/expo start` or the package.json name. */\nfunction getProcessCommand(pid: number, procDirectory: string): string {\n  const name = getPackageName(procDirectory);\n\n  if (name) {\n    return name;\n  }\n  return execSync(`ps -o command -p ${pid} | sed -n 2p`, defaultOptions).replace(/\\n$/, '').trim();\n}\n\n/** Get directory for a given process ID. */\nexport function getDirectoryOfProcessById(processId: number): string {\n  return execSync(\n    `lsof -p ${processId} | awk '$4==\"cwd\" {for (i=9; i<=NF; i++) printf \"%s \", $i}'`,\n    defaultOptions\n  ).trim();\n}\n\n/** Get information about a running process given a port. Returns null if no process is running on the given port. */\nexport function getRunningProcess(port: number): {\n  /** The PID value for the port. */\n  pid: number;\n  /** Get the directory for the running process. */\n  directory: string;\n  /** The command running the process like `node /Users/<USER>/.../bin/expo start` or the `package.json` name like `my-app`. */\n  command: string;\n} | null {\n  // 63828\n  const pid = getPID(port);\n  if (!pid) {\n    return null;\n  }\n\n  try {\n    // /Users/<USER>/Documents/GitHub/lab/myapp\n    const directory = getDirectoryOfProcessById(pid);\n    // /Users/<USER>/Documents/GitHub/lab/myapp/package.json\n    const command = getProcessCommand(pid, directory);\n    // TODO: Have a better message for reusing another process.\n    return { pid, directory, command };\n  } catch {\n    return null;\n  }\n}\n"], "names": ["getDirectoryOfProcessById", "getPID", "getRunningProcess", "debug", "require", "defaultOptions", "encoding", "stdio", "port", "results", "execFileSync", "split", "trim", "pid", "Number", "error", "getPackageName", "packageRoot", "packageJson", "path", "join", "name", "getProcessCommand", "procDirectory", "execSync", "replace", "processId", "directory", "command"], "mappings": ";;;;;;;;;;;IA8CgBA,yBAAyB;eAAzBA;;IAnCAC,MAAM;eAANA;;IA2CAC,iBAAiB;eAAjBA;;;;yBAtD0D;;;;;;;iEACpD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtB,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,MAAMC,iBAAoD;IACxDC,UAAU;IACVC,OAAO;QAAC;QAAQ;QAAQ;KAAS;AACnC;AAGO,SAASN,OAAOO,IAAY;IACjC,IAAI;QACF,MAAMC,UAAUC,IAAAA,6BAAY,EAAC,QAAQ;YAAC,CAAC,GAAG,EAAEF,MAAM;YAAE;YAAM;YAAM;SAAe,EAAEH,gBAC9EM,KAAK,CAAC,KAAK,CAAC,EAAE,CACdC,IAAI;QACP,MAAMC,MAAMC,OAAOL;QACnBN,MAAM,CAAC,KAAK,EAAEU,IAAI,WAAW,EAAEL,MAAM;QACrC,OAAOK;IACT,EAAE,OAAOE,OAAY;QACnBZ,MAAM,CAAC,uBAAuB,EAAEK,KAAK,SAAS,EAAEO,OAAO;QACvD,OAAO;IACT;AACF;AAEA,yFAAyF,GACzF,SAASC,eAAeC,WAAmB;IACzC,MAAMC,cAAcC,QAAKC,IAAI,CAACH,aAAa;IAC3C,IAAI;QACF,OAAOb,QAAQc,aAAaG,IAAI,IAAI;IACtC,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,gGAAgG,GAChG,SAASC,kBAAkBT,GAAW,EAAEU,aAAqB;IAC3D,MAAMF,OAAOL,eAAeO;IAE5B,IAAIF,MAAM;QACR,OAAOA;IACT;IACA,OAAOG,IAAAA,yBAAQ,EAAC,CAAC,iBAAiB,EAAEX,IAAI,YAAY,CAAC,EAAER,gBAAgBoB,OAAO,CAAC,OAAO,IAAIb,IAAI;AAChG;AAGO,SAASZ,0BAA0B0B,SAAiB;IACzD,OAAOF,IAAAA,yBAAQ,EACb,CAAC,QAAQ,EAAEE,UAAU,2DAA2D,CAAC,EACjFrB,gBACAO,IAAI;AACR;AAGO,SAASV,kBAAkBM,IAAY;IAQ5C,QAAQ;IACR,MAAMK,MAAMZ,OAAOO;IACnB,IAAI,CAACK,KAAK;QACR,OAAO;IACT;IAEA,IAAI;QACF,8CAA8C;QAC9C,MAAMc,YAAY3B,0BAA0Ba;QAC5C,2DAA2D;QAC3D,MAAMe,UAAUN,kBAAkBT,KAAKc;QACvC,2DAA2D;QAC3D,OAAO;YAAEd;YAAKc;YAAWC;QAAQ;IACnC,EAAE,OAAM;QACN,OAAO;IACT;AACF"}
export declare function log(...params: Parameters<typeof console.log>): void;
export declare function debug(...params: Parameters<typeof console.debug>): void;
export declare function info(...params: Parameters<typeof console.info>): void;
export declare function warn(...params: Parameters<typeof console.info>): void;
export declare function setEnableLogging(enabled: boolean): void;
//# sourceMappingURL=logger.d.ts.map
{"version": 3, "sources": ["../../../src/utils/isModuleSymlinked.ts"], "sourcesContent": ["import path from 'path';\nimport resolveFrom from 'resolve-from';\n\n/**\n * Return true if the parent folder for a given file path is named \"node_modules\".\n *\n * @example\n * isModuleRootPathInNodeModulesFolder('./foo/expo') -> false\n * isModuleRootPathInNodeModulesFolder('./node_modules/expo') -> true\n */\nfunction isModuleRootPathInNodeModulesFolder(moduleRootPath: string): boolean {\n  const parentFolderName = path.basename(path.dirname(moduleRootPath));\n  return parentFolderName === 'node_modules';\n}\n\n/**\n * Given a node module name, and a project path, this method will:\n *\n * 1. Resolve the module path.\n * 2. Find the module root folder.\n * 3. Return true if the module root folder is in a folder named `node_modules`\n *\n * @param projectRoot\n * @param moduleId\n *\n * @example\n * isModuleSymlinked('./expo/apps/native-component-list', {\n *   moduleId: 'react-native'\n * })\n */\nexport function isModuleSymlinked(\n  projectRoot: string,\n  {\n    moduleId,\n    isSilent,\n  }: {\n    moduleId: string;\n    isSilent?: boolean;\n  }\n): boolean {\n  try {\n    const moduleRootPath = path.dirname(resolveFrom(projectRoot, `${moduleId}/package.json`));\n    return !isModuleRootPathInNodeModulesFolder(moduleRootPath);\n  } catch (error) {\n    if (!isSilent) {\n      throw error;\n    }\n    // Failed to resolve the package.json relative to the project, not sure what to do here.\n    // This is probably not possible due to node module resolution.\n    return false;\n  }\n}\n"], "names": ["isModuleSymlinked", "isModuleRootPathInNodeModulesFolder", "moduleRootPath", "parentFolderName", "path", "basename", "dirname", "projectRoot", "moduleId", "isSilent", "resolveFrom", "error"], "mappings": ";;;;+BA8BgBA;;;eAAAA;;;;gEA9BC;;;;;;;gEACO;;;;;;;;;;;AAExB;;;;;;CAMC,GACD,SAASC,oCAAoCC,cAAsB;IACjE,MAAMC,mBAAmBC,eAAI,CAACC,QAAQ,CAACD,eAAI,CAACE,OAAO,CAACJ;IACpD,OAAOC,qBAAqB;AAC9B;AAiBO,SAASH,kBACdO,WAAmB,EACnB,EACEC,QAAQ,EACRC,QAAQ,EAIT;IAED,IAAI;QACF,MAAMP,iBAAiBE,eAAI,CAACE,OAAO,CAACI,IAAAA,sBAAW,EAACH,aAAa,GAAGC,SAAS,aAAa,CAAC;QACvF,OAAO,CAACP,oCAAoCC;IAC9C,EAAE,OAAOS,OAAO;QACd,IAAI,CAACF,UAAU;YACb,MAAME;QACR;QACA,wFAAwF;QACxF,+DAA+D;QAC/D,OAAO;IACT;AACF"}
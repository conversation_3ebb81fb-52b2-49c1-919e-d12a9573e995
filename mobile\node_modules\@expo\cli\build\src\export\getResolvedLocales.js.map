{"version": 3, "sources": ["../../../src/export/getResolvedLocales.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport JsonF<PERSON>, { JSONObject } from '@expo/json-file';\nimport path from 'path';\n\nimport { CommandError } from '../utils/errors';\n\nexport type LocaleMap = Record<string, JSONObject>;\n\n// Similar to how we resolve locales in `@expo/config-plugins`\nexport async function getResolvedLocalesAsync(\n  projectRoot: string,\n  exp: Pick<ExpoConfig, 'locales'>\n): Promise<LocaleMap> {\n  if (!exp.locales) {\n    return {};\n  }\n\n  const locales: LocaleMap = {};\n  for (const [lang, localeJsonPath] of Object.entries(exp.locales)) {\n    if (typeof localeJsonPath === 'string') {\n      try {\n        locales[lang] = await JsonFile.readAsync(path.join(projectRoot, localeJsonPath));\n      } catch (error: any) {\n        throw new CommandError('EXPO_CONFIG', JSON.stringify(error));\n      }\n    } else {\n      // In the off chance that someone defined the locales json in the config, pass it directly to the object.\n      // We do this to make the types more elegant.\n      locales[lang] = localeJsonPath;\n    }\n  }\n  return locales;\n}\n"], "names": ["getResolvedLocalesAsync", "projectRoot", "exp", "locales", "lang", "localeJsonPath", "Object", "entries", "JsonFile", "readAsync", "path", "join", "error", "CommandError", "JSON", "stringify"], "mappings": ";;;;+BASsBA;;;eAAAA;;;;gEARe;;;;;;;gEACpB;;;;;;wBAEY;;;;;;AAKtB,eAAeA,wBACpBC,WAAmB,EACnBC,GAAgC;IAEhC,IAAI,CAACA,IAAIC,OAAO,EAAE;QAChB,OAAO,CAAC;IACV;IAEA,MAAMA,UAAqB,CAAC;IAC5B,KAAK,MAAM,CAACC,MAAMC,eAAe,IAAIC,OAAOC,OAAO,CAACL,IAAIC,OAAO,EAAG;QAChE,IAAI,OAAOE,mBAAmB,UAAU;YACtC,IAAI;gBACFF,OAAO,CAACC,KAAK,GAAG,MAAMI,mBAAQ,CAACC,SAAS,CAACC,eAAI,CAACC,IAAI,CAACV,aAAaI;YAClE,EAAE,OAAOO,OAAY;gBACnB,MAAM,IAAIC,oBAAY,CAAC,eAAeC,KAAKC,SAAS,CAACH;YACvD;QACF,OAAO;YACL,yGAAyG;YACzG,6CAA6C;YAC7CT,OAAO,CAACC,KAAK,GAAGC;QAClB;IACF;IACA,OAAOF;AACT"}
{"version": 3, "sources": ["../../../src/whoami/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport { Command } from '../../bin/cli';\nimport { assertArgs, printHelp } from '../utils/args';\nimport { logCmdError } from '../utils/errors';\n\nexport const expoWhoami: Command = async (argv) => {\n  const args = assertArgs(\n    {\n      // Types\n      '--help': <PERSON><PERSON><PERSON>,\n      // Aliases\n      '-h': '--help',\n    },\n    argv\n  );\n\n  if (args['--help']) {\n    printHelp(\n      `Show the currently authenticated username`,\n      `npx expo whoami`,\n      `-h, --help    Usage info`\n    );\n  }\n\n  const { whoamiAsync } = await import('./whoamiAsync.js');\n  return whoamiAsync().catch(logCmdError);\n};\n"], "names": ["expoWhoami", "argv", "args", "assertArgs", "Boolean", "printHelp", "<PERSON>ami<PERSON><PERSON>", "catch", "logCmdError"], "mappings": ";;;;;+BAKaA;;;eAAAA;;;sBAHyB;wBACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMA,aAAsB,OAAOC;IACxC,MAAMC,OAAOC,IAAAA,gBAAU,EACrB;QACE,QAAQ;QACR,UAAUC;QACV,UAAU;QACV,MAAM;IACR,GACAH;IAGF,IAAIC,IAAI,CAAC,SAAS,EAAE;QAClBG,IAAAA,eAAS,EACP,CAAC,yCAAyC,CAAC,EAC3C,CAAC,eAAe,CAAC,EACjB,CAAC,wBAAwB,CAAC;IAE9B;IAEA,MAAM,EAAEC,WAAW,EAAE,GAAG,MAAM,mEAAA,QAAO;IACrC,OAAOA,cAAcC,KAAK,CAACC,mBAAW;AACxC"}
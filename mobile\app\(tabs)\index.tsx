import { View, Text, SafeAreaView, TouchableOpacity, Alert } from 'react-native';
import { useUser, useClerk } from '@clerk/clerk-expo';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

export default function HomeScreen() {
  const { user } = useUser();
  const { signOut } = useClerk();
  const router = useRouter();

  const handleSignOut = async () => {
    try {
      await signOut();
      router.replace('/(auth)');
    } catch (error) {
      Alert.alert('Error', 'Failed to sign out. Please try again.');
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar style="dark" />
      
      {/* Header */}
      <View className="px-6 py-4 border-b border-gray-100">
        <View className="flex-row items-center justify-between">
          <View>
            <Text className="text-2xl font-bold text-gray-900">
              Welcome back!
            </Text>
            <Text className="text-gray-600 mt-1">
              {user?.emailAddresses[0]?.emailAddress}
            </Text>
          </View>
          <TouchableOpacity
            onPress={handleSignOut}
            className="bg-red-50 p-3 rounded-full"
          >
            <Ionicons name="log-out-outline" size={24} color="#EF4444" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Content */}
      <View className="flex-1 px-6 py-8">
        <View className="bg-blue-50 p-6 rounded-xl mb-6">
          <View className="flex-row items-center mb-4">
            <Ionicons name="checkmark-circle" size={24} color="#3B82F6" />
            <Text className="text-lg font-semibold text-blue-900 ml-3">
              Authentication Successful!
            </Text>
          </View>
          <Text className="text-blue-700">
            You have successfully signed in with Google. Your authentication is working perfectly.
          </Text>
        </View>

        {/* User Info Card */}
        <View className="bg-gray-50 p-6 rounded-xl">
          <Text className="text-lg font-semibold text-gray-900 mb-4">
            User Information
          </Text>
          
          <View className="space-y-3">
            <View className="flex-row justify-between">
              <Text className="text-gray-600">Name:</Text>
              <Text className="text-gray-900 font-medium">
                {user?.fullName || 'Not provided'}
              </Text>
            </View>
            
            <View className="flex-row justify-between">
              <Text className="text-gray-600">Email:</Text>
              <Text className="text-gray-900 font-medium">
                {user?.emailAddresses[0]?.emailAddress}
              </Text>
            </View>
            
            <View className="flex-row justify-between">
              <Text className="text-gray-600">User ID:</Text>
              <Text className="text-gray-900 font-medium text-xs">
                {user?.id}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}

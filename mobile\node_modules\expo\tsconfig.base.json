{"$schema": "https://json.schemastore.org/tsconfig", "display": "Expo", "compilerOptions": {"allowJs": true, "esModuleInterop": true, "jsx": "react-native", "lib": ["DOM", "ESNext"], "module": "preserve", "moduleDetection": "force", "moduleResolution": "bundler", "customConditions": ["react-native"], "noEmit": true, "resolveJsonModule": true, "skipLibCheck": true, "target": "ESNext"}, "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}
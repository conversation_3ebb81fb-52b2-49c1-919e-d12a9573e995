{"version": 3, "sources": ["../../../../src/run/android/resolveGradlePropsAsync.ts"], "sourcesContent": ["import path from 'path';\n\nimport { Device, getDeviceABIsAsync } from '../../start/platforms/android/adb';\nimport { CommandError } from '../../utils/errors';\n\n// Supported ABIs for Android. see https://developer.android.com/ndk/guides/abis\nconst VALID_ARCHITECTURES = ['armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'];\n\nexport type GradleProps = {\n  /** Directory for the APK based on the `variant`. */\n  apkVariantDirectory: string;\n  /** Name of the app, used in the `apkVariantDirectory`. */\n  appName: string;\n  /** Last section of the provided `variant`, indicates the starting directory of the file name for the output APK. E.g. \"debug\" or \"release\" */\n  buildType: string;\n  /** Used to assemble the APK, also included in the output APK filename. */\n  flavors?: string[];\n  /** Architectures to build for. */\n  architectures?: string;\n};\n\nfunction assertVariant(variant?: string) {\n  if (variant && typeof variant !== 'string') {\n    throw new CommandError('BAD_ARGS', '--variant must be a string');\n  }\n  return variant ?? 'debug';\n}\n\nexport async function resolveGradlePropsAsync(\n  projectRoot: string,\n  options: { variant?: string; allArch?: boolean },\n  device: Device\n): Promise<GradleProps> {\n  const variant = assertVariant(options.variant);\n  // NOTE(EvanBacon): Why would this be different? Can we get the different name?\n  const appName = 'app';\n\n  const apkDirectory = path.join(projectRoot, 'android', appName, 'build', 'outputs', 'apk');\n\n  // buildDeveloperTrust -> buildtype: trust, flavors: build, developer\n  // developmentDebug -> buildType: debug, flavors: development\n  // productionRelease -> buildType: release, flavors: production\n  // This won't work for non-standard flavor names like \"myFlavor\" would be treated as \"my\", \"flavor\".\n  const flavors = variant.split(/(?=[A-Z])/).map((v) => v.toLowerCase());\n  const buildType = flavors.pop() ?? 'debug';\n\n  const apkVariantDirectory = path.join(apkDirectory, ...flavors, buildType);\n  const architectures = await getConnectedDeviceABIS(buildType, device, options.allArch);\n\n  return {\n    appName,\n    buildType,\n    flavors,\n    apkVariantDirectory,\n    architectures,\n  };\n}\n\nasync function getConnectedDeviceABIS(\n  buildType: string,\n  device: Device,\n  allArch?: boolean\n): Promise<string> {\n  // Follow the same behavior as iOS, only enable this for debug builds\n  if (allArch || buildType !== 'debug') {\n    return '';\n  }\n\n  const abis = await getDeviceABIsAsync(device);\n\n  const validAbis = abis.filter((abi) => VALID_ARCHITECTURES.includes(abi));\n  return validAbis.filter((abi, i, arr) => arr.indexOf(abi) === i).join(',');\n}\n"], "names": ["resolveGradlePropsAsync", "VALID_ARCHITECTURES", "assert<PERSON><PERSON>t", "variant", "CommandError", "projectRoot", "options", "device", "appName", "apkDirectory", "path", "join", "flavors", "split", "map", "v", "toLowerCase", "buildType", "pop", "apkVariantDirectory", "architectures", "getConnectedDeviceABIS", "allArch", "abis", "getDeviceABIsAsync", "validAbis", "filter", "abi", "includes", "i", "arr", "indexOf"], "mappings": ";;;;+BA4BsBA;;;eAAAA;;;;gEA5BL;;;;;;qBAE0B;wBACd;;;;;;AAE7B,gFAAgF;AAChF,MAAMC,sBAAsB;IAAC;IAAe;IAAa;IAAO;CAAS;AAezE,SAASC,cAAcC,OAAgB;IACrC,IAAIA,WAAW,OAAOA,YAAY,UAAU;QAC1C,MAAM,IAAIC,oBAAY,CAAC,YAAY;IACrC;IACA,OAAOD,WAAW;AACpB;AAEO,eAAeH,wBACpBK,WAAmB,EACnBC,OAAgD,EAChDC,MAAc;IAEd,MAAMJ,UAAUD,cAAcI,QAAQH,OAAO;IAC7C,+EAA+E;IAC/E,MAAMK,UAAU;IAEhB,MAAMC,eAAeC,eAAI,CAACC,IAAI,CAACN,aAAa,WAAWG,SAAS,SAAS,WAAW;IAEpF,qEAAqE;IACrE,6DAA6D;IAC7D,+DAA+D;IAC/D,oGAAoG;IACpG,MAAMI,UAAUT,QAAQU,KAAK,CAAC,aAAaC,GAAG,CAAC,CAACC,IAAMA,EAAEC,WAAW;IACnE,MAAMC,YAAYL,QAAQM,GAAG,MAAM;IAEnC,MAAMC,sBAAsBT,eAAI,CAACC,IAAI,CAACF,iBAAiBG,SAASK;IAChE,MAAMG,gBAAgB,MAAMC,uBAAuBJ,WAAWV,QAAQD,QAAQgB,OAAO;IAErF,OAAO;QACLd;QACAS;QACAL;QACAO;QACAC;IACF;AACF;AAEA,eAAeC,uBACbJ,SAAiB,EACjBV,MAAc,EACde,OAAiB;IAEjB,qEAAqE;IACrE,IAAIA,WAAWL,cAAc,SAAS;QACpC,OAAO;IACT;IAEA,MAAMM,OAAO,MAAMC,IAAAA,uBAAkB,EAACjB;IAEtC,MAAMkB,YAAYF,KAAKG,MAAM,CAAC,CAACC,MAAQ1B,oBAAoB2B,QAAQ,CAACD;IACpE,OAAOF,UAAUC,MAAM,CAAC,CAACC,KAAKE,GAAGC,MAAQA,IAAIC,OAAO,CAACJ,SAASE,GAAGlB,IAAI,CAAC;AACxE"}
{"version": 3, "sources": ["../../../src/utils/codesigning.ts"], "sourcesContent": ["import { GraphQLError } from '@0no-co/graphql.web';\nimport {\n  convertCertificatePEMToCertificate,\n  convertKeyPairToPEM,\n  convertCSRToCSRPEM,\n  generateKeyPair,\n  generateCSR,\n  convertPrivateKeyPEMToPrivateKey,\n  validateSelfSignedCertificate,\n  signBufferRSASHA256AndVerify,\n} from '@expo/code-signing-certificates';\nimport { ExpoConfig } from '@expo/config';\nimport JsonFile, { JSONObject } from '@expo/json-file';\nimport { CombinedError } from '@urql/core';\nimport { promises as fs } from 'fs';\nimport { pki as PKI } from 'node-forge';\nimport path from 'path';\nimport { Dictionary, parseDictionary } from 'structured-headers';\n\nimport { env } from './env';\nimport { CommandError } from './errors';\nimport { getExpoGoIntermediateCertificateAsync } from '../api/getExpoGoIntermediateCertificate';\nimport { getProjectDevelopmentCertificateAsync } from '../api/getProjectDevelopmentCertificate';\nimport { AppQuery } from '../api/graphql/queries/AppQuery';\nimport { getExpoHomeDirectory } from '../api/user/UserSettings';\nimport { ensureLoggedInAsync } from '../api/user/actions';\nimport { Actor } from '../api/user/user';\nimport { AppByIdQuery, Permission } from '../graphql/generated';\nimport * as Log from '../log';\nimport { learnMore } from '../utils/link';\n\nconst debug = require('debug')('expo:codesigning') as typeof console.log;\n\nexport type CodeSigningInfo = {\n  keyId: string;\n  privateKey: string;\n  certificateForPrivateKey: string;\n  /**\n   * Chain of certificates to serve in the manifest multipart body \"certificate_chain\" part.\n   * The leaf certificate must be the 0th element of the array, followed by any intermediate certificates\n   * necessary to evaluate the chain of trust ending in the implicitly trusted root certificate embedded in\n   * the client.\n   *\n   * An empty array indicates that there is no need to serve the certificate chain in the multipart response.\n   */\n  certificateChainForResponse: string[];\n  /**\n   * Scope key cached for the project when certificate is development Expo Go code signing.\n   * For project-specific code signing (keyId == the project's generated keyId) this is undefined.\n   */\n  scopeKey: string | null;\n};\n\ntype StoredDevelopmentExpoRootCodeSigningInfo = {\n  easProjectId: string | null;\n  scopeKey: string | null;\n  privateKey: string | null;\n  certificateChain: string[] | null;\n};\nconst DEVELOPMENT_CODE_SIGNING_SETTINGS_FILE_NAME = 'development-code-signing-settings-2.json';\n\nexport function getDevelopmentCodeSigningDirectory(): string {\n  return path.join(getExpoHomeDirectory(), 'codesigning');\n}\n\nfunction getProjectDevelopmentCodeSigningInfoFile<T extends JSONObject>(defaults: T) {\n  function getFile(easProjectId: string): JsonFile<T> {\n    const filePath = path.join(\n      getDevelopmentCodeSigningDirectory(),\n      easProjectId,\n      DEVELOPMENT_CODE_SIGNING_SETTINGS_FILE_NAME\n    );\n    return new JsonFile<T>(filePath);\n  }\n\n  async function readAsync(easProjectId: string): Promise<T> {\n    let projectSettings;\n    try {\n      projectSettings = await getFile(easProjectId).readAsync();\n    } catch {\n      projectSettings = await getFile(easProjectId).writeAsync(defaults, { ensureDir: true });\n    }\n    // Set defaults for any missing fields\n    return { ...defaults, ...projectSettings };\n  }\n\n  async function setAsync(easProjectId: string, json: Partial<T>): Promise<T> {\n    try {\n      return await getFile(easProjectId).mergeAsync(json, {\n        cantReadFileDefault: defaults,\n      });\n    } catch {\n      return await getFile(easProjectId).writeAsync(\n        {\n          ...defaults,\n          ...json,\n        },\n        { ensureDir: true }\n      );\n    }\n  }\n\n  return {\n    getFile,\n    readAsync,\n    setAsync,\n  };\n}\n\nexport const DevelopmentCodeSigningInfoFile =\n  getProjectDevelopmentCodeSigningInfoFile<StoredDevelopmentExpoRootCodeSigningInfo>({\n    easProjectId: null,\n    scopeKey: null,\n    privateKey: null,\n    certificateChain: null,\n  });\n\n/**\n * Get info necessary to generate a response `expo-signature` header given a project and incoming request `expo-expect-signature` header.\n * This only knows how to serve two code signing keyids:\n * - `expo-root` indicates that it should use a development certificate in the `expo-root` chain. See {@link getExpoRootDevelopmentCodeSigningInfoAsync}\n * - <developer's expo-updates keyid> indicates that it should sign with the configured certificate. See {@link getProjectCodeSigningCertificateAsync}\n */\nexport async function getCodeSigningInfoAsync(\n  exp: ExpoConfig,\n  expectSignatureHeader: string | null,\n  privateKeyPath: string | undefined\n): Promise<CodeSigningInfo | null> {\n  if (!expectSignatureHeader) {\n    return null;\n  }\n\n  let parsedExpectSignature: Dictionary;\n  try {\n    parsedExpectSignature = parseDictionary(expectSignatureHeader);\n  } catch {\n    throw new CommandError('Invalid value for expo-expect-signature header');\n  }\n\n  const expectedKeyIdOuter = parsedExpectSignature.get('keyid');\n  if (!expectedKeyIdOuter) {\n    throw new CommandError('keyid not present in expo-expect-signature header');\n  }\n\n  const expectedKeyId = expectedKeyIdOuter[0];\n  if (typeof expectedKeyId !== 'string') {\n    throw new CommandError(\n      `Invalid value for keyid in expo-expect-signature header: ${expectedKeyId}`\n    );\n  }\n\n  let expectedAlg: string | null = null;\n  const expectedAlgOuter = parsedExpectSignature.get('alg');\n  if (expectedAlgOuter) {\n    const expectedAlgTemp = expectedAlgOuter[0];\n    if (typeof expectedAlgTemp !== 'string') {\n      throw new CommandError('Invalid value for alg in expo-expect-signature header');\n    }\n    expectedAlg = expectedAlgTemp;\n  }\n\n  if (expectedKeyId === 'expo-root') {\n    return await getExpoRootDevelopmentCodeSigningInfoAsync(exp);\n  } else if (expectedKeyId === 'expo-go') {\n    throw new CommandError(\n      'Invalid certificate requested: cannot sign with embedded keyid=expo-go key'\n    );\n  } else {\n    return await getProjectCodeSigningCertificateAsync(\n      exp,\n      privateKeyPath,\n      expectedKeyId,\n      expectedAlg\n    );\n  }\n}\n\n/**\n * Get a development code signing certificate for the expo-root -> expo-go -> (development certificate) certificate chain.\n * This requires the user be logged in and online, otherwise try to use the cached development certificate.\n */\nasync function getExpoRootDevelopmentCodeSigningInfoAsync(\n  exp: ExpoConfig\n): Promise<CodeSigningInfo | null> {\n  const easProjectId = exp.extra?.eas?.projectId;\n  // can't check for scope key validity since scope key is derived on the server from projectId and we may be offline.\n  // we rely upon the client certificate check to validate the scope key\n  if (!easProjectId) {\n    debug(\n      `WARN: Expo Application Services (EAS) is not configured for your project. Configuring EAS enables a more secure development experience amongst many other benefits. ${learnMore(\n        'https://docs.expo.dev/eas/'\n      )}`\n    );\n    return null;\n  }\n\n  const developmentCodeSigningInfoFromFile =\n    await DevelopmentCodeSigningInfoFile.readAsync(easProjectId);\n  const validatedCodeSigningInfo = validateStoredDevelopmentExpoRootCertificateCodeSigningInfo(\n    developmentCodeSigningInfoFromFile,\n    easProjectId\n  );\n\n  // 1. If online, ensure logged in, generate key pair and CSR, fetch and cache certificate chain for projectId\n  //    (overwriting existing dev cert in case projectId changed or it has expired)\n  if (!env.EXPO_OFFLINE) {\n    try {\n      return await fetchAndCacheNewDevelopmentCodeSigningInfoAsync(easProjectId);\n    } catch (e: any) {\n      if (validatedCodeSigningInfo) {\n        Log.warn(\n          'There was an error fetching the Expo development certificate, falling back to cached certificate'\n        );\n        return validatedCodeSigningInfo;\n      } else {\n        // need to return null here and say a message\n        throw e;\n      }\n    }\n  }\n\n  // 2. check for cached cert/private key matching projectId and scopeKey of project, if found and valid return private key and cert chain including expo-go cert\n  if (validatedCodeSigningInfo) {\n    return validatedCodeSigningInfo;\n  }\n\n  // 3. if offline, return null\n  Log.warn('Offline and no cached development certificate found, unable to sign manifest');\n  return null;\n}\n\n/**\n * Get the certificate configured for expo-updates for this project.\n */\nasync function getProjectCodeSigningCertificateAsync(\n  exp: ExpoConfig,\n  privateKeyPath: string | undefined,\n  expectedKeyId: string,\n  expectedAlg: string | null\n): Promise<CodeSigningInfo | null> {\n  const codeSigningCertificatePath = exp.updates?.codeSigningCertificate;\n  if (!codeSigningCertificatePath) {\n    return null;\n  }\n\n  if (!privateKeyPath) {\n    throw new CommandError(\n      'Must specify --private-key-path argument to sign development manifest for requested code signing key'\n    );\n  }\n\n  const codeSigningMetadata = exp.updates?.codeSigningMetadata;\n  if (!codeSigningMetadata) {\n    throw new CommandError(\n      'Must specify \"codeSigningMetadata\" under the \"updates\" field of your app config file to use EAS code signing'\n    );\n  }\n\n  const { alg, keyid } = codeSigningMetadata;\n  if (!alg || !keyid) {\n    throw new CommandError(\n      'Must specify \"keyid\" and \"alg\" in the \"codeSigningMetadata\" field under the \"updates\" field of your app config file to use EAS code signing'\n    );\n  }\n\n  if (expectedKeyId !== keyid) {\n    throw new CommandError(`keyid mismatch: client=${expectedKeyId}, project=${keyid}`);\n  }\n\n  if (expectedAlg && expectedAlg !== alg) {\n    throw new CommandError(`\"alg\" field mismatch (client=${expectedAlg}, project=${alg})`);\n  }\n\n  const { privateKeyPEM, certificatePEM } =\n    await getProjectPrivateKeyAndCertificateFromFilePathsAsync({\n      codeSigningCertificatePath,\n      privateKeyPath,\n    });\n\n  return {\n    keyId: keyid,\n    privateKey: privateKeyPEM,\n    certificateForPrivateKey: certificatePEM,\n    certificateChainForResponse: [],\n    scopeKey: null,\n  };\n}\n\nasync function readFileWithErrorAsync(path: string, errorMessage: string): Promise<string> {\n  try {\n    return await fs.readFile(path, 'utf8');\n  } catch {\n    throw new CommandError(errorMessage);\n  }\n}\n\nasync function getProjectPrivateKeyAndCertificateFromFilePathsAsync({\n  codeSigningCertificatePath,\n  privateKeyPath,\n}: {\n  codeSigningCertificatePath: string;\n  privateKeyPath: string;\n}): Promise<{ privateKeyPEM: string; certificatePEM: string }> {\n  const [codeSigningCertificatePEM, privateKeyPEM] = await Promise.all([\n    readFileWithErrorAsync(\n      codeSigningCertificatePath,\n      `Code signing certificate cannot be read from path: ${codeSigningCertificatePath}`\n    ),\n    readFileWithErrorAsync(\n      privateKeyPath,\n      `Code signing private key cannot be read from path: ${privateKeyPath}`\n    ),\n  ]);\n\n  const privateKey = convertPrivateKeyPEMToPrivateKey(privateKeyPEM);\n  const certificate = convertCertificatePEMToCertificate(codeSigningCertificatePEM);\n  validateSelfSignedCertificate(certificate, {\n    publicKey: certificate.publicKey as PKI.rsa.PublicKey,\n    privateKey,\n  });\n\n  return { privateKeyPEM, certificatePEM: codeSigningCertificatePEM };\n}\n\n/**\n * Validate that the cached code signing info is still valid for the current project and\n * that it hasn't expired. If invalid, return null.\n */\nfunction validateStoredDevelopmentExpoRootCertificateCodeSigningInfo(\n  codeSigningInfo: StoredDevelopmentExpoRootCodeSigningInfo,\n  easProjectId: string\n): CodeSigningInfo | null {\n  if (codeSigningInfo.easProjectId !== easProjectId) {\n    return null;\n  }\n\n  const {\n    privateKey: privateKeyPEM,\n    certificateChain: certificatePEMs,\n    scopeKey,\n  } = codeSigningInfo;\n  if (!privateKeyPEM || !certificatePEMs) {\n    return null;\n  }\n\n  const certificateChain = certificatePEMs.map((certificatePEM) =>\n    convertCertificatePEMToCertificate(certificatePEM)\n  );\n\n  // TODO(wschurman): maybe move to @expo/code-signing-certificates\n\n  // ensure all intermediate certificates are valid\n  for (const certificate of certificateChain) {\n    const now = new Date();\n    if (certificate.validity.notBefore > now || certificate.validity.notAfter < now) {\n      return null;\n    }\n  }\n\n  // TODO(wschurman): maybe do more validation, like validation of projectID and scopeKey within eas certificate extension\n\n  return {\n    keyId: 'expo-go',\n    certificateChainForResponse: certificatePEMs,\n    certificateForPrivateKey: certificatePEMs[0],\n    privateKey: privateKeyPEM,\n    scopeKey,\n  };\n}\n\nfunction actorCanGetProjectDevelopmentCertificate(actor: Actor, app: AppByIdQuery['app']['byId']) {\n  const owningAccountId = app.ownerAccount.id;\n\n  const owningAccountIsActorPrimaryAccount =\n    actor.__typename === 'User' || actor.__typename === 'SSOUser'\n      ? actor.primaryAccount.id === owningAccountId\n      : false;\n  const userHasPublishPermissionForOwningAccount = !!actor.accounts\n    .find((account) => account.id === owningAccountId)\n    ?.users?.find((userPermission) => userPermission.actor.id === actor.id)\n    ?.permissions?.includes(Permission.Publish);\n  return owningAccountIsActorPrimaryAccount || userHasPublishPermissionForOwningAccount;\n}\n\nasync function fetchAndCacheNewDevelopmentCodeSigningInfoAsync(\n  easProjectId: string\n): Promise<CodeSigningInfo | null> {\n  const actor = await ensureLoggedInAsync();\n  let app: AppByIdQuery['app']['byId'];\n  try {\n    app = await AppQuery.byIdAsync(easProjectId);\n  } catch (e) {\n    if (e instanceof GraphQLError || e instanceof CombinedError) {\n      return null;\n    }\n    throw e;\n  }\n  if (!actorCanGetProjectDevelopmentCertificate(actor, app)) {\n    return null;\n  }\n\n  const keyPair = generateKeyPair();\n  const keyPairPEM = convertKeyPairToPEM(keyPair);\n  const csr = generateCSR(keyPair, `Development Certificate for ${easProjectId}`);\n  const csrPEM = convertCSRToCSRPEM(csr);\n  const [developmentSigningCertificate, expoGoIntermediateCertificate] = await Promise.all([\n    getProjectDevelopmentCertificateAsync(easProjectId, csrPEM),\n    getExpoGoIntermediateCertificateAsync(easProjectId),\n  ]);\n\n  await DevelopmentCodeSigningInfoFile.setAsync(easProjectId, {\n    easProjectId,\n    scopeKey: app.scopeKey,\n    privateKey: keyPairPEM.privateKeyPEM,\n    certificateChain: [developmentSigningCertificate, expoGoIntermediateCertificate],\n  });\n\n  return {\n    keyId: 'expo-go',\n    certificateChainForResponse: [developmentSigningCertificate, expoGoIntermediateCertificate],\n    certificateForPrivateKey: developmentSigningCertificate,\n    privateKey: keyPairPEM.privateKeyPEM,\n    scopeKey: app.scopeKey,\n  };\n}\n/**\n * Generate the `expo-signature` header for a manifest and code signing info.\n */\nexport function signManifestString(\n  stringifiedManifest: string,\n  codeSigningInfo: CodeSigningInfo\n): string {\n  const privateKey = convertPrivateKeyPEMToPrivateKey(codeSigningInfo.privateKey);\n  const certificate = convertCertificatePEMToCertificate(codeSigningInfo.certificateForPrivateKey);\n  return signBufferRSASHA256AndVerify(\n    privateKey,\n    certificate,\n    Buffer.from(stringifiedManifest, 'utf8')\n  );\n}\n"], "names": ["DevelopmentCodeSigningInfoFile", "getCodeSigningInfoAsync", "getDevelopmentCodeSigningDirectory", "signManifestString", "debug", "require", "DEVELOPMENT_CODE_SIGNING_SETTINGS_FILE_NAME", "path", "join", "getExpoHomeDirectory", "getProjectDevelopmentCodeSigningInfoFile", "defaults", "getFile", "easProjectId", "filePath", "JsonFile", "readAsync", "projectSettings", "writeAsync", "ensureDir", "setAsync", "json", "mergeAsync", "cantReadFileDefault", "<PERSON><PERSON>ey", "privateKey", "certificate<PERSON>hain", "exp", "expectSignatureHeader", "privateKeyPath", "parsedExpectSignature", "parseDictionary", "CommandError", "expectedKeyIdOuter", "get", "expectedKeyId", "expectedAlg", "expectedAlgOuter", "expectedAlgTemp", "getExpoRootDevelopmentCodeSigningInfoAsync", "getProjectCodeSigningCertificateAsync", "extra", "eas", "projectId", "learnMore", "developmentCodeSigningInfoFromFile", "validatedCodeSigningInfo", "validateStoredDevelopmentExpoRootCertificateCodeSigningInfo", "env", "EXPO_OFFLINE", "fetchAndCacheNewDevelopmentCodeSigningInfoAsync", "e", "Log", "warn", "codeSigningCertificatePath", "updates", "codeSigningCertificate", "codeSigningMetadata", "alg", "keyid", "privateKeyPEM", "certificatePEM", "getProjectPrivateKeyAndCertificateFromFilePathsAsync", "keyId", "certificateForPrivateKey", "certificateChainForResponse", "readFileWithErrorAsync", "errorMessage", "fs", "readFile", "codeSigningCertificatePEM", "Promise", "all", "convertPrivateKeyPEMToPrivateKey", "certificate", "convertCertificatePEMToCertificate", "validateSelfSignedCertificate", "public<PERSON>ey", "codeSigningInfo", "certificatePEMs", "map", "now", "Date", "validity", "notBefore", "notAfter", "actorCanGetProjectDevelopmentCertificate", "actor", "app", "owningAccountId", "ownerAccount", "id", "owningAccountIsActorPrimaryAccount", "__typename", "primaryAccount", "userHasPublishPermissionForOwningAccount", "accounts", "find", "account", "users", "userPermission", "permissions", "includes", "Permission", "Publish", "ensureLoggedInAsync", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "byIdAsync", "GraphQLError", "CombinedError", "keyPair", "generateKeyPair", "keyPairPEM", "convertKeyPairToPEM", "csr", "generateCSR", "csrPEM", "convertCSRToCSRPEM", "developmentSigningCertificate", "expoGoIntermediateCertificate", "getProjectDevelopmentCertificateAsync", "getExpoGoIntermediateCertificateAsync", "stringifiedManifest", "signBufferRSASHA256AndVerify", "<PERSON><PERSON><PERSON>", "from"], "mappings": ";;;;;;;;;;;IA6GaA,8BAA8B;eAA9BA;;IAcSC,uBAAuB;eAAvBA;;IA9DNC,kCAAkC;eAAlCA;;IA+WAC,kBAAkB;eAAlBA;;;;yBA5aa;;;;;;;yBAUtB;;;;;;;gEAE8B;;;;;;;yBACP;;;;;;;yBACC;;;;;;;gEAEd;;;;;;;yBAC2B;;;;;;qBAExB;wBACS;kDACyB;kDACA;0BAC7B;8BACY;yBACD;2BAEK;6DACpB;sBACK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE1B,MAAMC,QAAQC,QAAQ,SAAS;AA4B/B,MAAMC,8CAA8C;AAE7C,SAASJ;IACd,OAAOK,eAAI,CAACC,IAAI,CAACC,IAAAA,kCAAoB,KAAI;AAC3C;AAEA,SAASC,yCAA+DC,QAAW;IACjF,SAASC,QAAQC,YAAoB;QACnC,MAAMC,WAAWP,eAAI,CAACC,IAAI,CACxBN,sCACAW,cACAP;QAEF,OAAO,IAAIS,CAAAA,WAAO,SAAC,CAAID;IACzB;IAEA,eAAeE,UAAUH,YAAoB;QAC3C,IAAII;QACJ,IAAI;YACFA,kBAAkB,MAAML,QAAQC,cAAcG,SAAS;QACzD,EAAE,OAAM;YACNC,kBAAkB,MAAML,QAAQC,cAAcK,UAAU,CAACP,UAAU;gBAAEQ,WAAW;YAAK;QACvF;QACA,sCAAsC;QACtC,OAAO;YAAE,GAAGR,QAAQ;YAAE,GAAGM,eAAe;QAAC;IAC3C;IAEA,eAAeG,SAASP,YAAoB,EAAEQ,IAAgB;QAC5D,IAAI;YACF,OAAO,MAAMT,QAAQC,cAAcS,UAAU,CAACD,MAAM;gBAClDE,qBAAqBZ;YACvB;QACF,EAAE,OAAM;YACN,OAAO,MAAMC,QAAQC,cAAcK,UAAU,CAC3C;gBACE,GAAGP,QAAQ;gBACX,GAAGU,IAAI;YACT,GACA;gBAAEF,WAAW;YAAK;QAEtB;IACF;IAEA,OAAO;QACLP;QACAI;QACAI;IACF;AACF;AAEO,MAAMpB,iCACXU,yCAAmF;IACjFG,cAAc;IACdW,UAAU;IACVC,YAAY;IACZC,kBAAkB;AACpB;AAQK,eAAezB,wBACpB0B,GAAe,EACfC,qBAAoC,EACpCC,cAAkC;IAElC,IAAI,CAACD,uBAAuB;QAC1B,OAAO;IACT;IAEA,IAAIE;IACJ,IAAI;QACFA,wBAAwBC,IAAAA,oCAAe,EAACH;IAC1C,EAAE,OAAM;QACN,MAAM,IAAII,oBAAY,CAAC;IACzB;IAEA,MAAMC,qBAAqBH,sBAAsBI,GAAG,CAAC;IACrD,IAAI,CAACD,oBAAoB;QACvB,MAAM,IAAID,oBAAY,CAAC;IACzB;IAEA,MAAMG,gBAAgBF,kBAAkB,CAAC,EAAE;IAC3C,IAAI,OAAOE,kBAAkB,UAAU;QACrC,MAAM,IAAIH,oBAAY,CACpB,CAAC,yDAAyD,EAAEG,eAAe;IAE/E;IAEA,IAAIC,cAA6B;IACjC,MAAMC,mBAAmBP,sBAAsBI,GAAG,CAAC;IACnD,IAAIG,kBAAkB;QACpB,MAAMC,kBAAkBD,gBAAgB,CAAC,EAAE;QAC3C,IAAI,OAAOC,oBAAoB,UAAU;YACvC,MAAM,IAAIN,oBAAY,CAAC;QACzB;QACAI,cAAcE;IAChB;IAEA,IAAIH,kBAAkB,aAAa;QACjC,OAAO,MAAMI,2CAA2CZ;IAC1D,OAAO,IAAIQ,kBAAkB,WAAW;QACtC,MAAM,IAAIH,oBAAY,CACpB;IAEJ,OAAO;QACL,OAAO,MAAMQ,sCACXb,KACAE,gBACAM,eACAC;IAEJ;AACF;AAEA;;;CAGC,GACD,eAAeG,2CACbZ,GAAe;QAEMA,gBAAAA;IAArB,MAAMd,gBAAec,aAAAA,IAAIc,KAAK,sBAATd,iBAAAA,WAAWe,GAAG,qBAAdf,eAAgBgB,SAAS;IAC9C,oHAAoH;IACpH,sEAAsE;IACtE,IAAI,CAAC9B,cAAc;QACjBT,MACE,CAAC,oKAAoK,EAAEwC,IAAAA,eAAS,EAC9K,+BACC;QAEL,OAAO;IACT;IAEA,MAAMC,qCACJ,MAAM7C,+BAA+BgB,SAAS,CAACH;IACjD,MAAMiC,2BAA2BC,4DAC/BF,oCACAhC;IAGF,6GAA6G;IAC7G,iFAAiF;IACjF,IAAI,CAACmC,QAAG,CAACC,YAAY,EAAE;QACrB,IAAI;YACF,OAAO,MAAMC,gDAAgDrC;QAC/D,EAAE,OAAOsC,GAAQ;YACf,IAAIL,0BAA0B;gBAC5BM,KAAIC,IAAI,CACN;gBAEF,OAAOP;YACT,OAAO;gBACL,6CAA6C;gBAC7C,MAAMK;YACR;QACF;IACF;IAEA,+JAA+J;IAC/J,IAAIL,0BAA0B;QAC5B,OAAOA;IACT;IAEA,6BAA6B;IAC7BM,KAAIC,IAAI,CAAC;IACT,OAAO;AACT;AAEA;;CAEC,GACD,eAAeb,sCACbb,GAAe,EACfE,cAAkC,EAClCM,aAAqB,EACrBC,WAA0B;QAEST,cAWPA;IAX5B,MAAM2B,8BAA6B3B,eAAAA,IAAI4B,OAAO,qBAAX5B,aAAa6B,sBAAsB;IACtE,IAAI,CAACF,4BAA4B;QAC/B,OAAO;IACT;IAEA,IAAI,CAACzB,gBAAgB;QACnB,MAAM,IAAIG,oBAAY,CACpB;IAEJ;IAEA,MAAMyB,uBAAsB9B,gBAAAA,IAAI4B,OAAO,qBAAX5B,cAAa8B,mBAAmB;IAC5D,IAAI,CAACA,qBAAqB;QACxB,MAAM,IAAIzB,oBAAY,CACpB;IAEJ;IAEA,MAAM,EAAE0B,GAAG,EAAEC,KAAK,EAAE,GAAGF;IACvB,IAAI,CAACC,OAAO,CAACC,OAAO;QAClB,MAAM,IAAI3B,oBAAY,CACpB;IAEJ;IAEA,IAAIG,kBAAkBwB,OAAO;QAC3B,MAAM,IAAI3B,oBAAY,CAAC,CAAC,uBAAuB,EAAEG,cAAc,UAAU,EAAEwB,OAAO;IACpF;IAEA,IAAIvB,eAAeA,gBAAgBsB,KAAK;QACtC,MAAM,IAAI1B,oBAAY,CAAC,CAAC,6BAA6B,EAAEI,YAAY,UAAU,EAAEsB,IAAI,CAAC,CAAC;IACvF;IAEA,MAAM,EAAEE,aAAa,EAAEC,cAAc,EAAE,GACrC,MAAMC,qDAAqD;QACzDR;QACAzB;IACF;IAEF,OAAO;QACLkC,OAAOJ;QACPlC,YAAYmC;QACZI,0BAA0BH;QAC1BI,6BAA6B,EAAE;QAC/BzC,UAAU;IACZ;AACF;AAEA,eAAe0C,uBAAuB3D,IAAY,EAAE4D,YAAoB;IACtE,IAAI;QACF,OAAO,MAAMC,cAAE,CAACC,QAAQ,CAAC9D,MAAM;IACjC,EAAE,OAAM;QACN,MAAM,IAAIyB,oBAAY,CAACmC;IACzB;AACF;AAEA,eAAeL,qDAAqD,EAClER,0BAA0B,EAC1BzB,cAAc,EAIf;IACC,MAAM,CAACyC,2BAA2BV,cAAc,GAAG,MAAMW,QAAQC,GAAG,CAAC;QACnEN,uBACEZ,4BACA,CAAC,mDAAmD,EAAEA,4BAA4B;QAEpFY,uBACErC,gBACA,CAAC,mDAAmD,EAAEA,gBAAgB;KAEzE;IAED,MAAMJ,aAAagD,IAAAA,2DAAgC,EAACb;IACpD,MAAMc,cAAcC,IAAAA,6DAAkC,EAACL;IACvDM,IAAAA,wDAA6B,EAACF,aAAa;QACzCG,WAAWH,YAAYG,SAAS;QAChCpD;IACF;IAEA,OAAO;QAAEmC;QAAeC,gBAAgBS;IAA0B;AACpE;AAEA;;;CAGC,GACD,SAASvB,4DACP+B,eAAyD,EACzDjE,YAAoB;IAEpB,IAAIiE,gBAAgBjE,YAAY,KAAKA,cAAc;QACjD,OAAO;IACT;IAEA,MAAM,EACJY,YAAYmC,aAAa,EACzBlC,kBAAkBqD,eAAe,EACjCvD,QAAQ,EACT,GAAGsD;IACJ,IAAI,CAAClB,iBAAiB,CAACmB,iBAAiB;QACtC,OAAO;IACT;IAEA,MAAMrD,mBAAmBqD,gBAAgBC,GAAG,CAAC,CAACnB,iBAC5Cc,IAAAA,6DAAkC,EAACd;IAGrC,iEAAiE;IAEjE,iDAAiD;IACjD,KAAK,MAAMa,eAAehD,iBAAkB;QAC1C,MAAMuD,MAAM,IAAIC;QAChB,IAAIR,YAAYS,QAAQ,CAACC,SAAS,GAAGH,OAAOP,YAAYS,QAAQ,CAACE,QAAQ,GAAGJ,KAAK;YAC/E,OAAO;QACT;IACF;IAEA,wHAAwH;IAExH,OAAO;QACLlB,OAAO;QACPE,6BAA6Bc;QAC7Bf,0BAA0Be,eAAe,CAAC,EAAE;QAC5CtD,YAAYmC;QACZpC;IACF;AACF;AAEA,SAAS8D,yCAAyCC,KAAY,EAAEC,GAAgC;QAO3CD,6CAAAA,iCAAAA,4BAAAA;IANnD,MAAME,kBAAkBD,IAAIE,YAAY,CAACC,EAAE;IAE3C,MAAMC,qCACJL,MAAMM,UAAU,KAAK,UAAUN,MAAMM,UAAU,KAAK,YAChDN,MAAMO,cAAc,CAACH,EAAE,KAAKF,kBAC5B;IACN,MAAMM,2CAA2C,CAAC,GAACR,uBAAAA,MAAMS,QAAQ,CAC9DC,IAAI,CAAC,CAACC,UAAYA,QAAQP,EAAE,KAAKF,sCADeF,6BAAAA,qBAE/CY,KAAK,sBAF0CZ,kCAAAA,2BAExCU,IAAI,CAAC,CAACG,iBAAmBA,eAAeb,KAAK,CAACI,EAAE,KAAKJ,MAAMI,EAAE,uBAFrBJ,8CAAAA,gCAG/Cc,WAAW,qBAHoCd,4CAGlCe,QAAQ,CAACC,qBAAU,CAACC,OAAO;IAC5C,OAAOZ,sCAAsCG;AAC/C;AAEA,eAAe7C,gDACbrC,YAAoB;IAEpB,MAAM0E,QAAQ,MAAMkB,IAAAA,4BAAmB;IACvC,IAAIjB;IACJ,IAAI;QACFA,MAAM,MAAMkB,kBAAQ,CAACC,SAAS,CAAC9F;IACjC,EAAE,OAAOsC,GAAG;QACV,IAAIA,aAAayD,0BAAY,IAAIzD,aAAa0D,qBAAa,EAAE;YAC3D,OAAO;QACT;QACA,MAAM1D;IACR;IACA,IAAI,CAACmC,yCAAyCC,OAAOC,MAAM;QACzD,OAAO;IACT;IAEA,MAAMsB,UAAUC,IAAAA,0CAAe;IAC/B,MAAMC,aAAaC,IAAAA,8CAAmB,EAACH;IACvC,MAAMI,MAAMC,IAAAA,sCAAW,EAACL,SAAS,CAAC,4BAA4B,EAAEjG,cAAc;IAC9E,MAAMuG,SAASC,IAAAA,6CAAkB,EAACH;IAClC,MAAM,CAACI,+BAA+BC,8BAA8B,GAAG,MAAMhD,QAAQC,GAAG,CAAC;QACvFgD,IAAAA,uEAAqC,EAAC3G,cAAcuG;QACpDK,IAAAA,uEAAqC,EAAC5G;KACvC;IAED,MAAMb,+BAA+BoB,QAAQ,CAACP,cAAc;QAC1DA;QACAW,UAAUgE,IAAIhE,QAAQ;QACtBC,YAAYuF,WAAWpD,aAAa;QACpClC,kBAAkB;YAAC4F;YAA+BC;SAA8B;IAClF;IAEA,OAAO;QACLxD,OAAO;QACPE,6BAA6B;YAACqD;YAA+BC;SAA8B;QAC3FvD,0BAA0BsD;QAC1B7F,YAAYuF,WAAWpD,aAAa;QACpCpC,UAAUgE,IAAIhE,QAAQ;IACxB;AACF;AAIO,SAASrB,mBACduH,mBAA2B,EAC3B5C,eAAgC;IAEhC,MAAMrD,aAAagD,IAAAA,2DAAgC,EAACK,gBAAgBrD,UAAU;IAC9E,MAAMiD,cAAcC,IAAAA,6DAAkC,EAACG,gBAAgBd,wBAAwB;IAC/F,OAAO2D,IAAAA,uDAA4B,EACjClG,YACAiD,aACAkD,OAAOC,IAAI,CAACH,qBAAqB;AAErC"}
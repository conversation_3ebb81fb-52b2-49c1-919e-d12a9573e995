import { useAuth, useClerk } from '@clerk/clerk-expo'
import { Redirect } from 'expo-router'
import React from 'react'
import { Button, Text, View } from 'react-native'

const HomeScreen = () => {
    const { isSignedIn } = useAuth()
    const { signOut } = useClerk()

    // If user is not signed in, redirect to auth
    if (!isSignedIn) {
        return <Redirect href="/(auth)" />
    }

    return (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
            <Text style={{ fontSize: 20, marginBottom: 20 }}>Welcome! You're signed in.</Text>
            <Button onPress={() => signOut()} title="Sign Out" />
        </View>
    )
}

export default HomeScreen
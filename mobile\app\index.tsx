import { useAuth } from '@clerk/clerk-expo';
import { Redirect } from 'expo-router';
import { ActivityIndicator, View } from 'react-native';

export default function RootIndex() {
    const { isLoaded, isSignedIn } = useAuth();

    // Show loading spinner while <PERSON> is initializing
    if (!isLoaded) {
        return (
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ActivityIndicator size="large" color="#007AFF" />
            </View>
        );
    }

    // Redirect based on authentication status
    if (isSignedIn) {
        return <Redirect href="/(tabs)" />;
    } else {
        return <Redirect href="/(auth)" />;
    }
}
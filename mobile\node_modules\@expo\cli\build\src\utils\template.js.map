{"version": 3, "sources": ["../../../src/utils/template.ts"], "sourcesContent": ["/**\n * Simple unsafe interpolation for template strings. Does NOT escape values.\n *\n * Arguments can be named or numeric.\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Template_literals#tagged_templates\n *\n * @example\n * const t1Closure = unsafeTemplate`${0}${1}${0}!`;\n * // const t1Closure = unsafeTemplate([\"\",\"\",\"\",\"!\"],0,1,0);\n * t1Closure(\"Y\", \"A\"); // \"YAY!\"\n *\n * @example\n * const t2Closure = unsafeTemplate`${0} ${\"foo\"}!`;\n * // const t2Closure = unsafeTemplate([\"\",\" \",\"!\"],0,\"foo\");\n * t2Closure(\"Hello\", { foo: \"World\" }); // \"Hello World!\"\n *\n * @example\n * const t3Closure = unsafeTemplate`I'm ${\"name\"}. I'm almost ${\"age\"} years old.`;\n * // const t3Closure = unsafeTemplate([\"I'm \", \". I'm almost \", \" years old.\"], \"name\", \"age\");\n * t3Closure(\"foo\", { name: \"MDN\", age: 30 }); // \"I'm MDN. I'm almost 30 years old.\"\n * t3Closure({ name: \"MDN\", age: 30 }); // \"I'm MDN. I'm almost 30 years old.\"\n */\nexport function unsafeTemplate(strings: TemplateStringsArray, ...keys: (string | number)[]) {\n  return (\n    ...values: (string | number)[] | [...(string | number)[], Record<string | number, string>]\n  ) => {\n    const lastValue = values[values.length - 1];\n    const dict = typeof lastValue === 'object' ? lastValue : {};\n    const result = [strings[0]];\n    keys.forEach((key, i) => {\n      const value = typeof key === 'number' && Number.isInteger(key) ? values[key] : dict[key];\n      result.push(value as string, strings[i + 1]);\n    });\n    return result.join('');\n  };\n}\n"], "names": ["unsafeTemplate", "strings", "keys", "values", "lastValue", "length", "dict", "result", "for<PERSON>ach", "key", "i", "value", "Number", "isInteger", "push", "join"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;CAsBC;;;;+BACeA;;;eAAAA;;;AAAT,SAASA,eAAeC,OAA6B,EAAE,GAAGC,IAAyB;IACxF,OAAO,CACL,GAAGC;QAEH,MAAMC,YAAYD,MAAM,CAACA,OAAOE,MAAM,GAAG,EAAE;QAC3C,MAAMC,OAAO,OAAOF,cAAc,WAAWA,YAAY,CAAC;QAC1D,MAAMG,SAAS;YAACN,OAAO,CAAC,EAAE;SAAC;QAC3BC,KAAKM,OAAO,CAAC,CAACC,KAAKC;YACjB,MAAMC,QAAQ,OAAOF,QAAQ,YAAYG,OAAOC,SAAS,CAACJ,OAAON,MAAM,CAACM,IAAI,GAAGH,IAAI,CAACG,IAAI;YACxFF,OAAOO,IAAI,CAACH,OAAiBV,OAAO,CAACS,IAAI,EAAE;QAC7C;QACA,OAAOH,OAAOQ,IAAI,CAAC;IACrB;AACF"}